{% extends "vertical_base.html" %}
{% load static %}

{% block title %}Hints{% endblock title %}

{% block extra_css %}

<link href="{% static 'css/thoth/datatables.min.css' %}" rel="stylesheet"  type="text/css"/>
<link href="{% static 'css/vendor/responsive.bootstrap5.min.css' %}" rel="stylesheet" type="text/css" />

{% endblock %}

{% block page_title %}
{% include "partials/page-title.html" with page_title='Hints' sub_title='Hints & Context' %}
{% endblock %}

{% block content %}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-sm-5">
                        {% if user_is_editor %}
                        <a href="{% url 'thoth_ai_backend:create_hint' %}" class="btn btn-danger mb-2"><i
                                class="mdi mdi-plus-circle me-2"></i>Add Hint</a>
                        {% endif %}
                    </div>
                    <div class="col-sm-7">
                        <div class="text-sm-end">
                            {% if user_is_editor %}
                            <form method="POST" action="{% url 'thoth_ai_backend:delete_all_hints' %}" style="display: inline-block; margin-right: 5px;" onsubmit="return confirm('Are you sure you want to delete ALL hints? This action cannot be undone.');">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger mb-2"><i class="mdi mdi-delete-sweep me-2"></i> Delete All Hints</button>
                            </form>
                            <form method="POST" action="{% url 'thoth_ai_backend:import_hints_server_csv' %}" style="display: inline-block; margin-right: 5px;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-info mb-2">Import Server CSV</button>
                            </form>
                            {% endif %}
                            <a href="{% url 'thoth_ai_backend:export_hints_csv' %}" class="btn btn-light mb-2">Export CSV</a>
                        </div>
                    </div><!-- end col-->
                </div>

                <div class="table-responsive">
                    <table id="hints-datatable" class="table dt-responsive nowrap w-100 table-striped">
                        <thead>
                            <tr>
                                <th>Hint</th>
                                {% if user_is_editor %}
                                <th class="action-column"  style="width: 85px;">Action</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for hint_item in hints %}
                            <tr>
                                <td>{{ hint_item.hint }}</td>
                                {% if user_is_editor %}
                                <td class="table-action">
                                    {# Link the edit icon to the update_hint URL with the hint's ID #}
                                    <a href="{% url 'thoth_ai_backend:update_hint' hint_id=hint_item.id %}" class="action-icon"> <i
                                            class="mdi mdi-square-edit-outline"></i></a>
                                    {# Link the delete icon to the confirmation view #}
                                    <a href="{% url 'thoth_ai_backend:confirm_delete_hint' hint_id=hint_item.id %}" class="action-icon"> <i
                                            class="mdi mdi-delete"></i></a>
                                </td>
                                {% endif %}
                            </tr>
                            {% empty %}
                            <tr>
                                <td>
                                    {% if error %}
                                        <div class="alert alert-error">
                                            {{ error }}
                                        </div>
                                    {% else %}
                                        No hints found.
                                    {% endif %}
                                </td>
                                <td></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                 </div> <!-- end table-responsive-->
            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col -->
</div>
<!-- end row -->

{% endblock %}

{% block extra_javascript %}

<!-- Third party js -->
<script src = "{% static 'js/thoth/datatables.min.js' %}"></script>
<!-- Third party js ends -->

<!-- Init js -->
<script src="{% static 'js/thoth/vdb.hints.js' %}"></script>
<!-- Init js end -->

{% endblock %}
