# Generated by Django 5.2 on 2025-07-20 14:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('thoth_core', '0002_add_vectordb_path_field'),
    ]

    operations = [
        migrations.AlterField(
            model_name='basicaimodel',
            name='provider',
            field=models.CharField(choices=[('OPENAI', 'OpenAI'), ('ANTHROPIC', 'Anthropic'), ('CODESTRAL', 'Codestral'), ('DEEPSEEK', 'DeepSeek'), ('META', 'LLama'), ('LMSTUDIO', 'LM Studio'), ('MISTRAL', 'Mistral'), ('OLLAMA', 'Ollama'), ('OPENROUTER', 'OpenRouter'), ('GEMINI', 'Gemini')], default='ANTHROPIC', max_length=100),
        ),
        migrations.AlterField(
            model_name='vectordb',
            name='name',
            field=models.Char<PERSON><PERSON>(max_length=255, verbose_name='Collection Name'),
        ),
    ]
