# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import os
from django.conf import settings
from django.contrib import messages
from haystack import Pipeline
from haystack.components.builders import ChatPromptBuilder
from haystack.dataclasses import ChatMessage
from thoth_core.models import SqlDb, SqlTable, SqlColumn, Setting, LLMChoices
from thoth_core.thoth_ai.thoth_workflow.comment_generation_utils import setup_default_comment_llm_model

def generate_scope(modeladmin, request, queryset):
    """
    Generates a scope description for each selected SqlDb instance using an AI model.
    """
    try:
        if not hasattr(request, 'current_workspace') or not request.current_workspace:
            modeladmin.message_user(request, "No active workspace found. Please select a workspace.", level=messages.ERROR)
            return
        
        setting = request.current_workspace.setting
        if not setting or not setting.comment_model:
            modeladmin.message_user(request, "AI model for comment generation not configured in settings.", messages.ERROR)
            return

        llm = setup_default_comment_llm_model(setting)
        if llm is None:
            modeladmin.message_user(request, "Failed to set up LLM model.", messages.ERROR)
            return

        template_path = os.path.join(
            settings.BASE_DIR,
            'thoth_core',
            'thoth_ai',
            'thoth_workflow',
            'prompt_templates',
            'generate_scope_prompt.txt'
        )

        with open(template_path, 'r') as file:
            prompt_template_text = file.read()

        if setting.comment_model.basic_model.provider==LLMChoices.GEMINI:
            template=[ChatMessage.from_user(prompt_template_text)]
        else:
            template = [ChatMessage.from_user(prompt_template_text),
                        ChatMessage.from_system('You are an expert in relational database management, SQL and database semantics. You will be given a prompt related to database management.')]

        builder = ChatPromptBuilder(
            template=template,
        )
        
        pipeline = Pipeline()
        pipeline.add_component("builder", builder)
        pipeline.add_component("llm", llm)
        pipeline.connect("builder", "llm")

        for db in queryset:
            tables = SqlTable.objects.filter(sql_db=db)
            tables_data = []
            for table in tables:
                columns = SqlColumn.objects.filter(sql_table=table)
                columns_data = [{
                    'original_name': col.original_column_name,
                    'description': col.column_description,
                    'value_description': col.value_description
                } for col in columns]
                tables_data.append({
                    'name': table.name,
                    'description': table.description,
                    'columns': columns_data
                })

            try:
                language = db.language if db.language else setting.language
                output = pipeline.run(data={
                    "builder": {
                        "db_name": db.name,
                        "tables": tables_data,
                        "language": language
                    }
                })

                if output and 'llm' in output and output['llm']['replies']:
                    generated_scope = output['llm']['replies'][0].text
                    db.scope = generated_scope
                    db.save()
                    modeladmin.message_user(request, f"Successfully generated scope for database '{db.name}'.", messages.SUCCESS)
                else:
                    modeladmin.message_user(request, f"AI did not return a scope for database '{db.name}'.", messages.WARNING)

            except Exception as e:
                modeladmin.message_user(request, f"Error generating scope for database '{db.name}': {str(e)}", messages.ERROR)

    except FileNotFoundError:
        modeladmin.message_user(request, "Prompt template file not found.", messages.ERROR)
    except Exception as e:
        modeladmin.message_user(request, f"An unexpected error occurred: {str(e)}", messages.ERROR)

generate_scope.short_description = "Generate scope (AI assisted)"
