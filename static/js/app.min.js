!function(){var e=sessionStorage.getItem("__HYPER_CONFIG__"),t=document.getElementsByTagName("html")[0],n={theme:"light",nav:"vertical",layout:{mode:"fluid",position:"fixed"},topbar:{color:"light"},menu:{color:"dark"},sidenav:{size:"default",user:!1}},a=(this.html=document.getElementsByTagName("html")[0],config=Object.assign(JSON.parse(JSON.stringify(n)),{}),this.html.getAttribute("data-bs-theme")),a=(config.theme=null!==a?a:n.theme,this.html.getAttribute("data-layout")),a=(config.nav=null!==a?"topnav"===a?"horizontal":"vertical":n.nav,this.html.getAttribute("data-layout-mode")),a=(config.layout.mode=null!==a?a:n.layout.mode,this.html.getAttribute("data-layout-position")),a=(config.layout.position=null!==a?a:n.layout.position,this.html.getAttribute("data-topbar-color")),a=(config.topbar.color=null!=a?a:n.topbar.color,this.html.getAttribute("data-sidenav-size")),a=(config.sidenav.size=null!==a?a:n.sidenav.size,this.html.getAttribute("data-sidenav-user")),a=(config.sidenav.user=null!==a||n.sidenav.user,this.html.getAttribute("data-menu-color"));if(config.menu.color=null!==a?a:n.menu.color,window.defaultConfig=JSON.parse(JSON.stringify(config)),null!==e&&(config=JSON.parse(e)),window.config=config,"topnav"===t.getAttribute("data-layout")?config.nav="horizontal":config.nav="vertical",config&&(t.setAttribute("data-bs-theme",config.theme),t.setAttribute("data-layout-mode",config.layout.mode),t.setAttribute("data-menu-color",config.menu.color),t.setAttribute("data-topbar-color",config.topbar.color),t.setAttribute("data-layout-position",config.layout.position),"vertical"==config.nav)){let e=config.sidenav.size;window.innerWidth<=767?e="full":767<=window.innerWidth&&window.innerWidth<=1140&&"full"!==self.config.sidenav.size&&"fullscreen"!==self.config.sidenav.size&&(e="condensed"),t.setAttribute("data-sidenav-size",e),config.sidenav.user&&"true"===config.sidenav.user.toString()?t.setAttribute("data-sidenav-user",!0):t.removeAttribute("data-sidenav-user")}}();class ThemeCustomizer{constructor(){this.html=document.getElementsByTagName("html")[0],this.config={},this.defaultConfig=window.config}initConfig(){this.defaultConfig=JSON.parse(JSON.stringify(window.defaultConfig)),this.config=JSON.parse(JSON.stringify(window.config)),this.setSwitchFromConfig()}changeMenuColor(e){this.config.menu.color=e,this.html.setAttribute("data-menu-color",e),this.setSwitchFromConfig()}changeLeftbarSize(e,t=!0){this.html.setAttribute("data-sidenav-size",e),t&&(this.config.sidenav.size=e,this.setSwitchFromConfig())}changeLayoutMode(e,t=!0){this.html.setAttribute("data-layout-mode",e),t&&(this.config.layout.mode=e,this.setSwitchFromConfig())}changeLayoutPosition(e){this.config.layout.position=e,this.html.setAttribute("data-layout-position",e),this.setSwitchFromConfig()}changeLayoutColor(e){this.config.theme=e,this.html.setAttribute("data-bs-theme",e),this.setSwitchFromConfig()}changeTopbarColor(e){this.config.topbar.color=e,this.html.setAttribute("data-topbar-color",e),this.setSwitchFromConfig()}changeSidebarUser(e){(this.config.sidenav.user=e)?this.html.setAttribute("data-sidenav-user",e):this.html.removeAttribute("data-sidenav-user"),this.setSwitchFromConfig()}resetTheme(){this.config=JSON.parse(JSON.stringify(window.defaultConfig)),this.changeMenuColor(this.config.menu.color),this.changeLeftbarSize(this.config.sidenav.size),this.changeLayoutColor(this.config.theme),this.changeLayoutMode(this.config.layout.mode),this.changeLayoutPosition(this.config.layout.position),this.changeTopbarColor(this.config.topbar.color),this.changeSidebarUser(this.config.sidenav.user),this._adjustLayout()}initSwitchListener(){var n=this,e=(document.querySelectorAll("input[name=data-menu-color]").forEach(function(t){t.addEventListener("change",function(e){n.changeMenuColor(t.value)})}),document.querySelectorAll("input[name=data-sidenav-size]").forEach(function(t){t.addEventListener("change",function(e){n.changeLeftbarSize(t.value)})}),document.querySelectorAll("input[name=data-bs-theme]").forEach(function(t){t.addEventListener("change",function(e){n.changeLayoutColor(t.value)})}),document.querySelectorAll("input[name=data-layout-mode]").forEach(function(t){t.addEventListener("change",function(e){n.changeLayoutMode(t.value)})}),document.querySelectorAll("input[name=data-layout-position]").forEach(function(t){t.addEventListener("change",function(e){n.changeLayoutPosition(t.value)})}),document.querySelectorAll("input[name=data-layout]").forEach(function(t){t.addEventListener("change",function(e){window.location="horizontal"===t.value?"layouts-horizontal.html":"index.html"})}),document.querySelectorAll("input[name=data-topbar-color]").forEach(function(t){t.addEventListener("change",function(e){n.changeTopbarColor(t.value)})}),document.querySelectorAll("input[name=sidebar-user]").forEach(function(t){t.addEventListener("change",function(e){n.changeSidebarUser(t.checked)})}),document.getElementById("light-dark-mode")),e=(e&&e.addEventListener("click",function(e){"light"===n.config.theme?n.changeLayoutColor("dark"):n.changeLayoutColor("light")}),document.querySelector("#reset-layout")),e=(e&&e.addEventListener("click",function(e){n.resetTheme()}),document.querySelector(".button-toggle-menu")),e=(e&&e.addEventListener("click",function(){var e=n.config.sidenav.size,t=n.html.getAttribute("data-sidenav-size",e);"full"===t?n.showBackdrop():"fullscreen"==e?"fullscreen"===t?n.changeLeftbarSize("fullscreen"==e?"default":e,!1):n.changeLeftbarSize("fullscreen",!1):"condensed"===t?n.changeLeftbarSize("condensed"==e?"default":e,!1):n.changeLeftbarSize("condensed",!1),n.html.classList.toggle("sidebar-enable")}),document.querySelector(".button-close-fullsidebar"));e&&e.addEventListener("click",function(){n.html.classList.remove("sidebar-enable"),n.hideBackdrop()}),document.querySelectorAll(".button-sm-hover").forEach(function(e){e.addEventListener("click",function(){var e=n.config.sidenav.size;"sm-hover-active"===n.html.getAttribute("data-sidenav-size",e)?n.changeLeftbarSize("sm-hover",!1):n.changeLeftbarSize("sm-hover-active",!1)})})}showBackdrop(){var e=document.createElement("div");e.id="custom-backdrop",e.classList="offcanvas-backdrop fade show",document.body.appendChild(e),document.body.style.overflow="hidden",767<window.innerWidth&&(document.body.style.paddingRight="15px");let t=this;e.addEventListener("click",function(e){t.html.classList.remove("sidebar-enable"),t.hideBackdrop()})}hideBackdrop(){var e=document.getElementById("custom-backdrop");e&&(document.body.removeChild(e),document.body.style.overflow=null,document.body.style.paddingRight=null)}initWindowSize(){var t=this;window.addEventListener("resize",function(e){t._adjustLayout()})}_adjustLayout(){var e=this;window.innerWidth<=767.98?e.changeLeftbarSize("full",!1):767<=window.innerWidth&&window.innerWidth<=1140?"full"!==e.config.sidenav.size&&"fullscreen"!==e.config.sidenav.size&&("sm-hover"===e.config.sidenav.size?e.changeLeftbarSize("condensed"):e.changeLeftbarSize("condensed",!1)):(e.changeLeftbarSize(e.config.sidenav.size),e.changeLayoutMode(e.config.layout.mode))}setSwitchFromConfig(){sessionStorage.setItem("__HYPER_CONFIG__",JSON.stringify(this.config)),document.querySelectorAll(".right-bar input[type=checkbox]").forEach(function(e){e.checked=!1});var e,t,n,a,o,i,s,r,c=this.config;c&&(e=document.querySelector("input[type=radio][name=data-layout][value="+c.nav+"]"),t=document.querySelector("input[type=radio][name=data-bs-theme][value="+c.theme+"]"),n=document.querySelector("input[type=radio][name=data-layout-mode][value="+c.layout.mode+"]"),a=document.querySelector("input[type=radio][name=data-topbar-color][value="+c.topbar.color+"]"),o=document.querySelector("input[type=radio][name=data-menu-color][value="+c.menu.color+"]"),i=document.querySelector("input[type=radio][name=data-sidenav-size][value="+c.sidenav.size+"]"),s=document.querySelector("input[type=radio][name=data-layout-position][value="+c.layout.position+"]"),r=document.querySelector("input[type=checkbox][name=sidebar-user]"),e&&(e.checked=!0),t&&(t.checked=!0),n&&(n.checked=!0),a&&(a.checked=!0),o&&(o.checked=!0),i&&(i.checked=!0),s&&(s.checked=!0),r)&&"true"===c.sidenav.user.toString()&&(r.checked=!0)}init(){this.initConfig(),this.initSwitchListener(),this.initWindowSize(),this._adjustLayout(),this.setSwitchFromConfig()}}(new ThemeCustomizer).init(),(o=>{function e(){o(window).on("load",function(){o("#status").fadeOut(),o("#preloader").delay(350).fadeOut("slow")}),lucide.createIcons();[...document.querySelectorAll('[data-bs-toggle="popover"]')].map(e=>new bootstrap.Popover(e)),[...document.querySelectorAll('[data-bs-toggle="tooltip"]')].map(e=>new bootstrap.Tooltip(e)),[...document.querySelectorAll(".offcanvas")].map(e=>new bootstrap.Offcanvas(e));var e=document.getElementById("toastPlacement");e&&document.getElementById("selectToastPlacement").addEventListener("change",function(){e.dataset.originalClass||(e.dataset.originalClass=e.className),e.className=e.dataset.originalClass+" "+this.value});[].slice.call(document.querySelectorAll(".toast")).map(function(e){return new bootstrap.Toast(e)});let a=document.getElementById("liveAlertPlaceholder");var t=document.getElementById("liveAlertBtn");t&&t.addEventListener("click",()=>{var e,t,n;e="Nice, you triggered this alert message!",t="success",(n=document.createElement("div")).innerHTML=[`<div class="alert alert-${t} alert-dismissible" role="alert">`,`   <div>${e}</div>`,'   <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>',"</div>"].join(""),a.append(n)}),document.getElementById("app-style").href.includes("rtl.min.css")&&(document.getElementsByTagName("html")[0].dir="rtl")}function t(){var e;o(".side-nav").length&&(e=o(".side-nav li .collapse"),o(".side-nav li [data-bs-toggle='collapse']").on("click",function(e){return!1}),e.on({"show.bs.collapse":function(e){var t=o(e.target).parents(".collapse.show");o(".side-nav .collapse.show").not(e.target).not(t).collapse("hide")}}),o(".side-nav a").each(function(){var e=window.location.href.split(/[?#]/)[0];this.href==e&&(o(this).addClass("active"),o(this).parent().addClass("menuitem-active"),o(this).parent().parent().parent().addClass("show"),o(this).parent().parent().parent().parent().addClass("menuitem-active"),"sidebar-menu"!==(e=o(this).parent().parent().parent().parent().parent().parent()).attr("id")&&e.addClass("show"),o(this).parent().parent().parent().parent().parent().parent().parent().addClass("menuitem-active"),"wrapper"!==(e=o(this).parent().parent().parent().parent().parent().parent().parent().parent().parent()).attr("id")&&e.addClass("show"),(e=o(this).parent().parent().parent().parent().parent().parent().parent().parent().parent().parent()).is("body")||e.addClass("menuitem-active"))}),setTimeout(function(){var e,a,o,i,s,r,t=document.querySelector("li.menuitem-active .active");function c(){e=r+=20,t=i,n=s;var e,t,n=(e/=o/2)<1?n/2*e*e+t:-n/2*(--e*(e-2)-1)+t;a.scrollTop=n,r<o&&setTimeout(c,20)}null!=t&&(e=document.querySelector(".leftside-menu .simplebar-content-wrapper"),t=t.offsetTop-300,e)&&100<t&&(o=600,i=(a=e).scrollTop,s=t-i,r=0,c())},200))}var n,a,i,s;e(),o(document).on("click",'.card a[data-bs-toggle="remove"]',function(e){e.preventDefault();var e=o(this).closest(".card"),t=e.parent();e.remove(),0==t.children().length&&t.remove()}),o(document).on("click",'.card a[data-bs-toggle="reload"]',function(e){e.preventDefault();var e=o(this).closest(".card"),t=(e.append('<div class="card-disabled"><div class="card-portlets-loader"></div></div>'),e.find(".card-disabled"));setTimeout(function(){t.fadeOut("fast",function(){t.remove()})},500+5*Math.random()*300)}),o(".dropdown-menu a.dropdown-toggle").on("click",function(){var e=o(this).next(".dropdown-menu"),e=o(this).parent().parent().find(".dropdown-menu").not(e);return e.removeClass("show"),e.parent().find(".dropdown-toggle").removeClass("show"),!1}),t(),o(".navbar-nav").length&&(o(".navbar-nav li a").each(function(){var e=window.location.href.split(/[?#]/)[0];this.href==e&&(o(this).addClass("active"),o(this).parent().parent().addClass("active"),o(this).parent().parent().parent().parent().addClass("active"),o(this).parent().parent().parent().parent().parent().parent().addClass("active"))}),o(".navbar-toggle").on("click",function(){o(this).toggleClass("open"),o("#navigation").slideToggle(400)})),n=o(".navbar-custom .dropdown:not(.app-search)"),o(document).on("click",function(e){return"top-search"==e.target.id||e.target.closest("#search-dropdown")?o("#search-dropdown").addClass("d-block"):o("#search-dropdown").removeClass("d-block"),!0}),o("#top-search").on("focus",function(e){return e.preventDefault(),n.children(".dropdown-menu.show").removeClass("show"),o("#search-dropdown").addClass("d-block"),!1}),n.on("show.bs.dropdown",function(){o("#search-dropdown").removeClass("d-block")}),(a=document.querySelector('[data-toggle="fullscreen"]'))&&a.addEventListener("click",function(e){e.preventDefault(),document.body.classList.toggle("fullscreen-enable"),document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement?document.cancelFullScreen?document.cancelFullScreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen&&document.webkitCancelFullScreen():document.documentElement.requestFullscreen?document.documentElement.requestFullscreen():document.documentElement.mozRequestFullScreen?document.documentElement.mozRequestFullScreen():document.documentElement.webkitRequestFullscreen&&document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)}),o("[data-password]").on("click",function(){"false"==o(this).attr("data-password")?(o(this).siblings("input").attr("type","text"),o(this).attr("data-password","true"),o(this).addClass("show-password")):(o(this).siblings("input").attr("type","password"),o(this).attr("data-password","false"),o(this).removeClass("show-password"))}),document.querySelectorAll(".needs-validation").forEach(t=>{t.addEventListener("submit",e=>{t.checkValidity()||(e.preventDefault(),e.stopPropagation()),t.classList.add("was-validated")},!1)}),jQuery().select2&&o('[data-toggle="select2"]').select2(),jQuery().mask&&o('[data-toggle="input-mask"]').each(function(e,t){var n=o(t).data("maskFormat"),a=o(t).data("reverse");null!=a?o(t).mask(n,{reverse:a}):o(t).mask(n)}),jQuery().daterangepicker&&(i={startDate:moment().subtract(29,"days"),endDate:moment(),ranges:{Today:[moment(),moment()],Yesterday:[moment().subtract(1,"days"),moment().subtract(1,"days")],"Last 7 Days":[moment().subtract(6,"days"),moment()],"Last 30 Days":[moment().subtract(29,"days"),moment()],"This Month":[moment().startOf("month"),moment().endOf("month")],"Last Month":[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]}},o('[data-toggle="date-picker-range"]').each(function(e,t){var n=o.extend({},i,o(t).data()),a=n.targetDisplay;o(t).daterangepicker(n,function(e,t){a&&o(a).html(e.format("MMMM D, YYYY")+" - "+t.format("MMMM D, YYYY"))})}),s={cancelClass:"btn-light",applyButtonClasses:"btn-success"},o('[data-toggle="date-picker"]').each(function(e,t){var n=o.extend({},s,o(t).data());o(t).daterangepicker(n)})),jQuery().timepicker&&(s={showSeconds:!0,icons:{up:"mdi mdi-chevron-up",down:"mdi mdi-chevron-down"}},o('[data-toggle="timepicker"]').each(function(e,t){var n=o.extend({},s,o(t).data());o(t).timepicker(n)})),jQuery().TouchSpin&&(s={},o('[data-toggle="touchspin"]').each(function(e,t){var n=o.extend({},s,o(t).data());o(t).TouchSpin(n)})),jQuery().maxlength&&(s={warningClass:"badge bg-success",limitReachedClass:"badge bg-danger",separator:" out of ",preText:"You typed ",postText:" chars available.",placement:"bottom"},o('[data-toggle="maxlength"]').each(function(e,t){var n=o.extend({},s,o(t).data());o(t).maxlength(n)}))})(jQuery);