{% extends "vertical_base.html" %}
{% load static %}

{% block title %}Confirm Delete Hint{% endblock title %}

{% block page_title %}
{% include "partials/page-title.html" with page_title='Confirm Delete Hint' sub_title='Hints & Context' %}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Confirm Deletion</h4>
                <p class="text-muted font-14">
                    Are you sure you want to delete the following hint? This action cannot be undone.
                </p>

                <div class="alert alert-warning" role="alert">
                    <strong>Hint:</strong> {{ hint.hint }}
                </div>

                <form method="post" action="{% url 'thoth_ai_backend:delete_hint_confirmed' hint_id=hint.id %}">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Confirm Delete</button>
                    <a href="{% url 'thoth_ai_backend:hints' %}" class="btn btn-light">Cancel</a>
                </form>

            </div> <!-- end card-body -->
        </div> <!-- end card -->
    </div> <!-- end col -->
</div> <!-- end row -->
{% endblock %}
