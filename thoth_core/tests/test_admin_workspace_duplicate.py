# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.test import TestCase
from django.contrib.admin.sites import AdminSite
from django.contrib.auth.models import User
from django.contrib.messages.storage.fallback import FallbackStorage
from django.http import HttpRequest
from unittest.mock import Mock

from thoth_core.models import Workspace, SqlDb, Agent, Setting, AiModel, BasicAiModel, VectorDb
from thoth_core.admin import WorkspaceAdmin


class WorkspaceDuplicateTestCase(TestCase):
    """Test case for workspace duplication functionality in admin"""
    
    def setUp(self):
        """Set up test environment"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create basic dependencies
        self.basic_ai_model = BasicAiModel.objects.create(
            name='Test Basic Model',
            provider='OpenAI',
            description='Test model'
        )
        
        self.vector_db = VectorDb.objects.create(
            name='Test Vector DB',
            vect_type='Qdrant',
            host='localhost',
            port=6333
        )
        
        self.sql_db = SqlDb.objects.create(
            name='Test SQL DB',
            db_type='SQLite',
            db_name='test.db',
            vector_db=self.vector_db
        )
        
        self.ai_model = AiModel.objects.create(
            basic_model=self.basic_ai_model,
            specific_model='gpt-3.5-turbo',
            name='Test AI Model'
        )
        
        self.agent = Agent.objects.create(
            name='Test Agent',
            ai_model=self.ai_model,
            agent_type='DEFAULT'
        )
        
        self.setting = Setting.objects.create(
            name='Test Setting'
        )
        
        # Create a test workspace
        self.workspace = Workspace.objects.create(
            name='Test Workspace',
            level=Workspace.WorkspaceLevel.BASIC,
            description='Test workspace description',
            sql_db=self.sql_db,
            default_agent=self.agent,
            explanation_ai_model=self.ai_model,
            plotly_generator_ai_model=self.ai_model,
            setting=self.setting
        )
        
        # Add users to the workspace
        self.workspace.users.add(self.user)
        self.workspace.default_workspace.add(self.user)
        
        # Set up admin
        self.site = AdminSite()
        self.admin = WorkspaceAdmin(Workspace, self.site)
        
    def test_duplicate_workspace_basic(self):
        """Test basic workspace duplication"""
        # Create a mock request
        request = HttpRequest()
        request.user = self.user
        request.session = {}
        request._messages = FallbackStorage(request)
        
        # Create queryset with our test workspace
        queryset = Workspace.objects.filter(id=self.workspace.id)
        
        # Get initial count
        initial_count = Workspace.objects.count()
        
        # Execute the duplicate action
        self.admin.duplicate_workspace(request, queryset)
        
        # Check that a new workspace was created
        final_count = Workspace.objects.count()
        self.assertEqual(final_count, initial_count + 1)
        
        # Get the duplicated workspace
        duplicated_workspace = Workspace.objects.exclude(id=self.workspace.id).first()
        
        # Verify the duplicated workspace properties
        self.assertEqual(duplicated_workspace.name, 'Test Workspace copy')
        self.assertEqual(duplicated_workspace.level, self.workspace.level)
        self.assertEqual(duplicated_workspace.description, self.workspace.description)
        self.assertEqual(duplicated_workspace.sql_db, self.workspace.sql_db)
        self.assertEqual(duplicated_workspace.default_agent, self.workspace.default_agent)
        self.assertEqual(duplicated_workspace.explanation_ai_model, self.workspace.explanation_ai_model)
        self.assertEqual(duplicated_workspace.plotly_generator_ai_model, self.workspace.plotly_generator_ai_model)
        self.assertEqual(duplicated_workspace.setting, self.workspace.setting)
        
        # Verify many-to-many relationships are copied
        self.assertEqual(list(duplicated_workspace.users.all()), list(self.workspace.users.all()))
        self.assertEqual(list(duplicated_workspace.default_workspace.all()), list(self.workspace.default_workspace.all()))
        
        # Verify status fields are reset to default values
        self.assertEqual(duplicated_workspace.preprocessing_status, Workspace.PreprocessingStatus.IDLE)
        self.assertEqual(duplicated_workspace.table_comment_status, Workspace.PreprocessingStatus.IDLE)
        self.assertEqual(duplicated_workspace.column_comment_status, Workspace.PreprocessingStatus.IDLE)
        self.assertIsNone(duplicated_workspace.task_id)
        self.assertIsNone(duplicated_workspace.table_comment_task_id)
        self.assertIsNone(duplicated_workspace.column_comment_task_id)
        self.assertIsNone(duplicated_workspace.last_preprocess_log)
        self.assertIsNone(duplicated_workspace.table_comment_log)
        self.assertIsNone(duplicated_workspace.column_comment_log)
        
        # Verify timestamps are reset
        self.assertIsNone(duplicated_workspace.last_preprocess)
        self.assertIsNone(duplicated_workspace.last_hint_load)
        self.assertIsNone(duplicated_workspace.last_sql_loaded)
        self.assertIsNotNone(duplicated_workspace.created_at)
        self.assertIsNotNone(duplicated_workspace.updated_at)
        
    def test_duplicate_multiple_workspaces(self):
        """Test duplicating multiple workspaces"""
        # Create another workspace
        workspace2 = Workspace.objects.create(
            name='Second Workspace',
            level=Workspace.WorkspaceLevel.ADVANCED,
            description='Second test workspace',
            sql_db=self.sql_db,
            setting=self.setting
        )
        
        # Create a mock request
        request = HttpRequest()
        request.user = self.user
        request.session = {}
        request._messages = FallbackStorage(request)
        
        # Create queryset with both workspaces
        queryset = Workspace.objects.filter(id__in=[self.workspace.id, workspace2.id])
        
        # Get initial count
        initial_count = Workspace.objects.count()
        
        # Execute the duplicate action
        self.admin.duplicate_workspace(request, queryset)
        
        # Check that two new workspaces were created
        final_count = Workspace.objects.count()
        self.assertEqual(final_count, initial_count + 2)
        
        # Verify the duplicated workspaces exist
        duplicated_names = ['Test Workspace copy', 'Second Workspace copy']
        for name in duplicated_names:
            self.assertTrue(Workspace.objects.filter(name=name).exists())
            
    def test_duplicate_workspace_name_formatting(self):
        """Test that the duplicate workspace name is correctly formatted"""
        # Test with workspace name that already ends with 'copy'
        workspace_with_copy = Workspace.objects.create(
            name='Workspace copy',
            level=Workspace.WorkspaceLevel.BASIC,
            sql_db=self.sql_db,
            setting=self.setting
        )
        
        # Create a mock request
        request = HttpRequest()
        request.user = self.user
        request.session = {}
        request._messages = FallbackStorage(request)
        
        # Create queryset
        queryset = Workspace.objects.filter(id=workspace_with_copy.id)
        
        # Execute the duplicate action
        self.admin.duplicate_workspace(request, queryset)
        
        # Check the duplicated name
        duplicated_workspace = Workspace.objects.filter(name='Workspace copy copy').first()
        self.assertIsNotNone(duplicated_workspace)
        self.assertEqual(duplicated_workspace.name, 'Workspace copy copy')
