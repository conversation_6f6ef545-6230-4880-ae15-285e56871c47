# Thoth SQL Database Manager - Project Overview

## Project Description
Thoth SQL Database Manager is a comprehensive Python library for managing multiple database systems with a unified interface. It provides database-agnostic operations, LSH (Locality-Sensitive Hashing) for similarity search, and extensible plugin architecture.

## Key Features
- Multi-database support (SQLite, PostgreSQL, MySQL, MariaDB, Oracle, SQL Server, Informix, Supabase)
- LSH-based similarity search for database records
- Plugin architecture for database-specific extensions
- Factory pattern for database manager creation
- Dynamic import system with optional dependencies
- Comprehensive test suite
- Type hints and modern Python practices
- Supabase support with REST API and direct connection modes

## Architecture
- **Core Interfaces**: Abstract base classes defining contracts
- **Adapters**: Database-specific implementations
- **Plugins**: Extensible functionality modules
- **LSH Subsystem**: Locality-sensitive hashing for similarity search
- **Factory Pattern**: Centralized object creation
- **Dynamic Import System**: Optional dependency installation

## Directory Structure
```
thoth_sqldb2/
├── dbmanager/           # Core database management
├── dbmanager/core/      # Core interfaces and factory
├── dbmanager/impl/      # Database-specific implementations
├── dbmanager/adapters/  # Database adapters
├── dbmanager/plugins/   # Plugin system
├── dbmanager/lsh/       # LSH functionality
├── tests/              # Test suite
└── memory-bank/       # Project documentation
```

## Current Status
- Active development phase
- New architecture implemented with factory pattern
- LSH subsystem integrated
- Comprehensive test coverage for all database types
- Dynamic import system implemented with optional dependencies
- Support for selective database driver installation

## Installation Options

### Core Installation (SQLite + PostgreSQL)
```bash
pip install thoth-sqldb
```

### Specific Database Installation
```bash
# PostgreSQL only
pip install thoth-sqldb[postgresql]

# MySQL only
pip install thoth-sqldb[mysql]

# Multiple databases
pip install thoth-sqldb[postgresql,mysql,sqlite]

# All databases
pip install thoth-sqldb[postgresql,mysql,mariadb,sqlserver,oracle,informix,supabase,sqlite]
```

## Quick Start

### Basic Usage
```python
from dbmanager.core.factory import ThothDbFactory

# Create SQLite manager
factory = ThothDbFactory()
manager = factory.create_manager("sqlite", ".", database_path="mydb.db")
```

### Dynamic Import Usage
```python
from dbmanager.dynamic_imports import import_postgresql, import_sqlite

# Import only what you need
pg_manager = import_postgresql()['manager']
sqlite_manager = import_sqlite()['manager']
```

## Supported Databases
- **SQLite**: Built-in, always available
- **PostgreSQL**: `pip install thoth-sqldb[postgresql]`
- **MySQL**: `pip install thoth-sqldb[mysql]`
- **MariaDB**: `pip install thoth-sqldb[mariadb]`
- **SQL Server**: `pip install thoth-sqldb[sqlserver]`
- **Oracle**: `pip install thoth-sqldb[oracle]`
- **Informix**: `pip install thoth-sqldb[informix]`
- **Supabase**: `pip install thoth-sqldb[supabase]`
