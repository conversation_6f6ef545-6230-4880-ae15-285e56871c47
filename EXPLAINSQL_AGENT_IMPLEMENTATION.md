# Implementazione Agent EXPLAINSQL

## Obiettivo
Aggiungere un nuovo agente chiamato EXPLAINSQL seguendo le specifiche richieste:

1. Aggiungere EXPLAINSQL tra i AgentChoices
2. Aggiungere explain_sql_agent dopo test_exec_agent nel Workspace, posizionandolo a fianco nell'admin
3. Spostare ask_human_help_agent su una riga separata nell'admin
4. Aggiornare views e Serializers se necessario

## Analisi del Codice Esistente

### AgentChoices (models.py, linee 11-24)
```python
class AgentChoices(models.TextChoices):
    GENERATESQLBASIC = 'GENERATESQLBASIC', 'Generate SQL Basic'
    DEFAULT = 'DEFAULT', 'Default'
    EXTRACTKEYWORDS = 'EXTRACTKEYWORDS', 'Extract Keywords'
    SELECTCOLUMNS = 'SELECTCOLUMNS', 'Select Columns'
    SQLBASIC = 'SQLBASIC', 'Sql - Basic'
    SQLADVANCED = 'SQLADVANCED', 'Sql - Advanced'
    SQLEXPERT = 'SQLEXPERT', 'Sql - Expert'
    SQLTITAN = 'SQLTITAN', 'Sql - Titan'
    TESTGENERATOR= 'TESTGENERATOR', 'Test Generator'
    TESTEXECUTOR = 'TESTEXECUTOR', 'Test Executor'
    ASKFORHUMANHELP = 'ASKFORHUMANHELP', 'Ask For Human Help'
    VALIDATEQUESTION = 'VALIDATEQUESTION', 'Validate Question'
```

### Workspace Model (models.py, linee 309-310)
Attualmente:
```python
test_exec_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_test_exec')
ask_human_help_agent= models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_ask_human')
```

### Admin Configuration (admin.py, linee 515-517)
Attualmente:
```python
('test_gen_agent_1', 'test_gen_agent_2'),
('test_exec_agent', 'ask_human_help_agent')
```

## Piano di Implementazione

### 1. Modifiche a models.py
- Aggiungere `EXPLAINSQL = 'EXPLAINSQL', 'Explain SQL'` alle AgentChoices
- Aggiungere `explain_sql_agent` nel modello Workspace dopo `test_exec_agent`

### 2. Modifiche a admin.py
- Modificare la configurazione fieldsets per posizionare `explain_sql_agent` a fianco di `test_exec_agent`
- Spostare `ask_human_help_agent` su una riga separata

### 3. Modifiche a serializers.py
- Aggiungere `explain_sql_agent = AgentSerializer()` nel WorkspaceSerializer
- Aggiungere `'explain_sql_agent'` nella lista dei fields

### 4. Modifiche a views.py
- Aggiungere `explain_sql_agent__ai_model__basic_model` nelle query ottimizzate

### 5. Altri file da verificare
- `thoth_core/management/commands/import_workspace.py` - potrebbe richiedere aggiornamenti
- Eventuali migration files

## Dettagli Implementazione

### AgentChoices - Posizionamento
Inserire EXPLAINSQL in ordine logico, probabilmente dopo TESTEXECUTOR e prima di ASKFORHUMANHELP.

### Workspace Model - Campo explain_sql_agent
```python
explain_sql_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_explain_sql')
```

### Admin - Nuova configurazione fieldsets
```python
('test_gen_agent_1', 'test_gen_agent_2'),
('test_exec_agent', 'explain_sql_agent'),
('ask_human_help_agent',)
```

### Serializer - Aggiornamenti
Aggiungere il campo sia come proprietà che nella lista fields.

### Views - Query Optimization
Aggiungere nelle select_related per ottimizzare le query.

## Note Tecniche
- Il related_name deve essere unico: `workspaces_explain_sql`
- Mantenere la consistenza con gli altri agenti esistenti
- Verificare che non ci siano conflitti nei nomi dei campi
- Testare che l'admin form funzioni correttamente con la nuova disposizione

## Validazione
Dopo l'implementazione, verificare:
1. L'agente EXPLAINSQL appare nelle scelte disponibili
2. Il campo explain_sql_agent è visibile nell'admin del Workspace
3. La disposizione nell'admin è corretta (explain_sql_agent a fianco di test_exec_agent)
4. ask_human_help_agent è su una riga separata
5. Le API restituiscono correttamente i dati del nuovo agente
6. Non ci sono errori di migrazione o di integrità referenziale