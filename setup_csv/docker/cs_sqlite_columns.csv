id,original_column_name,column_name,data_format,column_description,generated_comment,value_description,sql_table_id,pk_field,fk_field
2140,CDSCode,CDSCode,VARCHAR,,,,284,PK,schools.CDSCode
2141,Academic Year,,VARCHAR,Description of Academic Year,,,284,,
2142,County Code,,VARCHAR,County Code,,,284,,
2143,District Code,,INT,District Code,,,284,,
2144,School Code,,VARCHAR,School Code,,,284,,
2145,County Name,,VARCHAR,County Code ,,,284,,
2146,District Name,,VARCHAR,District Name ,,,284,,
2147,School Name,,VARCHAR,School Name ,,,284,,
2148,District Type,,VARCHAR,District Type,,,284,,
2149,School Type,,VARCHAR,School Type ,,,284,,
2150,Educational Option Type,,VARCHAR,Educational Option Type,,,284,,
2151,NSLP Provision Status,,VARCHAR,NSLP Provision Status,,,284,,
2152,Charter School (Y/N),,INT,Charter School (Y/N),,"0: N;
1: Y",284,,
2153,Charter School Number,,VARCHAR,Charter School Number,,,284,,
2154,Charter Funding Type,,VARCHAR,Charter Funding Type,,,284,,
2155,IRC,,INT,,,Not useful,284,,
2156,Low Grade,,VARCHAR,Low Grade,,,284,,
2157,High Grade,,VARCHAR,High Grade,,,284,,
2158,Enrollment (K-12),,FLOAT,Enrollment (K-12),,"commonsense evidence:

K-12: 1st grade - 12nd grade ",284,,
2159,Free Meal Count (K-12),,FLOAT,Free Meal Count (K-12),,"commonsense evidence:

eligible free rate = Free Meal Count / Enrollment",284,,
2160,Percent (%) Eligible Free (K-12),,FLOAT,,,,284,,
2161,FRPM Count (K-12),,FLOAT,Free or Reduced Price Meal Count (K-12),,"commonsense evidence:

eligible FRPM rate = FRPM / Enrollment",284,,
2162,Percent (%) Eligible FRPM (K-12),,FLOAT,,,,284,,
2163,Enrollment (Ages 5-17),,FLOAT,Enrollment (Ages 5-17),,,284,,
2164,Free Meal Count (Ages 5-17),,FLOAT,Free Meal Count (Ages 5-17),,"commonsense evidence:

eligible free rate = Free Meal Count / Enrollment",284,,
2165,Percent (%) Eligible Free (Ages 5-17),,FLOAT,,,,284,,
2166,FRPM Count (Ages 5-17),,FLOAT,,,,284,,
2167,Percent (%) Eligible FRPM (Ages 5-17),,FLOAT,,,,284,,
2168,2013-14 CALPADS Fall 1 Certification Status,,INT,2013-14 CALPADS Fall 1 Certification Status,,,284,,
2169,cds,cds,VARCHAR,,,,285,PK,schools.CDSCode
2170,rtype,,VARCHAR,rtype,,unuseful,285,,
2171,sname,school name,VARCHAR,school name,,,285,,
2172,dname,district name,VARCHAR,district segment,,,285,,
2173,cname,county name,VARCHAR,county name,,,285,,
2174,enroll12,enrollment (1st-12nd grade),INT,enrollment (1st-12nd grade),,,285,,
2175,NumTstTakr,Number of Test Takers,INT,Number of Test Takers in this school,,number of test takers in each school,285,,
2176,AvgScrRead,average scores in Reading,INT,average scores in Reading,,average scores in Reading,285,,
2177,AvgScrMath,average scores in Math,INT,average scores in Math,,average scores in Math,285,,
2178,AvgScrWrite,average scores in writing,INT,average scores in writing,,average scores in writing,285,,
2179,NumGE1500,Number of Test Takers Whose Total SAT Scores Are Greater or Equal to 1500,INT,Number of Test Takers Whose Total SAT Scores Are Greater or Equal to 1500,,"Number of Test Takers Whose Total SAT Scores Are Greater or Equal to 1500

commonsense evidence:

Excellence Rate = NumGE1500 / NumTstTakr",285,,
2180,CDSCode,CDSCode,VARCHAR,,,,286,PK,
2181,NCESDist,National Center for Educational Statistics school district identification number,VARCHAR,"This field represents the 7-digit National Center for Educational Statistics (NCES) school district identification number. The first 2 digits identify the state and the last 5 digits identify the school district. Combined, they make a unique 7-digit ID for each school district.",,,286,,
2182,NCESSchool,National Center for Educational Statistics school identification number,VARCHAR,This field represents the 5-digit NCES school identification number. The NCESSchool combined with the NCESDist form a unique 12-digit ID for each school.,,,286,,
2183,StatusType,,VARCHAR,This field identifies the status of the district. ,,"Definitions of the valid status types are listed below:
·       Active: The district is in operation and providing instructional services.
·       Closed: The district is not in operation and no longer providing instructional services.
·       Merged: The district has combined with another district or districts.
·       Pending: The district has not opened for operation and instructional services yet, but plans to open within the next 9–12 months.",286,,
2184,County,,VARCHAR,County name,,,286,,
2185,District,,VARCHAR,District,,,286,,
2186,School,,VARCHAR,School,,,286,,
2187,Street,,VARCHAR,Street,,,286,,
2188,StreetAbr,street address ,VARCHAR,"The abbreviated street address of the school, district, or administrative authority’s physical location.",,"The abbreviated street address of the school, district, or administrative authority’s physical location. Note: Some records (primarily records of closed or retired schools) may not have data in this field.",286,,
2189,City,,VARCHAR,City,,,286,,
2190,Zip,,VARCHAR,Zip,,,286,,
2191,State,,VARCHAR,State,,,286,,
2192,MailStreet,,VARCHAR,MailStreet,,"The unabbreviated mailing address of the school, district, or administrative authority. Note: 1) Some entities (primarily closed or retired schools) may not have data in this field; 2) Many active entities have not provided a mailing street address. For your convenience we have filled the unpopulated MailStreet cells with Street data.",286,,
2193,MailStrAbr,mailing street address ,VARCHAR,,,"the abbreviated mailing street address of the school, district, or administrative authority.Note: Many active entities have not provided a mailing street address. For your convenience we have filled the unpopulated MailStrAbr cells with StreetAbr data.",286,,
2194,MailCity,mailing city,VARCHAR,,,"The city associated with the mailing address of the school, district, or administrative authority. Note: Many entities have not provided a mailing address city. For your convenience we have filled the unpopulated MailCity cells with City data.",286,,
2195,MailZip,mailing zip ,VARCHAR,,,"The zip code associated with the mailing address of the school, district, or administrative authority. Note: Many entities have not provided a mailing address zip code. For your convenience we have filled the unpopulated MailZip cells with Zip data.",286,,
2196,MailState,mailing state,VARCHAR,,,The state within the mailing address. For your convenience we have filled the unpopulated MailState cells with State data.,286,,
2197,Phone,,VARCHAR,Phone,,,286,,
2198,Ext,extension,VARCHAR,"The phone number extension of the school, district, or administrative authority.",,"The phone number extension of the school, district, or administrative authority.",286,,
2199,Website,,VARCHAR,"The website address of the school, district, or administrative authority.",,"The website address of the school, district, or administrative authority.",286,,
2200,OpenDate,,DATE,The date the school opened.,,,286,,
2201,ClosedDate,,DATE,The date the school closed.,,,286,,
2202,Charter,,INT,This field identifies a charter school. ,,"The field is coded as follows:

·       1 = The school is a charter

·       0 = The school is not a charter",286,,
2203,CharterNum,,VARCHAR,"The charter school number,",,4-digit number assigned to a charter school.,286,,
2204,FundingType,,VARCHAR,Indicates the charter school funding type,,"Values are as follows:

·       Not in CS (California School) funding model

·       Locally funded

·       Directly funded",286,,
2205,DOC,District Ownership Code,VARCHAR,District Ownership Code,,"The District Ownership Code (DOC) is the numeric code used to identify the category of the Administrative Authority.
•       00 - County Office of Education
•       02 – State Board of Education
•       03 – Statewide Benefit Charter
•       31 – State Special Schools
•       34 – Non-school Location*
•       52 – Elementary School District
•       54 – Unified School District
•       56 – High School District
•       98 – Regional Occupational Center/Program (ROC/P)
commonsense evidence:
*Only the California Education Authority has been included in the non-school location category.",286,,
2206,DOCType,The District Ownership Code Type,VARCHAR,The District Ownership Code Type is the text description of the DOC category.,,(See text values in DOC field description above),286,,
2207,SOC,School Ownership Code,VARCHAR,The School Ownership Code is a numeric code used to identify the type of school.,,"•      08 - Preschool      
•       09 – Special Education Schools (Public)
•      11 – Youth Authority Facilities (CEA)
•       13 – Opportunity Schools
•       14 – Juvenile Court Schools
•       15 – Other County or District Programs
•       31 – State Special Schools
•       60 – Elementary School (Public)
•       61 – Elementary School in 1 School District (Public)
•       62 – Intermediate/Middle Schools (Public)
•       63 – Alternative Schools of Choice
•       64 – Junior High Schools (Public)
•       65 – K-12 Schools (Public)
•       66 – High Schools (Public)
•       67 – High Schools in 1 School District (Public)
•       68 – Continuation High Schools
•       69 – District Community Day Schools
•       70 – Adult Education Centers
•       98 – Regional Occupational Center/Program (ROC/P)",286,,
2208,SOCType,School Ownership Code Type,VARCHAR,The School Ownership Code Type is the text description of the type of school.,,The School Ownership Code Type is the text description of the type of school.,286,,
2209,EdOpsCode,Education Option Code,VARCHAR,The Education Option Code is a short text description of the type of education offered.,,"
•      ALTSOC – Alternative School of Choice
•      COMM – County Community School
•       COMMDAY – Community Day School
•       CON – Continuation School
•       JUV – Juvenile Court School
•       OPP – Opportunity School
•       YTH – Youth Authority School
•       SSS – State Special School
•       SPEC – Special Education School
•       TRAD – Traditional
•       ROP – Regional Occupational Program
•       HOMHOS – Home and Hospital
•       SPECON – District Consortia Special Education School",286,,
2210,EdOpsName,Educational Option Name,VARCHAR,Educational Option Name,,The Educational Option Name is the long text description of the type of education being offered.,286,,
2211,EILCode,Educational Instruction Level Code,VARCHAR,The Educational Instruction Level Code is a short text description of the institution's type relative to the grade range served.,,"•       A – Adult
•       ELEM – Elementary
•       ELEMHIGH – Elementary-High Combination
•       HS – High School
•       INTMIDJR – Intermediate/Middle/Junior High
•       PS – Preschool
•       UG – Ungraded",286,,
2212,EILName,Educational Instruction Level Name ,VARCHAR,The Educational Instruction Level Name is the long text description of the institution’s type relative to the grade range served.,,The Educational Instruction Level Name is the long text description of the institution’s type relative to the grade range served.,286,,
2213,GSoffered,grade span offered,VARCHAR,"The grade span offered is the lowest grade and the highest grade offered or supported by the school, district, or administrative authority. This field might differ from the grade span served as reported in the most recent certified California Longitudinal Pupil Achievement (CALPADS) Fall 1 data collection.",,"For example XYZ School might display the following data:

GSoffered = P–Adult

GSserved = K–12",286,,
2214,GSserved,grade span served.,VARCHAR,It is the lowest grade and the highest grade of student enrollment as reported in the most recent certified CALPADS Fall 1 data collection. Only K–12 enrollment is reported through CALPADS. This field may differ from the grade span offered.,,"commonsense evidence:

1.     Only K–12 enrollment is reported through CALPADS

2.     Note: Special programs at independent study, alternative education, and special education schools will often exceed the typical grade span for schools of that type",286,,
2215,Virtual,,VARCHAR,"This field identifies the type of virtual instruction offered by the school. Virtual instruction is instruction in which students and teachers are separated by time and/or location, and interaction occurs via computers and/or telecommunications technologies. ",,"The field is coded as follows:

·       F = Exclusively Virtual – The school has no physical building where students meet with each other or with teachers, all instruction is virtual.

·       V = Primarily Virtual – The school focuses on a systematic program of virtual instruction but includes some physical meetings among students or with teachers.

·       C = Primarily Classroom – The school offers virtual courses but virtual instruction is not the primary means of instruction.

·       N = Not Virtual – The school does not offer any virtual instruction.

·       P = Partial Virtual – The school offers some, but not all, instruction through virtual instruction. Note: This value was retired and replaced with the Primarily Virtual and Primarily Classroom values beginning with the 2016–17 school year.",286,,
2216,Magnet,,INT,This field identifies whether a school is a magnet school and/or provides a magnet program. ,,"The field is coded as follows:

·       1 = Magnet - The school is a magnet school and/or offers a magnet program.

·       0 = Not Magnet - The school is not a magnet school and/or does not offer a magnet program.

commonsense evidence:

Note: Preschools and adult education centers do not contain a magnet school indicator.",286,,
2217,Latitude,,FLOAT,"The angular distance (expressed in degrees) between the location of the school, district, or administrative authority and the equator measured north to south.",,"The angular distance (expressed in degrees) between the location of the school, district, or administrative authority and the equator measured north to south.",286,,
2218,Longitude,,FLOAT,"The angular distance (expressed in degrees) between the location of the school, district, or administrative authority and the prime meridian (Greenwich, England) measured from west to east.",,"The angular distance (expressed in degrees) between the location of the school, district, or administrative authority and the prime meridian (Greenwich, England) measured from west to east.",286,,
2219,AdmFName1,administrator's first name,VARCHAR,administrator's first name,,"The superintendent’s or principal’s first name.

commonsense evidence:

Only active and pending districts and schools will display administrator information, if applicable.",286,,
2220,AdmLName1,administrator's last name,VARCHAR,administrator's last name,,"The superintendent’s or principal’s last name.

commonsense evidence:
Only active and pending districts and schools will display administrator information, if applicable.",286,,
2221,AdmEmail1,administrator's email address,VARCHAR,administrator's email address,,"The superintendent’s or principal’s email address.

commonsense evidence:

Only active and pending districts and schools will display administrator information, if applicable.",286,,
2222,AdmFName2,,VARCHAR,,,SAME as 1,286,,
2223,AdmLName2,,VARCHAR,,,,286,,
2224,AdmEmail2,,VARCHAR,,,,286,,
2225,AdmFName3,,VARCHAR,,,not useful,286,,
2226,AdmLName3,,VARCHAR,,,not useful,286,,
2227,AdmEmail3,,VARCHAR,,,not useful,286,,
2228,LastUpdate,,DATE,,,when is this record updated last time,286,,
