# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import json
import os
import re
from typing import Any

from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager
from haystack.utils import Secret
from haystack_integrations.components.generators.anthropic import AnthropicChatGenerator
from haystack_integrations.components.generators.google_ai import GoogleAIGeminiChatGenerator
from haystack_integrations.components.generators.mistral import MistralChatGenerator
from haystack.components.generators.chat import OpenAIChatGenerator
from haystack_integrations.components.generators.ollama import OllamaChatGenerator
from thoth_core.models import LLMChoices

def output_to_json(output):
    """
    Extracts and parses JSON content from the output of a generator.

    This function searches for a JSON-like string enclosed in square brackets
    within the generator's response and attempts to parse it into a Python object.

    Args:
        output (dict): A dictionary containing the generator's output.
                       Expected to have a structure like:
                       {'generator': {'replies': [{'content': '...'}]}}

    Returns:
        dict or None: The parsed JSON content if found and successfully parsed,
                      or None if no valid JSON content is found.

    Note:
        The function assumes that the JSON content is enclosed in square brackets
        and is part of the first reply in the generator's output.
    """
    response = output['generator']['replies'][0].text
    # Extract the description
    json_match = re.search(r'\[.*\]', response, re.DOTALL)
    if json_match:
        json_str = json_match.group(0)
        return json.loads(json_str)
    else:
        return None

def setup_default_comment_llm_model(setting) -> Any:
    """
    Set up and return the default language model for comment generation based on the provided settings.

    This function initializes a language model for generating comments, specifically
    supporting the Mistral model. It uses the settings provided to configure the model.

    Args:
        setting: An object containing configuration settings for the AI model.
                 Expected to have a 'comment_model' attribute with details about the model.

    Returns:
        Any: A configured MistralChatGenerator instance if the basic model is Mistral,
             or None if the model is not supported.

    Note:
        The function currently only supports the Mistral model. For other models,
        it returns None.
    """
    ai_model = setting.comment_model
    if ai_model.basic_model.provider == LLMChoices.OPENAI:
        return OpenAIChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or os.environ.get('OPENAI_API_KEY')),
            model=ai_model.specific_model,
        )
    elif ai_model.basic_model.provider == LLMChoices.GEMINI:
        return GoogleAIGeminiChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or os.environ.get('GEMINI_API_KEY')),
            model=ai_model.specific_model)
    elif ai_model.basic_model.provider == LLMChoices.CLAUDE:
        return AnthropicChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or os.environ.get('ANTHROPIC_API_KEY')),
            model=ai_model.specific_model)
    elif ai_model.basic_model.provider == LLMChoices.CODESTRAL:
        return OpenAIChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or os.environ.get('MISTRAL_API_KEY')),
            model=ai_model.specific_model,
            api_base_url="https://api.codestral.com/v1")
    elif ai_model.basic_model.provider == LLMChoices.DEEPSEEK:
        return OpenAIChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or os.environ.get('DEEPSEEK_API_KEY')),
            model=ai_model.specific_model,
            api_base_url="https://api.deepseek.com/v1")
    elif ai_model.basic_model.provider == LLMChoices.LLAMA:
        return OllamaChatGenerator(
            model=ai_model.specific_model,
            timeout=45, url="http://localhost:11434",
            generation_kwargs={
                "num_predict": 100,
                "temperature": float(ai_model.temperature)
            })
    elif ai_model.basic_model.provider == LLMChoices.MISTRAL:
        return MistralChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or os.environ.get('MISTRAL_API_KEY')),
            model=ai_model.specific_model)
    elif ai_model.basic_model.provider == LLMChoices.OLLAMA:
        return OllamaChatGenerator(
            model=ai_model.specific_model,
            timeout=45, url="http://localhost:11434",
            generation_kwargs={
                "num_predict": 100,
                "temperature": float(ai_model.temperature)
            })
    elif ai_model.basic_model.provider == LLMChoices.OPENROUTER:
        return OpenAIChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or os.environ.get('OPENROUTER_API_KEY')),
            model=ai_model.specific_model,
            api_base_url="https://openrouter.ai/api/v1")
    elif ai_model.basic_model.provider == LLMChoices.LMSTUDIO:
        # LM Studio uses OpenAI-compatible API format
        base_url = ai_model.url or "http://localhost:1234/v1"
        return OpenAIChatGenerator(
            api_key=Secret.from_token(ai_model.api_key or "lm-studio"),
            model=ai_model.specific_model,
            api_base_url=base_url)
    else:
        return None


def setup_sql_db(sql_db):
    """
    Set up and return a database component based on the provided SQL database configuration.

    This function initializes a database component, supporting PostgreSQL and SQLite.
    It uses the configuration details provided in the sql_db parameter to establish
    a connection to the database.

    Args:
        sql_db: An object containing the SQL database configuration.
                Expected to have attributes:
                - db_type: The type of the database (e.g., 'PostgreSQL', 'SQLite')
                - db_host: The host address of the database (for PostgreSQL)
                - db_port: The port number for the database connection (for PostgreSQL)
                - name: The name of the database
                - schema: The schema to be used in the database (for PostgreSQL)
                - user_name: The username for database authentication (for PostgreSQL)
                - password: The password for database authentication (for PostgreSQL)
                - db_mode: The mode for SQLite database

    Returns:
        ThothPgManager or ThothSqliteManager: An initialized database component.

    Raises:
        ValueError: If the specified database type is not supported.

    Note:
        Currently, PostgreSQL and SQLite are supported. Attempting to use any other
        database type will raise a ValueError.
    """
    db_type = sql_db.db_type
    if db_type == 'PostgreSQL':
        return ThothPgManager(
            host = sql_db.db_host,
            port = sql_db.db_port,
            dbname = sql_db.db_name,
            schema = sql_db.schema,
            user=sql_db.user_name,
            password=sql_db.password)
    elif db_type == 'SQLite':
        return ThothSqliteManager(
            db_id=sql_db.name,
            db_root_path=os.getenv("DB_ROOT_PATH"),
            db_mode=sql_db.db_mode)
    else:
        raise ValueError(f"Unsupported database type: {db_type}")
