{% extends "vertical_base.html" %}

{% load i18n %}

{% block title %}{% trans "Sign Out" %}{% endblock title %}

{% block page_title %}
{% include "partials/page-title.html" with page_title='Sign Out' sub_title='Account' %}
{% endblock %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <h4 class="header-title mb-3">{% trans "Sign Out" %}</h4>
        
        <div class="row">
          <div class="col-md-6">
            <p class="text-muted mb-3">{% trans 'Are you sure you want to sign out?' %}</p>

            <form method="post" action="{% url 'account_logout' %}" novalidate>
              {% csrf_token %}
              {% if redirect_field_value %}
              <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}"/>
              {% endif %}
              <button class="btn btn-danger" type="submit">{% trans 'Sign Out' %}</button>
              <a href="{% url 'index' %}" class="btn btn-light ms-1">{% trans 'Cancel' %}</a>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
