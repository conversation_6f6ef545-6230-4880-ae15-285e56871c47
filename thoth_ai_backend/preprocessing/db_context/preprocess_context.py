# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import logging

from dotenv import load_dotenv
from typing import Type

from haystack.document_stores.types import DuplicatePolicy
from vdbmanager.ThothVectorStore import ColumnNameDocument, ThothType

from .load_table_description import load_tables_description

load_dotenv(override=True)


def make_db_context_vec_db(document_store, db_params, **kwargs) -> None:
    """
    Creates a context vector database for the specified database directory.

    This function performs the following steps:
    1. Loads table descriptions from the specified database
    2. Deletes any existing column name documents from the vector store
    3. Creates new ColumnNameDocument objects for each column in each table
    4. Uploads the new documents to the vector store

    Args:
        document_store: Vector store instance to store the documents
        db_params (dict): Database parameters containing:
            - db_name (str): Name of the database
        **kwargs: Additional keyword arguments:
            - use_value_description (bool): Whether to include value descriptions (default: True)

    Returns:
        None

    Example:
        db_params = {"db_name": "my_database"}
        make_db_context_vec_db(vector_store, db_params, use_value_description=True)
    """
    #TODO - gestire lo use_value_description
    db_id = db_params["id"]
    db_name = db_params["name"]
    table_description = load_tables_description(
        db_id, use_value_description=kwargs.get("use_value_description", True)
    )

    docs = []

    # First get all existing Column_name documents to delete them
    existing_docs = document_store.get_documents_by_type(ThothType.COLUMN_NAME, ColumnNameDocument)
    if existing_docs:
        doc_ids = [doc.id for doc in existing_docs]
        document_store.delete_documents(doc_ids)

    # Then proceed with creating new documents
    for table_name, columns in table_description.items():
        for column_name, column_info in columns.items():
            column_description=ColumnNameDocument(
                table_name=table_name,
                column_name=column_info.get("column_name", ""),
                original_column_name=column_info.get("original_column_name", ""),
                column_description=column_info.get("column_description", ""),
                value_description=column_info.get("value_description", ""),
                thoth_type=ThothType.COLUMN_NAME,
                text=""
            )
            docs.append(column_description)

    # Upload of documents to the vector store.
    logging.info(
        f"Uploading {len(docs)} nodes with columns_data key in collection {document_store.collection_name} paired with database {db_name}"
    )
    if docs:
        document_store.bulk_add_documents(docs, policy=DuplicatePolicy.OVERWRITE)
    logging.info(f"Context vector database created at {db_name}")
