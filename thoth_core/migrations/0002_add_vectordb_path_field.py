# Generated manually to add path field to VectorDb model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('thoth_core', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='vectordb',
            name='path',
            field=models.CharField(blank=True, max_length=500),
        ),
        migrations.AlterField(
            model_name='vectordb',
            name='port',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
