# Thoth SQL Database Manager - API Documentation

This document provides detailed API documentation for the Thoth SQL Database Manager library.

## Table of Contents

1. [Core Classes](#core-classes)
2. [Factory Pattern](#factory-pattern)
3. [Plugin System](#plugin-system)
4. [Document Models](#document-models)
5. [Database Adapters](#database-adapters)
6. [LSH System](#lsh-system)
7. [Error Handling](#error-handling)
8. [Examples](#examples)

## Core Classes

### ThothDbManager

The main abstract base class for database operations.

```python
from dbmanager import ThothDbManager
```

#### Class Methods

##### `get_instance(db_type: str, **kwargs) -> ThothDbManager`

Factory method to create or retrieve database manager instances.

**Parameters:**
- `db_type` (str): Database type identifier
  - Supported values: `"postgresql"`, `"mysql"`, `"mariadb"`, `"sqlite"`, `"supabase"`, `"sqlserver"`, `"oracle"`, `"informix"`
- `db_root_path` (str): Path to store database-related files and LSH indexes
- `db_mode` (str, optional): Operating mode. Defaults to `"dev"`
  - Common values: `"dev"`, `"prod"`, `"test"`
- `**kwargs`: Database-specific connection parameters

**Returns:** Database manager instance

**Raises:**
- `ValueError`: If database type is unsupported or required parameters are missing
- `RuntimeError`: If plugin initialization fails

**Example:**
```python
manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    db_mode="production",
    host="localhost",
    port=5432,
    database="myapp",
    user="dbuser",
    password="dbpass"
)
```

#### Instance Methods

##### `execute_sql(sql: str, params: Optional[Dict] = None, fetch: Union[str, int] = "all", timeout: int = 60) -> Any`

Execute SQL queries against the database.

**Parameters:**
- `sql` (str): SQL query string
- `params` (Optional[Dict]): Parameters for prepared statements
- `fetch` (Union[str, int]): How to fetch results
  - `"all"`: Fetch all rows
  - `"one"`: Fetch one row
  - `int`: Fetch specified number of rows
- `timeout` (int): Query timeout in seconds

**Returns:** Query results (format depends on database adapter)

**Example:**
```python
# Simple query
results = manager.execute_sql("SELECT * FROM users LIMIT 10")

# Parameterized query
results = manager.execute_sql(
    "SELECT * FROM users WHERE age > %(min_age)s",
    params={"min_age": 18}
)

# Fetch single row
user = manager.execute_sql(
    "SELECT * FROM users WHERE id = %(user_id)s",
    params={"user_id": 123},
    fetch="one"
)
```

##### `get_tables() -> List[Dict[str, str]]`

Get list of tables in the database.

**Returns:** List of dictionaries with table information

**Dictionary Keys:**
- `name` (str): Table name
- `comment` (str): Table comment/description

**Example:**
```python
tables = manager.get_tables()
for table in tables:
    print(f"Table: {table['name']} - {table['comment']}")
```

##### `get_columns(table_name: str) -> List[Dict[str, Any]]`

Get column information for a specific table.

**Parameters:**
- `table_name` (str): Name of the table

**Returns:** List of dictionaries with column metadata

**Dictionary Keys:**
- `name` (str): Column name
- `data_type` (str): Column data type
- `comment` (str): Column comment/description
- `is_pk` (bool): Whether column is part of primary key
- `is_nullable` (bool): Whether column allows NULL values
- `default_value` (Optional[str]): Default value if any

**Example:**
```python
columns = manager.get_columns("users")
for col in columns:
    pk_indicator = " (PK)" if col['is_pk'] else ""
    print(f"{col['name']}: {col['data_type']}{pk_indicator}")
```

##### `get_foreign_keys() -> List[Dict[str, str]]`

Get foreign key relationships in the database.

**Returns:** List of dictionaries with foreign key information

**Dictionary Keys:**
- `source_table_name` (str): Source table name
- `source_column_name` (str): Source column name
- `target_table_name` (str): Target table name
- `target_column_name` (str): Target column name
- `constraint_name` (str): Foreign key constraint name

**Example:**
```python
foreign_keys = manager.get_foreign_keys()
for fk in foreign_keys:
    print(f"{fk['source_table_name']}.{fk['source_column_name']} -> "
          f"{fk['target_table_name']}.{fk['target_column_name']}")
```

##### `get_example_data(table_name: str, number_of_rows: int = 30) -> Dict[str, List[Any]]`

Get the most frequent values for each column in a table.

**Parameters:**
- `table_name` (str): Name of the table
- `number_of_rows` (int): Maximum number of example values per column

**Returns:** Dictionary mapping column names to lists of example values

**Example:**
```python
examples = manager.get_example_data("users", number_of_rows=10)
for column, values in examples.items():
    print(f"Column '{column}' examples: {values[:5]}")
```

##### `get_unique_values() -> Dict[str, Dict[str, List[str]]]`

Get unique values from all text columns in the database.

**Returns:** Nested dictionary structure

**Structure:**
```python
{
    "table_name": {
        "column_name": ["unique_value1", "unique_value2", ...]
    }
}
```

**Example:**
```python
unique_values = manager.get_unique_values()
for table, columns in unique_values.items():
    for column, values in columns.items():
        print(f"{table}.{column}: {len(values)} unique values")
```

##### `query_lsh(keyword: str, signature_size: int = 30, n_gram: int = 3, top_n: int = 10) -> Dict[str, Dict[str, List[str]]]`

Search for similar values using LSH (Locality-Sensitive Hashing).

**Parameters:**
- `keyword` (str): Search term
- `signature_size` (int): MinHash signature size (affects accuracy vs speed)
- `n_gram` (int): N-gram size for text processing
- `top_n` (int): Number of similar values to return per column

**Returns:** Nested dictionary with similar values

**Structure:**
```python
{
    "table_name": {
        "column_name": ["similar_value1", "similar_value2", ...]
    }
}
```

**Example:**
```python
similar = manager.query_lsh("john smith", top_n=5)
for table, columns in similar.items():
    for column, values in columns.items():
        print(f"Similar to 'john smith' in {table}.{column}:")
        for value in values:
            print(f"  - {value}")
```

##### `set_lsh() -> str`

Initialize or load LSH indexes for similarity search.

**Returns:** Status string
- `"success"`: LSH loaded successfully
- `"error"`: Error loading LSH

**Example:**
```python
status = manager.set_lsh()
if status == "success":
    print("LSH ready for queries")
else:
    print("LSH initialization failed")
```

## Factory Pattern

### ThothDbFactory

Factory class for creating database manager instances with advanced features.

```python
from dbmanager import ThothDbFactory
```

#### Static Methods

##### `create_manager(db_type: str, db_root_path: str, db_mode: str = "dev", **kwargs) -> DbPlugin`

Create a database manager instance using the plugin system.

**Parameters:**
- `db_type` (str): Database type identifier
- `db_root_path` (str): Path to database root directory
- `db_mode` (str): Database mode
- `**kwargs`: Database-specific connection parameters

**Returns:** Database plugin instance

**Example:**
```python
manager = ThothDbFactory.create_manager(
    db_type="postgresql",
    db_root_path="./data",
    db_mode="production",
    host="localhost",
    port=5432,
    database="myapp",
    user="dbuser",
    password="dbpass"
)
```

##### `list_available_databases() -> List[str]`

List all available database types.

**Returns:** List of supported database type identifiers

**Example:**
```python
available = ThothDbFactory.list_available_databases()
print(f"Supported databases: {available}")
# Output: ['postgresql', 'mysql', 'mariadb', 'sqlite', 'supabase']
```

##### `get_required_parameters(db_type: str) -> Dict[str, Any]`

Get required connection parameters for a database type.

**Parameters:**
- `db_type` (str): Database type identifier

**Returns:** Dictionary describing required and optional parameters

**Example:**
```python
params = ThothDbFactory.get_required_parameters("postgresql")
print(f"Required: {params['required']}")
print(f"Optional: {params['optional']}")
```

##### `validate_database_type(db_type: str) -> bool`

Check if a database type is supported.

**Parameters:**
- `db_type` (str): Database type identifier

**Returns:** True if supported, False otherwise

##### `get_plugin_status() -> Dict[str, Any]`

Get status information about all registered plugins.

**Returns:** Status information for all plugins

## Plugin System

### DbPlugin (Abstract Base Class)

Base class for database plugins.

```python
from dbmanager.core.interfaces import DbPlugin
```

#### Plugin Metadata

Each plugin defines metadata:

```python
class MyPlugin(DbPlugin):
    plugin_name = "My Database Plugin"
    plugin_version = "1.0.0"
    supported_db_types = ["mydatabase"]
    required_dependencies = ["mydatabase-driver"]
```

#### Abstract Methods

##### `create_adapter(**kwargs) -> DbAdapter`

Create and return a database adapter instance.

##### `validate_connection_params(**kwargs) -> bool`

Validate connection parameters for this plugin.

### DbPluginRegistry

Registry for managing database plugins.

```python
from dbmanager.core.registry import DbPluginRegistry
```

#### Class Methods

##### `register(db_type: str, plugin_class: Type[DbPlugin]) -> None`

Register a plugin for a specific database type.

##### `list_plugins() -> List[str]`

List all registered database types.

##### `get_plugin_info(db_type: Optional[str] = None) -> Dict[str, Any]`

Get information about registered plugins.

## Document Models

### BaseThothDbDocument

Base class for all document types using Pydantic for validation.

```python
from dbmanager.documents import BaseThothDbDocument, ThothDbType
```

#### Attributes

- `id` (str): Unique identifier
- `thoth_type` (ThothDbType): Document type
- `text` (str): Human-readable description
- `metadata` (Dict[str, Any]): Additional metadata

### TableDocument

Document representing a database table.

```python
from dbmanager.documents import TableDocument

table_doc = TableDocument(
    table_name="users",
    schema_name="public",
    comment="User account information",
    row_count=1000
)
```

#### Attributes

- `table_name` (str): Table name
- `schema_name` (str): Schema name
- `comment` (str): Table comment
- `row_count` (Optional[int]): Number of rows

### ColumnDocument

Document representing a database column.

```python
from dbmanager.documents import ColumnDocument

column_doc = ColumnDocument(
    table_name="users",
    column_name="email",
    data_type="varchar",
    is_nullable=False,
    is_pk=False,
    comment="User email address"
)
```

#### Attributes

- `table_name` (str): Parent table name
- `column_name` (str): Column name
- `data_type` (str): Data type
- `is_pk` (bool): Primary key flag
- `is_nullable` (bool): Nullable flag
- `comment` (str): Column comment

### Other Document Types

- `QueryDocument`: SQL queries with metadata
- `SchemaDocument`: Database schemas
- `ForeignKeyDocument`: Foreign key relationships
- `IndexDocument`: Database indexes

## Database Adapters

### DbAdapter (Abstract Base Class)

Base class for database-specific operations.

```python
from dbmanager.core.interfaces import DbAdapter
```

#### Abstract Methods

##### `connect() -> None`

Establish database connection.

##### `disconnect() -> None`

Close database connection.

##### `execute_query(query: str, params: Optional[Dict] = None, fetch: Union[str, int] = "all", timeout: int = 60) -> Any`

Execute SQL query through adapter.

##### `get_tables_as_documents() -> List[TableDocument]`

Return tables as document objects.

##### `get_columns_as_documents(table_name: str) -> List[ColumnDocument]`

Return columns as document objects.

##### `health_check() -> bool`

Check if database connection is healthy.

## LSH System

### LshManager

Manages LSH (Locality-Sensitive Hashing) operations.

```python
from dbmanager.lsh.manager import LshManager

lsh_manager = LshManager(db_directory_path)
```

#### Methods

##### `load_lsh() -> bool`

Load LSH indexes from storage.

##### `query(keyword: str, **kwargs) -> Dict[str, Dict[str, List[str]]]`

Query LSH for similar values.

## Error Handling

### Common Exceptions

#### ValueError

Raised for invalid parameters or unsupported database types.

```python
try:
    manager = ThothDbManager.get_instance(
        db_type="unsupported_db",
        db_root_path="./data"
    )
except ValueError as e:
    print(f"Invalid configuration: {e}")
```

#### RuntimeError

Raised for plugin initialization failures or connection issues.

```python
try:
    manager = ThothDbManager.get_instance(
        db_type="postgresql",
        db_root_path="./data",
        host="invalid_host"
    )
except RuntimeError as e:
    print(f"Connection failed: {e}")
```

#### ImportError

Raised when required database drivers are not installed.

```python
try:
    manager = ThothDbManager.get_instance(
        db_type="postgresql",
        db_root_path="./data"
    )
except ImportError as e:
    print(f"Missing dependency: {e}")
    print("Install with: pip install psycopg2-binary")
```

## Examples

### Complete PostgreSQL Example

```python
from dbmanager import ThothDbManager

# Create manager
manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    db_mode="production",
    host="localhost",
    port=5432,
    database="myapp",
    user="dbuser",
    password="dbpass"
)

# Explore database structure
print("=== Database Tables ===")
tables = manager.get_tables()
for table in tables:
    print(f"Table: {table['name']}")
    
    # Get columns for each table
    columns = manager.get_columns(table['name'])
    for col in columns:
        pk_flag = " (PK)" if col['is_pk'] else ""
        print(f"  - {col['name']}: {col['data_type']}{pk_flag}")

# Execute queries
print("\n=== Query Results ===")
users = manager.execute_sql("SELECT * FROM users LIMIT 5")
print(f"Found {len(users)} users")

# Get example data
print("\n=== Example Data ===")
examples = manager.get_example_data("users", number_of_rows=10)
for column, values in examples.items():
    print(f"{column}: {values[:3]}...")

# LSH similarity search
print("\n=== LSH Search ===")
similar = manager.query_lsh("john smith", top_n=5)
for table, columns in similar.items():
    for column, values in columns.items():
        if values:  # Only show columns with results
            print(f"Similar values in {table}.{column}:")
            for value in values:
                print(f"  - {value}")
```

### SQLite Example

```python
from dbmanager import ThothDbManager

# Create SQLite manager
manager = ThothDbManager.get_instance(
    db_type="sqlite",
    db_root_path="./data",
    db_mode="dev",
    database_path="./data/myapp.db"
)

# Create a table
manager.execute_sql("""
    CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        price REAL,
        category TEXT
    )
""")

# Insert sample data
manager.execute_sql("""
    INSERT OR REPLACE INTO products (id, name, price, category) VALUES
    (1, 'Laptop Computer', 999.99, 'Electronics'),
    (2, 'Office Chair', 199.99, 'Furniture'),
    (3, 'Smartphone', 699.99, 'Electronics')
""")

# Query data
products = manager.execute_sql("SELECT * FROM products")
print("Products:", products)

# Get table structure
columns = manager.get_columns("products")
for col in columns:
    print(f"Column: {col['name']} ({col['data_type']})")
```

### Factory Pattern Example

```python
from dbmanager import ThothDbFactory

# List available databases
available = ThothDbFactory.list_available_databases()
print(f"Available databases: {available}")

# Get parameter requirements
params = ThothDbFactory.get_required_parameters("mysql")
print(f"MySQL requires: {params['required']}")

# Create manager with validation
try:
    manager = ThothDbFactory.create_with_validation(
        db_type="mysql",
        db_root_path="./data",
        db_mode="dev",
        host="localhost",
        port=3306,
        database="testdb",
        user="testuser",
        password="testpass"
    )
    print("MySQL manager created successfully")
except ValueError as e:
    print(f"Validation failed: {e}")
```

### Document-Based API Example

```python
from dbmanager import ThothDbManager

manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    host="localhost",
    database="mydb",
    user="user",
    password="pass"
)

# Use document-based API if available
if hasattr(manager, 'get_tables_as_documents'):
    table_docs = manager.get_tables_as_documents()
    for doc in table_docs:
        print(f"Table Document:")
        print(f"  ID: {doc.id}")
        print(f"  Name: {doc.table_name}")
        print(f"  Schema: {doc.schema_name}")
        print(f"  Type: {doc.thoth_type.value}")
        print(f"  Text: {doc.text}")
        print(f"  Metadata: {doc.metadata}")

if hasattr(manager, 'get_columns_as_documents'):
    column_docs = manager.get_columns_as_documents("users")
    for doc in column_docs:
        print(f"Column Document:")
        print(f"  Table: {doc.table_name}")
        print(f"  Column: {doc.column_name}")
        print(f"  Type: {doc.data_type}")
        print(f"  Primary Key: {doc.is_pk}")
        print(f"  Nullable: {doc.is_nullable}")
```

This API documentation provides comprehensive coverage of all available classes, methods, and usage patterns in the Thoth SQL Database Manager library.
