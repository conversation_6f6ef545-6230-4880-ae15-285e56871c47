# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import json
import logging
import os
import sys
import django
from pathlib import Path
from django.utils import timezone
from vdbmanager.ThothVectorStore import ThothType, HintDocument
from vdbmanager.impl.QdrantHaystackStore import QdrantHaystackStore

# Add project root to sys.path
project_root = Path(__file__).resolve().parents[2]
sys.path.append(str(project_root))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Thoth.settings')
django.setup()

# Now you can import Django models
from thoth_core.models import Workspace

from thoth_ai_backend.backend_utils.vectordb_config_utils import get_vectordb_config

def upload_hints_to_vectordb(workspace_id=None) -> None:
    """
    Uploads hints from dev.json to the vector database and updates the workspace timestamp.
    
    This function processes hint data from a development JSON file and uploads it to the 
    appropriate vector database collection associated with the specified workspace. It first 
    establishes connections to the workspace's SQL database and vector database, then clears 
    any existing hint documents before uploading new ones. Only hints with matching db_id 
    (corresponding to the collection name) are processed.
    
    The function workflow:
    1. Creates a QdrantVectorStore for the collection based on workspace's SQL DB
    2. Deletes all existing HINT documents
    3. Reads hints from data/dev_databases/dev.json
    4. Creates and uploads HintDocuments for each evidence where db_id matches the workspace's collection
    5. Updates the workspace's last_hint_load timestamp
    
    Args:
        workspace_id (int, optional): The ID of the workspace to determine the collection name
            and database connections. Must be provided.
        
    Raises:
        ValueError: If workspace_id is not provided, if the workspace doesn't exist,
            if the workspace has no SQL database configured, or if the SQL database
            has no vector database configured.
        NotImplementedError: If the vector database type is not supported (currently only Qdrant).
        IOError: If the dev.json file cannot be read.
    
    Returns:
        None: The function updates the vector database and workspace timestamp but doesn't return a value.
    """
    # Check if workspace_id is provided
    if not workspace_id:
        error_msg = "Workspace ID is required"
        logging.error(error_msg)
        raise ValueError(error_msg)
    
    # Get workspace and extract configuration
    try:
        workspace = Workspace.objects.get(id=workspace_id)
    except Workspace.DoesNotExist:
        error_msg = f"Workspace with ID {workspace_id} not found"
        logging.error(error_msg)
        raise ValueError(error_msg)
    
    # Check if SQL DB is configured
    if not workspace.sql_db:
        error_msg = f"Workspace {workspace_id} has no SQL database configured"
        logging.error(error_msg)
        raise ValueError(error_msg)
    
    sql_db = workspace.sql_db
    
    # Convert Django model to dictionary for compatibility with existing code
    sql_db_params = {
        "name": sql_db.name,
        "db_host": sql_db.db_host,
        "db_type": sql_db.db_type,
        "db_name": sql_db.db_name,
        "db_port": sql_db.db_port,
        "schema": sql_db.schema,
        "user_name": sql_db.user_name,
        "password": sql_db.password,
        "db_mode": sql_db.db_mode,
        "language": sql_db.language,
    }
    
    # Check if vector DB is configured
    if not sql_db.vector_db:
        error_msg = f"SQL DB {sql_db.name} has no vector database configured"
        logging.error(error_msg)
        raise ValueError(error_msg)
    
    vector_db_obj = sql_db.vector_db
    sql_db_params["vector_db"] = {
        "name": vector_db_obj.name,
        "vect_type": vector_db_obj.vect_type,
        "host": vector_db_obj.host,
        "port": vector_db_obj.port,
        "username": vector_db_obj.username,
        "password": vector_db_obj.password,
        "api_key": vector_db_obj.api_key,
        "tenant": vector_db_obj.tenant,
    }
    
    # Get vector database configuration
    vector_db_config = get_vectordb_config(sql_db_params)
    
    # Initialize the appropriate vector store based on the vector database type
    if vector_db_config["vector_db_type"] == "Qdrant":
        vector_db=QdrantHaystackStore(collection=vector_db_config.get('collection_name'),
                                                   host=vector_db_config.get('host'),
                                                   port=vector_db_config.get('port'))
    else:
        error_msg = f"Unsupported vector database type: {vector_db_config['vector_db_type']}"
        logging.error(error_msg)
        raise NotImplementedError(error_msg)
    
    logging.info(f"Using vector database: {vector_db_config['vector_db_type']}, "
                 f"collection: {vector_db_config.get('collection_name')}, "
                 f"host: {vector_db_config.get('host')}, "
                 f"port: {vector_db_config.get('port')}")
    
    # Delete all existing HINT documents
    existing_hints = vector_db.get_documents_by_type(ThothType.HINT, HintDocument)
    if existing_hints:
        hint_ids = [doc.id for doc in existing_hints]
        vector_db.delete_documents(hint_ids)
        logging.info(f"Deleted {len(hint_ids)} existing hint documents")
    
    # Read dev.json file
    project_root = Path(__file__).resolve().parents[2]
    
    # Check for DB_ROOT_PATH environment variable
    db_root_path_val = os.getenv("DB_ROOT_PATH")
    if not db_root_path_val:
        error_msg = "Environment variable DB_ROOT_PATH is not set."
        logging.error(error_msg)
        raise ValueError(error_msg)
        
    # Check if the base directory specified in DB_ROOT_PATH exists
    db_root_dir = project_root / db_root_path_val
    if not db_root_dir.is_dir():
        error_msg = f"The directory specified by DB_ROOT_PATH does not exist: {db_root_dir}"
        logging.error(error_msg)
        raise ValueError(error_msg)
        
    db_mode_val = sql_db.db_mode
    dev_json_path = db_root_dir / f"{db_mode_val}_databases" / f"{db_mode_val}.json"
    
    # Check if dev_json_path exists and is a file
    if not dev_json_path.is_file():
        error_msg = f"The JSON file specified by dev_json_path does not exist: {dev_json_path}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    try:
        with open(dev_json_path, 'r', encoding='utf-8') as f:
            dev_data = json.load(f)
    except json.JSONDecodeError as e:
        error_msg = f"Failed to decode JSON from {dev_json_path}: {str(e)}"
        logging.error(error_msg)
        raise ValueError(error_msg) # Changed from IOError to ValueError for consistency
    except Exception as e: # Catch other potential IOErrors
        error_msg = f"Failed to read dev.json file at {dev_json_path}: {str(e)}"
        logging.error(error_msg)
        raise IOError(error_msg)
    
    # Use collection name as db_id for filtering
    db_id = sql_db.db_name
    
    successful_uploads = 0
    failed_uploads = 0
    
    # Process each entry, filtering for the specified db_id
    for entry in dev_data:
        if entry.get("db_id") != db_id:
            continue
            
        evidence = entry.get("evidence")
        if not evidence or not isinstance(evidence, str):
            logging.warning(f"Skipping invalid evidence entry: {entry}")
            continue

        hint_doc = HintDocument(
            hint=evidence.strip(),
            thoth_type=ThothType.HINT,
            text=""
        )
        
        try:
            vector_db.add_hint(hint_doc)
            successful_uploads += 1
            logging.info(f"Successfully uploaded hint: {evidence[:100]}...")
        except Exception as e:
            failed_uploads += 1
            logging.error(f"Failed to upload hint: {evidence[:100]}... Error: {str(e)}")
    
    # Clear the cache to ensure fresh data on next retrieval
    #get_cached_training_data.clear(ThothType.HINT) - sostituire con equivalente streamlit free
    
    # Update the last_hint_load timestamp in the workspace
    workspace.last_hint_load = timezone.now()
    workspace.save()
    logging.info(f"Updated last_hint_load timestamp for workspace {workspace.name} (ID: {workspace_id})")
    
    # Log summary
    logging.info(f"Hint upload complete. Successful: {successful_uploads}, Failed: {failed_uploads}")
    
    return successful_uploads

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Upload hints to vector database")
    parser.add_argument("--workspace_id", type=int, required=True, help="Workspace ID to determine collection name")
    
    args = parser.parse_args()
    
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    try:
        upload_hints_to_vectordb(workspace_id=args.workspace_id)
    except Exception as e:
        logging.error(f"Error in upload_hints_to_vectordb: {str(e)}")
        sys.exit(1)
