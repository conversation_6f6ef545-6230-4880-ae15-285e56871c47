# Supabase Support Documentation

## Overview

Thoth SQL Database Manager now includes comprehensive support for **Supabase**, a PostgreSQL-based backend-as-a-service platform. This support extends the existing PostgreSQL functionality with Supabase-specific features and provides two connection modes:

1. **Direct Database Connection** - Traditional PostgreSQL connection with SSL enforcement
2. **REST API Connection** - Modern REST API access via Supabase client

## Architecture

The Supabase support is implemented through three main components:

### 1. ThothSupabaseManager (`dbmanager/impl/ThothSupabaseManager.py`)
- **Purpose**: Main Supabase manager class extending ThothDbManager
- **Features**:
  - Singleton pattern for connection management
  - SSL enforcement for security
  - Connection pooling optimization
  - Support for both direct and REST API connections
  - Supabase-specific table filtering (excludes system tables)

### 2. SupabasePlugin (`dbmanager/plugins/supabase.py`)
- **Purpose**: Plugin system integration
- **Features**:
  - Plugin registration and validation
  - Parameter validation for both connection modes
  - LSH (Locality-Sensitive Hashing) integration
  - Backward compatibility support

### 3. SupabaseAdapter (`dbmanager/adapters/supabase.py`)
- **Purpose**: Database adapter for Supabase-specific operations
- **Features**:
  - Extends PostgreSQL adapter
  - SSL configuration
  - System table filtering
  - REST API integration

## Connection Modes

### Direct Database Connection
Uses traditional PostgreSQL connection with enhanced security:

```python
from dbmanager.impl.ThothSupabaseManager import ThothSupabaseManager

# Create manager instance
manager = ThothSupabaseManager.get_instance(
    host="your-project.supabase.co",
    port=5432,
    dbname="postgres",
    user="postgres",
    password="your-password",
    db_root_path="/path/to/data",
    schema="public"
)

# Use standard operations
tables = manager.get_tables()
columns = manager.get_columns("users")
```

### REST API Connection
Uses Supabase's REST API for modern applications:

```python
from dbmanager.impl.ThothSupabaseManager import ThothSupabaseManager

# Create manager instance
manager = ThothSupabaseManager.get_instance(
    project_url="https://your-project.supabase.co",
    api_key="your-anon-key",
    db_root_path="/path/to/data",
    use_rest_api=True
)

# Use standard operations
tables = manager.get_tables()
data = manager.get_example_data("users", 10)
```

## Factory Integration

### Using the Factory
```python
from dbmanager.core.factory import ThothDbFactory

# Create via factory
manager = ThothDbFactory.create_manager(
    db_type="supabase",
    db_root_path="/path/to/data",
    host="your-project.supabase.co",
    port=5432,
    database="postgres",
    user="postgres",
    password="your-password"
)

# Or with validation
manager = ThothDbFactory.create_with_validation(
    db_type="supabase",
    db_root_path="/path/to/data",
    host="your-project.supabase.co",
    port=5432,
    database="postgres",
    user="postgres",
    password="your-password"
)
```

### Configuration-based Creation
```python
from dbmanager.core.factory import ThothDbFactory

config = {
    "db_type": "supabase",
    "db_root_path": "/path/to/data",
    "host": "your-project.supabase.co",
    "port": 5432,
    "database": "postgres",
    "user": "postgres",
    "password": "your-password",
    "schema": "public"
}

manager = ThothDbFactory.create_from_config(config)
```

## Required Dependencies

Add these to your `requirements.txt`:
```
supabase
postgrest-py
gotrue-py
```

## Security Features

### SSL Enforcement
- All direct connections use `sslmode=require`
- SSL certificates can be provided via parameters
- Connection pooling with default settings

### System Table Filtering
Automatically excludes Supabase system tables:
- `auth_*` tables
- `storage_*` tables
- `realtime_*` tables
- `supabase_functions_*` tables

## Configuration Parameters

### Direct Connection Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `host` | string | Yes | Supabase host (e.g., `your-project.supabase.co`) |
| `port` | int | Yes | Database port (usually 5432) |
| `database` | string | Yes | Database name (usually `postgres`) |
| `user` | string | Yes | Database username |
| `password` | string | Yes | Database password |
| `schema` | string | No | Database schema (default: `public`) |
| `sslmode` | string | No | SSL mode (default: `require`) |
| `sslcert` | string | No | SSL certificate path |
| `sslkey` | string | No | SSL key path |
| `sslrootcert` | string | No | SSL root certificate path |

### REST API Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `project_url` | string | Yes | Supabase project URL |
| `api_key` | string | Yes | Supabase anon key |
| `schema` | string | No | Database schema (default: `public`) |
| `use_rest_api` | bool | No | Enable REST API mode (must be `True`) |

## Usage Examples

### Basic Operations
```python
# Get all tables (excluding system tables)
tables = manager.get_tables()

# Get table schema
schema = manager.get_table_schema("users")

# Get column information
columns = manager.get_columns("users")

# Get example data
data = manager.get_example_data("users", 10)

# Get foreign keys
fks = manager.get_foreign_keys()

# Get unique values
unique_values = manager.get_unique_values()
```

### LSH Integration (Backward Compatibility)
```python
# Set up LSH
result = manager.set_lsh()

# Query LSH
results = manager.query_lsh("search_term", top_n=5)
```

### SQL Execution
```python
# Execute custom SQL
result = manager.execute_sql("SELECT * FROM users LIMIT 10")

# Execute with parameters
result = manager.execute_sql(
    "SELECT * FROM users WHERE age > :min_age",
    params={"min_age": 18}
)
```

## Testing

Run the comprehensive test suite:
```bash
pytest tests/test_thoth_supabase_manager.py -v
```

## Migration from PostgreSQL

Existing PostgreSQL code can be easily migrated to Supabase:

### Before (PostgreSQL)
```python
from dbmanager.impl.ThothPgManager import ThothPgManager

manager = ThothPgManager.get_instance(
    host="localhost",
    port=5432,
    dbname="mydb",
    user="user",
    password="pass",
    db_root_path="/data"
)
```

### After (Supabase)
```python
from dbmanager.impl.ThothSupabaseManager import ThothSupabaseManager

manager = ThothSupabaseManager.get_instance(
    host="your-project.supabase.co",
    port=5432,
    dbname="postgres",
    user="postgres",
    password="your-password",
    db_root_path="/data"
)
```

## Error Handling

### Common Errors and Solutions

1. **Connection Refused**
   - Ensure Supabase project is running
   - Check firewall settings
   - Verify SSL certificates

2. **Authentication Failed**
   - Verify username/password for direct connection
   - Check API key for REST API connection

3. **SSL Certificate Issues**
   - Provide valid SSL certificates
   - Use `sslmode=require` for production

4. **Missing Dependencies**
   - Install required packages: `pip install supabase postgrest-py gotrue-py`

## Best Practices

1. **Use Direct Connection for**: Heavy queries, bulk operations, complex joins
2. **Use REST API for**: Simple CRUD operations, real-time applications, client-side usage
3. **Security**: Always use SSL in production
4. **Performance**: Use connection pooling for high-traffic applications
5. **Schema Management**: Use migrations for schema changes

## Troubleshooting

### Debug Mode
Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Connection Testing
```python
# Test direct connection
try:
    manager = ThothSupabaseManager.get_instance(
        host="your-project.supabase.co",
        port=5432,
        dbname="postgres",
        user="postgres",
        password="your-password",
        db_root_path="/tmp"
    )
    print("Connection successful!")
except Exception as e:
    print(f"Connection failed: {e}")
```

### REST API Testing
```python
# Test REST API
try:
    manager = ThothSupabaseManager.get_instance(
        project_url="https://your-project.supabase.co",
        api_key="your-anon-key",
        db_root_path="/tmp",
        use_rest_api=True
    )
    print("REST API connection successful!")
except Exception as e:
    print(f"REST API connection failed: {e}")
