# Docker Exports Configuration Guide

## Overview

This document explains the Docker volume configuration for the Thoth exports functionality and provides troubleshooting guidance for common issues.

## Docker Configuration

### Current docker-compose.yml Setup

The exports directory is properly configured in the docker-compose.yml:

```yaml
services:
  app:
    volumes:
      - ./exports:/app/exports  # Exports volume binding
      - ./setup_csv:/app/setup_csv  # Setup CSV (read-only operations)
```

### Directory Structure

```
Thoth/
├── exports/           # Admin actions import/export (Docker mounted)
├── setup_csv/         # Management commands setup data
│   ├── local/         # Local environment setup files
│   └── docker/        # Docker environment setup files
└── docker-compose.yml
```

## Admin Actions vs Management Commands

### Admin Actions (Web Interface)
- **Directory**: `exports/`
- **Usage**: Daily operations via Django admin interface
- **Functions**: `export_csv()`, `import_csv()`, `export_db_structure_to_csv()`
- **Docker**: Requires volume mounting for persistence

### Management Commands (CLI)
- **Directory**: `setup_csv/`
- **Usage**: Initial setup and bulk operations via `python manage.py`
- **Functions**: `import_all_csv`, `import_db_structure`, etc.
- **Docker**: Files included in container build

## Error Handling

### Enhanced Docker Error Messages

The system now provides specific error messages for common Docker issues:

#### Permission Denied
```
Docker volume permissions issue. The exports directory is not writable.
Check that the Docker volume is properly mounted with write permissions.
Try: 'chmod 755 ./exports' on the host system.
```

#### Read-Only File System
```
Docker volume not properly mounted as writable.
Check your docker-compose.yml configuration.
The exports volume should be mounted as: './exports:/app/exports:rw'
```

#### No Space Left
```
Insufficient disk space on Docker host.
Free up space on the host system or check Docker volume configuration.
```

#### Directory Not Found
```
Docker volume mount path issue.
Ensure the exports directory exists on the host system: 'mkdir -p ./exports'
```

## Troubleshooting

### 1. Create Exports Directory

Before starting Docker containers:

```bash
mkdir -p ./exports
chmod 755 ./exports
```

### 2. Verify Volume Mounting

Check if the volume is properly mounted:

```bash
docker exec -it thoth-be ls -la /app/exports
```

### 3. Test Write Permissions

Test write access from inside the container:

```bash
docker exec -it thoth-be touch /app/exports/test.txt
docker exec -it thoth-be rm /app/exports/test.txt
```

### 4. Check Host Directory Permissions

On the host system:

```bash
ls -la ./exports
# Should show: drwxr-xr-x ... exports
```

### 5. Fix Permission Issues

If you encounter permission issues:

```bash
# On the host system
sudo chown -R $USER:$USER ./exports
chmod -R 755 ./exports
```

## Environment Variables

### IO_DIR Configuration

You can customize the exports directory using environment variables:

```yaml
# docker-compose.yml
services:
  app:
    environment:
      - IO_DIR=custom_exports  # Default: 'exports'
    volumes:
      - ./custom_exports:/app/custom_exports
```

## Production Deployment

### Recommended Configuration

```yaml
services:
  app:
    volumes:
      # Explicit read-write mounting
      - ./exports:/app/exports:rw
      # Read-only for setup files
      - ./setup_csv:/app/setup_csv:ro
    environment:
      - IO_DIR=exports
      - IS_DOCKER=True
```

### Backup Considerations

The exports directory contains user-generated data and should be included in backups:

```bash
# Backup exports
tar -czf exports_backup_$(date +%Y%m%d).tar.gz ./exports

# Restore exports
tar -xzf exports_backup_YYYYMMDD.tar.gz
```

## Testing the Configuration

### 1. Start the Application

```bash
docker-compose up -d
```

### 2. Access Django Admin

Navigate to the admin interface and try to export some data.

### 3. Verify File Creation

Check that files are created on the host:

```bash
ls -la ./exports/
```

### 4. Test Persistence

Restart the container and verify files persist:

```bash
docker-compose restart app
ls -la ./exports/  # Files should still be there
```

## Common Issues and Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| Files disappear after restart | Volume not mounted | Check docker-compose.yml volume configuration |
| Permission denied errors | Wrong file permissions | `chmod 755 ./exports` on host |
| Cannot create directory | Directory doesn't exist | `mkdir -p ./exports` before starting |
| Read-only file system | Volume mounted as read-only | Ensure volume is mounted as `:rw` |

## Implementation Details

The enhanced error handling includes:

1. **Directory Validation**: `ensure_exports_directory()` function
2. **Write Testing**: Actual write test before operations
3. **Docker-Specific Messages**: User-friendly error explanations
4. **Graceful Degradation**: Operations fail safely with clear feedback

This ensures robust operation in Docker environments while providing clear guidance for troubleshooting configuration issues.
