(i=>{function t(){this.$body=i("body"),this.$chatInput=i(".chat-input"),this.$chatList=i(".conversation-list"),this.$chatSendBtn=i(".chat-send"),this.$chatForm=i("#chat-form")}t.prototype.save=function(){var t=this.$chatInput.val(),a=moment().format("h:mm");return""==t?(this.$chatInput.focus(),!1):(i('<li class="clearfix odd"><div class="chat-avatar"><img src="assets/images/users/avatar-1.jpg" alt="male"><i>'+a+'</i></div><div class="conversation-text"><div class="ctext-wrap"><i>Dominic</i><p>'+t+"</p></div></div></li>").appendTo(".conversation-list"),this.$chatInput.focus(),this.$chatList.animate({scrollTop:this.$chatList.prop("scrollHeight")},1e3),!0)},t.prototype.init=function(){var a=this;a.$chatInput.keypress(function(t){if(13==t.which)return a.save(),!1}),a.$chatForm.on("submit",function(t){return t.preventDefault(),a.save(),a.$chatForm.removeClass("was-validated"),a.$chatInput.val(""),!1})},i.ChatApp=new t,i.ChatApp.Constructor=t})(window.jQuery),window.jQuery.ChatApp.init();