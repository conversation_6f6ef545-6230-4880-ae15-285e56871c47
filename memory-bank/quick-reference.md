# Quick Reference Guide

## Database Manager Creation

### Using Factory Pattern
```python
from dbmanager.core.factory import DatabaseManagerFactory

# Create SQLite manager
manager = DatabaseManagerFactory.create_manager('sqlite', database='test.db')

# Create PostgreSQL manager
manager = DatabaseManagerFactory.create_manager(
    'postgresql',
    host='localhost',
    database='test_db',
    user='user',
    password='pass'
)
```

### Supported Database Types
- `sqlite`
- `postgresql`
- `mysql`
- `mariadb`
- `oracle`
- `sqlserver`
- `informix`

## Common Operations

### Basic CRUD Operations
```python
# Insert
manager.insert('table_name', {'column1': 'value1', 'column2': 'value2'})

# Select
results = manager.select('table_name', where={'column1': 'value1'})

# Update
manager.update('table_name', {'column1': 'new_value'}, where={'id': 1})

# Delete
manager.delete('table_name', where={'id': 1})
```

### LSH Operations
```python
# Create LSH index
lsh_manager = manager.get_lsh_manager()
lsh_manager.create_index('table_name', 'column_name')

# Find similar records
similar = lsh_manager.find_similar('table_name', 'column_name', query_value)
```

### Transaction Management
```python
with manager.transaction():
    manager.insert('table1', data1)
    manager.insert('table2', data2)
```

## Configuration Examples

### SQLite Configuration
```python
config = {
    'database': 'path/to/database.db',
    'check_same_thread': False
}
```

### PostgreSQL Configuration
```python
config = {
    'host': 'localhost',
    'port': 5432,
    'database': 'test_db',
    'user': 'user',
    'password': 'pass'
}
```

### MySQL Configuration
```python
config = {
    'host': 'localhost',
    'port': 3306,
    'database': 'test_db',
    'user': 'user',
    'password': 'pass'
}
```

## Testing Commands

### Run All Tests
```bash
pytest tests/
```

### Run Specific Tests
```bash
pytest tests/test_thoth_sqlite_manager.py -v
```

### Run with Coverage
```bash
pytest --cov=dbmanager tests/
```

## Environment Variables

### Database Configuration
```bash
export THOTH_DB_TYPE=postgresql
export THOTH_DB_HOST=localhost
export THOTH_DB_PORT=5432
export THOTH_DB_NAME=thoth_test
export THOTH_DB_USER=test_user
export THOTH_DB_PASSWORD=test_pass
```

### LSH Configuration
```bash
export THOTH_LSH_PERMUTATIONS=128
export THOTH_LSH_THRESHOLD=0.8
```

## File Locations

### Core Files
- **Factory**: `dbmanager/core/factory.py`
- **Interfaces**: `dbmanager/core/interfaces.py`
- **Base Manager**: `dbmanager/ThothDbManager.py`

### Database Implementations
- **SQLite**: `dbmanager/impl/ThothSqliteManager.py`
- **PostgreSQL**: `dbmanager/impl/ThothPgManager.py`
- **MySQL**: `dbmanager/impl/ThothMySqlManager.py`

### Tests
- **All Tests**: `tests/`
- **Test Data**: `tests/test_data/`
- **Test DB**: `tests/test_db/`

## Troubleshooting Commands

### Check Database Connection
```python
manager.test_connection()
```

### Verify LSH Setup
```python
lsh_manager = manager.get_lsh_manager()
print(lsh_manager.get_index_info('table_name'))
```

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)
