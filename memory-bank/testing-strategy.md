# Testing Strategy

## Test Structure

### Unit Tests
- **Location**: `tests/`
- **Naming**: `test_*.py`
- **Coverage**: Individual components

### Integration Tests
- **Location**: `tests/test_integration_new_architecture.py`
- **Purpose**: End-to-end testing
- **Databases**: All supported types

### Test Data
- **Location**: `tests/test_data/`
- **Generated Data**: `tests/generate_test_lsh_data.py`
- **Test Databases**: `tests/test_db/`

## Test Categories

### 1. Database Manager Tests
- **File**: `test_thoth_db_manager_base.py`
- **Purpose**: Base functionality testing
- **Coverage**: Core operations

### 2. Database-Specific Tests
- **SQLite**: `test_thoth_sqlite_manager.py`
- **PostgreSQL**: `test_thoth_pg_manager.py`
- **MySQL**: `test_thoth_mysql_manager.py`
- **MariaDB**: `test_thoth_mariadb_manager.py`
- **Oracle**: `test_thoth_oracle_manager.py`
- **SQL Server**: `test_thoth_sqlserver_manager.py`
- **Informix**: `test_thoth_informix_manager.py`

### 3. LSH Tests
- **File**: `test_lsh_query.py`
- **Purpose**: Similarity search testing
- **Coverage**: MinHash generation, LSH indexing

### 4. Parameter Validation Tests
- **File**: `test_parameter_validation.py`
- **Purpose**: Input validation testing
- **Coverage**: Edge cases, error handling

### 5. New Architecture Tests
- **File**: `test_new_architecture.py`
- **Purpose**: Factory pattern testing
- **Coverage**: Registry, manager creation

## Test Execution

### Running All Tests
```bash
python -m pytest tests/
```

### Running Specific Database Tests
```bash
python -m pytest tests/test_thoth_sqlite_manager.py
```

### Running Integration Tests
```bash
python -m pytest tests/test_integration_new_architecture.py
```

## Test Data Generation
- **Script**: `tests/generate_test_lsh_data.py`
- **Purpose**: Create test data for LSH functionality
- **Output**: Pickle files in `tests/test_db/preprocessed/`

## Coverage Requirements
- **Minimum**: 80% code coverage
- **Target**: 90% code coverage
- **Current**: ~85% overall coverage
