# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth.models import User, Group
from django.db.models.signals import post_save
from django.dispatch import receiver

class AgentChoices(models.TextChoices):
    GENERATESQLBASIC = 'GENERATESQLBASIC', 'Generate SQL Basic'
    DEFAULT = 'DEFAULT', 'Default'
    EXTRACTKEYWORDS = 'EXTRACTKEYWORDS', 'Extract Keywords'
    SELECTCOLUMNS = 'SELECTCOLUMNS', 'Select Columns'
    SQLBASIC = 'SQLBASIC', 'Sql - Basic'
    SQLADVANCED = 'SQLADVANCED', 'Sql - Advanced'
    SQLEXPERT = 'SQLEXPERT', 'Sql - Expert'
    SQLTITAN = 'SQLTITAN', 'Sql - Titan'
    TESTGENERATOR= 'TESTGENERATOR', 'Test Generator'
    TESTEXECUTOR = 'TESTEXECUTOR', 'Test Executor'
    EXPLAINSQL = 'EXPLAINSQL', 'Explain SQL'
    ASKFORHUMANHELP = 'ASKFORHUMANHELP', 'Ask For Human Help'
    VALIDATEQUESTION = 'VALIDATEQUESTION', 'Validate Question'

class LLMChoices(models.TextChoices):
    OPENAI  = 'OPENAI', 'OpenAI'
    CLAUDE = 'ANTHROPIC', 'Anthropic'
    CODESTRAL = 'CODESTRAL', 'Codestral'
    DEEPSEEK= 'DEEPSEEK', 'DeepSeek'
    LLAMA = 'META', 'LLama'
    LMSTUDIO = 'LMSTUDIO', 'LM Studio'
    MISTRAL = 'MISTRAL', 'Mistral'
    OLLAMA = 'OLLAMA', 'Ollama'
    OPENROUTER= 'OPENROUTER', 'OpenRouter'
    GEMINI = 'GEMINI', 'Gemini'

class DBMODEChoices(models.TextChoices):
    DEV = 'dev', 'dev',
    TEST = 'test', 'test',
    PROD = 'prod', 'prod'

class SQLDBChoices(models.TextChoices):
    MARIADB = 'MariaDB', 'MariaDB'
    MYSQL = 'MySQL', 'MySQL'
    ORACLE = 'Oracle', 'Oracle'
    POSTGRES = 'PostgreSQL', 'PostgreSQL'
    SQLSERVER = 'SQLServer', 'SQLServer'
    SQLITE = 'SQLite', 'SQLite'

class ColumnDataTypes(models.TextChoices):
    INT = 'INT', 'INT'
    FLOAT = 'FLOAT', 'FLOAT'
    DOUBLE = 'DOUBLE', 'DOUBLE'
    DECIMAL = 'DECIMAL', 'DECIMAL'
    VARCHAR = 'VARCHAR', 'VARCHAR'
    CHAR = 'CHAR', 'CHAR'
    DATE = 'DATE', 'DATE'
    TIME = 'TIME', 'TIME'
    TIMESTAMP = 'TIMESTAMP', 'TIMESTAMP'
    BOOLEAN = 'BOOLEAN', 'BOOLEAN'
    ENUM = 'ENUM', 'ENUM'

class VectorDbChoices(models.TextChoices):
    CHROMA = 'ChromaDB', 'ChromaDB'
    ELASTICSEARCH = 'Elasticsearch', 'Elasticsearch'
    FAISS = 'FAISS', 'FAISS'
    MILVUS = 'Milvus', 'Milvus'
    MONGODB = 'MongoDB', 'MongoDB'
    PINECONE = 'Pinecone', 'Pinecone'
    PGVECTOR = 'PGVector', 'PGVector'
    QDRANT= 'Qdrant', 'Qdrant'
    WEAVIETE = 'Weaviate', 'Weaviate'

class BasicAiModel(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    provider = models.CharField(max_length=100, choices=LLMChoices.choices, default=LLMChoices.CLAUDE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class AiModel(models.Model):
    basic_model = models.ForeignKey(BasicAiModel, on_delete=models.CASCADE, related_name='ai_models', null=True)
    specific_model = models.CharField(max_length=100, null=False,  default='')
    name = models.CharField(max_length=100, null=True, blank=False)
    api_key = models.CharField(max_length=255, null=True, blank=True, verbose_name='API Key')
    url = models.URLField(null=True, blank=True)
    temperature_allowed = models.BooleanField(default=True)
    temperature = models.DecimalField(
        help_text="""
        What sampling temperature to use, between 0 and 2. 
        Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. 
        We generally recommend altering this or top_p, not both simultaneously.
        """,
        max_digits=3, decimal_places=2,
        default=0.8,
        validators=[MinValueValidator(0), MaxValueValidator(2)],
        verbose_name='Temperature'
    )
    top_p = models.DecimalField(
        help_text="""
        Top-P changes how the model selects tokens for output. Tokens are selected from the most (see top-K) to least probable until the sum of their probabilities equals the top-P value. For example, if tokens A, B, and C have a probability of 0.3, 0.2, and 0.1 and the top-P value is 0.5, then the model will select either A or B as the next token by using temperature and excludes C as a candidate.
        Specify a lower value for less random responses and a higher value for more random responses.
        """,
        max_digits=3, decimal_places=2,
        default=0.9,
        validators=[MinValueValidator(0), MaxValueValidator(1)],
        verbose_name='Top P [0.00 .. 1.00] - (0.90)'
    )
    max_tokens = models.IntegerField(
        help_text="""
        Maximum number of tokens that can be generated in the response. 
        A token is approximately four characters. 100 tokens correspond to roughly 60-80 words.
        Specify a lower value for shorter responses and a higher value for potentially longer responses.""",
        default=1280,
        validators=[MinValueValidator(128), MaxValueValidator(16000)],
        verbose_name='Max Tokens [-2 .. 16000] (128)'
    )
    timeout = models.FloatField(
        help_text="""
        Timeouts take place if this threshold, expressed in seconds, is exceeded:""",
        default=45.0,
        validators=[MinValueValidator(1.0), MaxValueValidator(3600.0)],
        verbose_name='Timeout'
    )
    def __str__(self):
        #ritorna la concatenazione di name e specific_model
        return f"{self.name} - {self.specific_model}"

class Agent(models.Model):
    name = models.CharField(max_length=255, null=False, blank=False)
    agent_type = models.CharField(max_length=255, choices=AgentChoices.choices, default=AgentChoices.EXTRACTKEYWORDS)
    ai_model = models.ForeignKey(AiModel, on_delete=models.CASCADE, related_name='agents', null=True)
    temperature = models.DecimalField(
        help_text="""
        What sampling temperature to use, between 0 and 2. 
        Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. 
        We generally recommend altering this or top_p, not both simultaneously.
        """,
        max_digits=3, decimal_places=2,
        default=0.8,
        validators=[MinValueValidator(0), MaxValueValidator(2)],
        verbose_name='Temperature')
    top_p = models.DecimalField(
        help_text="""
        Top-P changes how the model selects tokens for output.""",
        max_digits=3, decimal_places=2,
        default=0.95,
        validators=[MinValueValidator(0), MaxValueValidator(1)])
    max_tokens = models.IntegerField(
        help_text="""
        The maximum number of tokens to generate.""",
        default=1280)
    timeout = models.FloatField(
        help_text="""
        Timeouts take place if this threshold, expressed in seconds, is exceeded:
        """,
        default=45.0,
        validators=[MinValueValidator(1.0), MaxValueValidator(3600.0)],
        verbose_name='Timeout')
    system_prompt = models.TextField(blank=True)
    user_prompt = models.TextField(blank=True)
    retries = models.IntegerField(default=1)

    def __str__(self):
        return self.name

class SqlDb(models.Model):
    name = models.CharField(max_length=255)
    db_host = models.CharField(max_length=255, blank=True)
    db_type = models.CharField(max_length=255, choices=SQLDBChoices, default=SQLDBChoices.POSTGRES)
    db_name = models.CharField(max_length=255)
    db_port = models.IntegerField(blank=True,null=True)
    schema = models.CharField(max_length=255, blank=True)
    user_name = models.CharField(max_length=255,blank=True)
    password = models.CharField(max_length=255,blank=True)
    db_mode = models.CharField(max_length=255, choices=DBMODEChoices, default=DBMODEChoices.DEV)
    vector_db = models.OneToOneField('VectorDb', on_delete=models.SET_NULL, null=True, blank=True, )
    language = models.CharField(max_length=50, blank=True,default='English')
    scope = models.TextField(blank=True, null=True)
    last_columns_update= models.DateTimeField(blank=True, null=True)

    def get_collection_name(self):
        if not self.schema or self.schema == 'public':
            return self.name
        return f"{self.schema}__{self.name}"

    def __str__(self):
        return f"{self.name} - {self.db_name}"

class SqlTable(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    generated_comment = models.TextField(blank=True)
    sql_db = models.ForeignKey(SqlDb, on_delete=models.CASCADE, related_name='tables')

    def __str__(self):
        return self.name

class SqlColumn(models.Model):
    original_column_name = models.CharField(max_length=255)
    column_name = models.CharField(max_length=255, blank=True)
    data_format = models.CharField(max_length=255, choices=ColumnDataTypes, default=ColumnDataTypes.VARCHAR)
    column_description = models.TextField(blank=True)
    generated_comment = models.TextField(blank=True)
    value_description = models.TextField(blank=True)
    pk_field = models.TextField(blank=True)
    fk_field = models.TextField(blank=True)
    sql_table = models.ForeignKey(SqlTable, on_delete=models.CASCADE, related_name='columns')

class Relationship(models.Model):
    source_table = models.ForeignKey(SqlTable, on_delete=models.CASCADE, related_name='source_tables')
    target_table = models.ForeignKey(SqlTable, on_delete=models.CASCADE, related_name='target_tables')
    source_column = models.ForeignKey(SqlColumn, on_delete=models.CASCADE, related_name='source_columns')
    target_column = models.ForeignKey(SqlColumn, on_delete=models.CASCADE, related_name='target_columns')

    @staticmethod
    def update_pk_fk_fields():
        # Dizionario per tenere traccia delle relazioni FK
        fk_relations = {}

        # Raccogliamo tutte le relazioni
        for rel in Relationship.objects.all():
            source_col = rel.source_column
            target_col = rel.target_column

            # Aggiorniamo solo le relazioni FK
            if source_col.id not in fk_relations:
                fk_relations[source_col.id] = set()
            fk_relations[source_col.id].add(f"{target_col.sql_table.name}.{target_col.original_column_name}")

        # Aggiorniamo solo i campi fk_field
        for col_id, references in fk_relations.items():
            col = SqlColumn.objects.get(id=col_id)
            col.fk_field = f"{', '.join(references)}"
            col.save()

class VectorDb(models.Model):
    name = models.CharField(max_length=255, verbose_name='Collection Name')
    vect_type= models.CharField(max_length=255, choices=VectorDbChoices, default=VectorDbChoices.QDRANT)
    host = models.CharField(max_length=255, blank=True)
    port = models.IntegerField(blank=True, null=True)
    username = models.CharField(max_length=255, blank=True)
    password = models.CharField(max_length=255, blank=True)
    api_key = models.CharField(max_length=255, blank=True)
    path = models.CharField(max_length=500, blank=True)
    tenant = models.CharField(max_length=255, blank=True)

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.vect_type != VectorDbChoices.QDRANT:
            raise ValidationError(f"Vector database type {self.vect_type} is not currently supported")

    def __str__(self):
        return f"{self.name} - {self.vect_type}"

class Setting(models.Model):
    name = models.CharField(max_length=255)
    theme = models.CharField(max_length=50, null=True, blank=True)

    language = models.CharField(max_length=50)
    example_rows_for_comment = models.PositiveIntegerField(default=5, help_text="Number of example rows to use for comment generation")
    system_prompt = models.TextField(null=True, blank=True)
    comment_model = models.ForeignKey(AiModel, on_delete=models.SET_NULL, null=True, related_name='setting_comment_models')

    signature_size = models.IntegerField(default=30, help_text="Size of the signature - LSH")
    n_grams = models.IntegerField(default=3, help_text="Number of n-grams - LSH")
    threshold = models.FloatField(default=0.01, help_text="Threshold value for similarity comparison - LSH")
    verbose = models.BooleanField(default=True, help_text="Enable verbose mode for LHS preprocessing")
    use_value_description = models.BooleanField(default=True, help_text="Enable verbose mode for similarity comparison - LSH")

    def __str__(self):
        return self.name

class Workspace(models.Model):
    class PreprocessingStatus(models.TextChoices):
        IDLE = 'IDLE', 'Idle'
        RUNNING = 'RUNNING', 'Running'
        COMPLETED = 'COMPLETED', 'Completed'
        FAILED = 'FAILED', 'Failed'

    class WorkspaceLevel(models.TextChoices):
        BASIC = 'BASIC', 'Basic'
        ADVANCED = 'ADVANCED', 'Advanced'
        EXPERT = 'EXPERT', 'Expert'
        TITANIC = 'TITANIC', 'Titanic'

    name = models.CharField(max_length=100, null=False)
    level = models.CharField(max_length=20, choices=WorkspaceLevel.choices, default=WorkspaceLevel.BASIC)
    description = models.TextField(blank=True)
    users = models.ManyToManyField(User, related_name='workspaces')
    default_workspace = models.ManyToManyField(User, related_name='default_workspaces', blank=True)
    sql_db = models.ForeignKey(SqlDb, on_delete=models.SET_NULL, null=True, related_name='workspaces')
    default_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_default')
    question_validator = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_question_validator')
    kw_sel_agent_1 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_kw1')
    kw_sel_agent_2 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_kw2')
    sel_columns_agent_1 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sel_col_1')
    sel_columns_agent_2 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sel_col_2')
    sql_basic_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sql_basic')
    sql_advanced_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sql_advanced')
    sql_expert_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sql_expert')
    sql_titan_agent_1 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sql_titan_1')
    sql_titan_agent_2 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sql_titan_2')
    sql_titan_agent_3 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_sql_titan_3')
    test_gen_agent_1 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_test_gen_1')
    test_gen_agent_2 = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_test_gen_2')
    test_exec_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_test_exec')
    explain_sql_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_explain_sql')
    ask_human_help_agent= models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, related_name='workspaces_ask_human')
    explanation_ai_model = models.ForeignKey(AiModel, on_delete=models.SET_NULL, null=True, related_name='explanation_workspaces')
    plotly_generator_ai_model = models.ForeignKey(AiModel, on_delete=models.SET_NULL, null=True, related_name='plotly_workspaces')
    setting = models.ForeignKey(Setting, on_delete=models.SET_NULL, null=True, related_name='workspaces')
    last_preprocess = models.DateTimeField(blank=True, null=True)
    last_hint_load = models.DateTimeField(blank=True, null=True)
    last_sql_loaded = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    
    # New fields for async preprocessing
    preprocessing_status = models.CharField(
        max_length=20,
        choices=PreprocessingStatus.choices,
        default=PreprocessingStatus.IDLE
    )
    task_id = models.CharField(max_length=255, blank=True, null=True)
    last_preprocess_log = models.TextField(blank=True, null=True)
    preprocessing_start_time = models.DateTimeField(blank=True, null=True)
    preprocessing_end_time = models.DateTimeField(blank=True, null=True)
    
    # New fields for async AI comment generation
    table_comment_status = models.CharField(
        max_length=20,
        choices=PreprocessingStatus.choices,
        default=PreprocessingStatus.IDLE
    )
    table_comment_task_id = models.CharField(max_length=255, blank=True, null=True)
    table_comment_log = models.TextField(blank=True, null=True)
    table_comment_start_time = models.DateTimeField(blank=True, null=True)
    table_comment_end_time = models.DateTimeField(blank=True, null=True)
    
    column_comment_status = models.CharField(
        max_length=20,
        choices=PreprocessingStatus.choices,
        default=PreprocessingStatus.IDLE
    )
    column_comment_task_id = models.CharField(max_length=255, blank=True, null=True)
    column_comment_log = models.TextField(blank=True, null=True)
    column_comment_start_time = models.DateTimeField(blank=True, null=True)
    column_comment_end_time = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return self.name

class GroupProfile(models.Model):
    group = models.OneToOneField(Group, on_delete=models.CASCADE, related_name='profile')
    show_sql = models.BooleanField(default=False, help_text="Show SQL queries to group members")
    show_keywords = models.BooleanField(default=True, help_text="Show keywords extraction to group members")
    show_hints = models.BooleanField(default=True, help_text="Show hints to group members")
    show_process_info = models.BooleanField(default=False, help_text="Show process information to group members")
    show_sql_generation = models.BooleanField(default=True, help_text="Show SQL generation process to group members")
    explain_generated_query = models.BooleanField(default=True, help_text="Explain generated SQL query to group members")

    class Meta:
        verbose_name = "Group Profile"
        verbose_name_plural = "Group Profiles"

    def __str__(self):
        return f"{self.group.name} Profile"

# Signal handler to automatically create GroupProfile when Group is created
@receiver(post_save, sender=Group)
def create_group_profile(sender, instance, created, **kwargs):
    if created:
        GroupProfile.objects.create(group=instance)

@receiver(post_save, sender=Group)
def save_group_profile(sender, instance, **kwargs):
    if hasattr(instance, 'profile'):
        instance.profile.save()
    else:
        GroupProfile.objects.create(group=instance)
