(a=>{function e(){this.charts=[]}e.prototype.init=function(){this.initCharts()},e.prototype.initCharts=function(){var e=["#727cf5","#0acf97"],t=a("#revenue-statistics-chart").data("colors"),t={chart:{height:361,type:"line",dropShadow:{enabled:!0,opacity:.2,blur:7,left:-7,top:7}},dataLabels:{enabled:!1},stroke:{curve:"smooth",width:4},series:[{name:"Budget",data:[10,20,15,28,22,34]},{name:"Revenue",data:[2,26,10,38,30,48]}],colors:e=t?t.split(","):e,zoom:{enabled:!1},xaxis:{type:"string",categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tooltip:{enabled:!1},axisBorder:{show:!1}},yaxis:{labels:{formatter:function(e){return e+"k"},offsetX:-15}}};new ApexCharts(document.querySelector("#revenue-statistics-chart"),t).render()},a.CrmManagement=new e,a.CrmManagement.Constructor=e})(window.jQuery),(t=>{t(document).ready(function(e){t.CrmManagement.init()})})(window.jQuery);