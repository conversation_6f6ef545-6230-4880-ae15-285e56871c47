"""
Oracle adapter for Thoth SQL Database Manager.
"""

from typing import Any, Dict, List, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

from ..core.interfaces import DbAdapter


class OracleAdapter(DbAdapter):
    """Oracle database adapter."""
    
    def __init__(self, connection_string: str, **kwargs: Any) -> None:
        """
        Initialize Oracle adapter.
        
        Args:
            connection_string: Oracle connection string
            **kwargs: Additional connection parameters
        """
        self.connection_string = connection_string
        self.engine = None
        self.connection_params = kwargs
        
    def connect(self) -> None:
        """Establish database connection."""
        try:
            self.engine = create_engine(
                self.connection_string,
                pool_pre_ping=True,
                **self.connection_params
            )
        except Exception as e:
            raise ConnectionError(f"Failed to connect to Oracle: {e}")
    
    def disconnect(self) -> None:
        """Close database connection."""
        if self.engine:
            self.engine.dispose()
            self.engine = None
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a query and return results."""
        if not self.engine:
            self.connect()
            
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                return [dict(row._mapping) for row in result]
        except SQLAlchemyError as e:
            raise RuntimeError(f"Oracle query failed: {e}")
    
    def execute_update(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """Execute an update query and return affected row count."""
        if not self.engine:
            self.connect()
            
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                conn.commit()
                return result.rowcount
        except SQLAlchemyError as e:
            raise RuntimeError(f"Oracle update failed: {e}")
    
    def get_tables(self) -> List[str]:
        """Get list of tables in the database."""
        query = """
        SELECT table_name as name
        FROM user_tables
        ORDER BY table_name
        """
        result = self.execute_query(query)
        return [row['name'] for row in result]
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get schema information for a specific table."""
        query = f"""
        SELECT 
            column_name as name,
            data_type as type,
            nullable,
            data_default as default_value,
            CASE WHEN constraint_type = 'P' THEN 1 ELSE 0 END as is_primary_key
        FROM user_tab_columns c
        LEFT JOIN (
            SELECT cc.column_name, uc.constraint_type
            FROM user_constraints uc
            JOIN user_cons_columns cc ON uc.constraint_name = cc.constraint_name
            WHERE uc.table_name = '{table_name.upper()}'
            AND uc.constraint_type = 'P'
        ) pk ON c.column_name = pk.column_name
        WHERE c.table_name = '{table_name.upper()}'
        ORDER BY c.column_id
        """
        
        columns = self.execute_query(query)
        
        schema = {
            'table_name': table_name,
            'columns': []
        }
        
        for col in columns:
            schema['columns'].append({
                'name': col['name'],
                'type': col['type'],
                'nullable': col['nullable'] == 'Y',
                'default': col['default_value'],
                'primary_key': bool(col['is_primary_key'])
            })
        
        return schema
    
    def get_indexes(self, table_name: str) -> List[Dict[str, Any]]:
        """Get index information for a table."""
        query = f"""
        SELECT 
            index_name as name,
            column_name,
            uniqueness as unique_index,
            index_type
        FROM user_ind_columns ic
        JOIN user_indexes i ON ic.index_name = i.index_name
        WHERE ic.table_name = '{table_name.upper()}'
        ORDER BY ic.index_name, ic.column_position
        """
        
        return self.execute_query(query)
    
    def get_foreign_keys(self, table_name: str) -> List[Dict[str, Any]]:
        """Get foreign key information for a table."""
        query = f"""
        SELECT 
            constraint_name as name,
            column_name,
            r_table_name as referenced_table,
            r_column_name as referenced_column
        FROM user_cons_columns cc
        JOIN user_constraints c ON cc.constraint_name = c.constraint_name
        JOIN user_cons_columns rcc ON c.r_constraint_name = rcc.constraint_name
        WHERE c.table_name = '{table_name.upper()}'
        AND c.constraint_type = 'R'
        ORDER BY cc.constraint_name, cc.position
        """
        
        return self.execute_query(query)
    
    def create_table(self, table_name: str, schema: Dict[str, Any]) -> None:
        """Create a new table with the given schema."""
        columns = []
        for col in schema.get('columns', []):
            col_def = f'"{col["name"]}" {col["type"]}'
            if not col.get('nullable', True):
                col_def += " NOT NULL"
            if col.get('default') is not None:
                col_def += f" DEFAULT {col['default']}"
            if col.get('primary_key'):
                col_def += " PRIMARY KEY"
            columns.append(col_def)
        
        query = f'CREATE TABLE "{table_name}" ({", ".join(columns)})'
        self.execute_update(query)
    
    def drop_table(self, table_name: str) -> None:
        """Drop a table."""
        query = f'DROP TABLE "{table_name}"'
        self.execute_update(query)
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        query = f"""
        SELECT COUNT(*) as count
        FROM user_tables
        WHERE table_name = '{table_name.upper()}'
        """
        result = self.execute_query(query)
        return result[0]['count'] > 0
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information."""
        return {
            'type': 'oracle',
            'connection_string': self.connection_string,
            'connected': self.engine is not None
        }
