# Generated by Django 5.2 on 2025-07-20 23:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('thoth_core', '0003_add_lmstudio_choice'),
    ]

    operations = [
        migrations.AddField(
            model_name='workspace',
            name='question_validator',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_question_validator', to='thoth_core.agent'),
        ),
        migrations.AlterField(
            model_name='agent',
            name='agent_type',
            field=models.CharField(choices=[('GENERATESQLBASIC', 'Generate SQL Basic'), ('DEFAULT', 'Default'), ('EXTRACTKEYWORDS', 'Extract Keywords'), ('SELECTCOLUMNS', 'Select Columns'), ('SQLBASIC', 'Sql - Basic'), ('SQLADVANCED', 'Sql - Advanced'), ('SQLEXPERT', 'Sql - Expert'), ('SQLTITAN', 'Sql - Titan'), ('TESTGENERATOR', 'Test Generator'), ('TESTEXECUTOR', 'Test Executor'), ('ASKFORHUMANHELP', 'Ask For Human Help'), ('VALIDATEQUESTION', 'Validate Question')], default='EXTRACTKEYWORDS', max_length=255),
        ),
    ]
