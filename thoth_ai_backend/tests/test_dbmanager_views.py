import unittest
import os
import django
from pathlib import Path

# Add project root to sys.path
project_root = Path(__file__).resolve().parents[2]
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Thoth.settings')
django.setup()

from unittest.mock import patch, MagicMock
from thoth_ai_backend.views import run_preprocessing_task, preprocess
from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager
from dbmanager.ThothDbManager import ThothDbManager
from thoth_core.models import Workspace, SqlDb, Setting, VectorDb

class TestDbManagerViews(unittest.TestCase):

    @patch('thoth_ai_backend.views.ThothPgManager')
    @patch('thoth_ai_backend.views.ThothSqliteManager')
    @patch('thoth_ai_backend.views.Workspace.objects.get')
    @patch('thoth_ai_backend.views.preprocess')
    def test_run_preprocessing_task_postgres(self, mock_preprocess, mock_workspace_get, mock_sqlite_manager, mock_pg_manager):
        # Setup
        mock_workspace = MagicMock()
        mock_workspace.id = 1
        mock_workspace.sql_db = MagicMock()
        mock_workspace.sql_db.db_type = 'PostgreSQL'
        mock_workspace.sql_db.db_host = 'localhost'
        mock_workspace.sql_db.db_port = 5432
        mock_workspace.sql_db.db_name = 'test_db'
        mock_workspace.sql_db.user_name = 'user'
        mock_workspace.sql_db.password = 'pass'
        mock_workspace.sql_db.schema = 'public'
        mock_workspace.sql_db.vector_db = MagicMock()
        mock_workspace.setting = MagicMock()
        mock_workspace.setting.signature_size = 128
        mock_workspace.setting.n_grams = 2
        mock_workspace.setting.threshold = 0.5
        mock_workspace.setting.verbose = True
        mock_workspace.setting.use_value_description = True

        mock_workspace_get.return_value = mock_workspace

        # Execute
        run_preprocessing_task(1)

        # Assert
        mock_pg_manager.assert_called_once_with(
            host='localhost',
            port=5432,
            schema='public',
            dbname='test_db',
            user='user',
            password='pass',
            db_root_path=None,  # This would be set by os.getenv
            db_mode='dev'  # Default mode
        )
        mock_preprocess.assert_called_once()

    @patch('thoth_ai_backend.views.ThothPgManager')
    @patch('thoth_ai_backend.views.ThothSqliteManager')
    @patch('thoth_ai_backend.views.Workspace.objects.get')
    @patch('thoth_ai_backend.views.preprocess')
    def test_run_preprocessing_task_sqlite(self, mock_preprocess, mock_workspace_get, mock_sqlite_manager, mock_pg_manager):
        # Setup
        mock_workspace = MagicMock()
        mock_workspace.id = 1
        mock_workspace.sql_db = MagicMock()
        mock_workspace.sql_db.db_type = 'SQLite'
        mock_workspace.sql_db.db_name = 'test_db'
        mock_workspace.setting = MagicMock()
        mock_workspace.setting.signature_size = 128
        mock_workspace.setting.n_grams = 2
        mock_workspace.setting.threshold = 0.5
        mock_workspace.setting.verbose = True
        mock_workspace.setting.use_value_description = True

        mock_workspace_get.return_value = mock_workspace

        # Execute
        run_preprocessing_task(1)

        # Assert
        mock_sqlite_manager.assert_called_once_with(
            db_id='test_db',
            db_root_path=None,  # This would be set by os.getenv
            db_mode='dev'  # Default mode
        )
        mock_preprocess.assert_called_once()

    @patch('thoth_ai_backend.views.make_db_lsh')
    @patch('thoth_ai_backend.views.make_db_context_vec_db')
    def test_preprocess(self, mock_make_db_context, mock_make_db_lsh):
        # Setup
        mock_db = MagicMock(spec=ThothDbManager)
        mock_document_store = MagicMock()
        db_params = {
            'db_name': 'test_db',
            'db_mode': 'dev'
        }
        setting = {
            'signature_size': 128,
            'n_grams': 2,
            'threshold': 0.5,
            'verbose': True,
            'use_value_description': True
        }

        # Execute
        preprocess(mock_db, mock_document_store, db_params, setting)

        # Assert
        mock_make_db_lsh.assert_called_once()
        mock_make_db_context.assert_called_once()

if __name__ == '__main__':
    unittest.main()
