###

POST http://127.0.0.1:8000/api/login
Content-Type: application/json

{   "username": "marco",
    "password": "confucio"}

###

GET http://127.0.0.1:8000/api/test_token
Content-Type: application/json
Authorization: Token bcff05c332257bd1a6b434a01d02b8c5d865ea1c

###

GET http://127.0.0.1:8000/api/workspaces
Content-Type: application/json
X-API-KEY: 3LHoZzYlGGsvdamksrcZYlah3H5IArxYnLkrSTBB9pg.

###

GET http://127.0.0.1:8000/api/workspace/2/agents
Content-Type: application/json
Authorization: Token b8dbc3b647b9131d91508d572496309c2f4af14a

###

GET http://127.0.0.1:8000/api/workspace/2/agents/type/GENERATESQLBASIC
Content-Type: application/json
Authorization: Token b8dbc3b647b9131d91508d572496309c2f4af14a

###



