"""
SQL Server plugin for Thoth SQL Database Manager.
"""

from typing import Any, Dict, List, Optional
from ..core.interfaces import DbPlugin
from ..adapters.sqlserver import SQLServerAdapter


class SQLServerPlugin(DbPlugin):
    """SQL Server database plugin."""
    
    def __init__(self) -> None:
        """Initialize SQL Server plugin."""
        self.name = "sqlserver"
        self.display_name = "SQL Server"
        self.description = "Microsoft SQL Server database plugin"
        self.version = "1.0.0"
        
    def get_adapter(self, connection_string: str, **kwargs: Any) -> SQLServerAdapter:
        """
        Get SQL Server adapter instance.
        
        Args:
            connection_string: SQL Server connection string
            **kwargs: Additional connection parameters
            
        Returns:
            SQLServerAdapter instance
        """
        return SQLServerAdapter(connection_string, **kwargs)
    
    def get_connection_string_template(self) -> str:
        """Get connection string template for SQL Server."""
        return "mssql+pyodbc://{username}:{password}@{host}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server"
    
    def get_default_port(self) -> int:
        """Get default SQL Server port."""
        return 1433
    
    def get_required_parameters(self) -> List[str]:
        """Get required connection parameters."""
        return ["host", "database", "username", "password"]
    
    def get_optional_parameters(self) -> Dict[str, Any]:
        """Get optional connection parameters."""
        return {
            "port": 1433,
            "driver": "ODBC Driver 17 for SQL Server",
            "trusted_connection": False
        }
    
    def validate_connection_string(self, connection_string: str) -> bool:
        """Validate SQL Server connection string format."""
        return "mssql+pyodbc://" in connection_string
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get SQL Server database information."""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "version": self.version,
            "features": [
                "transactions",
                "foreign_keys",
                "indexes",
                "views",
                "stored_procedures",
                "triggers",
                "computed_columns",
                "partitioning"
            ],
            "data_types": [
                "INT", "BIGINT", "DECIMAL", "NUMERIC", "FLOAT", "REAL",
                "VARCHAR", "NVARCHAR", "TEXT", "NTEXT", "BINARY", "VARBINARY",
                "DATE", "TIME", "DATETIME", "DATETIME2", "SMALLDATETIME",
                "BIT", "UNIQUEIDENTIFIER", "XML"
            ]
        }
    
    def get_sample_connection_config(self) -> Dict[str, Any]:
        """Get sample connection configuration."""
        return {
            "host": "localhost",
            "port": 1433,
            "database": "mydatabase",
            "username": "user",
            "password": "password",
            "driver": "ODBC Driver 17 for SQL Server"
        }
