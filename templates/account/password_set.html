{% extends "account/base.html" %}

{% load i18n %}
{% load crispy_forms_tags %}

{% block head_title %}{% trans "Set Password" %}{% endblock %}

{% block page_content %}
    <h1>{% trans "Set Password" %}</h1>

    <form method="POST" action="{% url 'account_set_password' %}" class="password_set" novalidate>
        {% csrf_token %}
        {{ form|crispy }}
        <input class="btn btn-primary" type="submit" name="action" value="{% trans 'Set Password' %}"/>
    </form>
{% endblock %}
