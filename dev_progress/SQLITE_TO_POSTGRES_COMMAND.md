# SQLite to PostgreSQL Conversion Command

## Overview

Il comando `sqlite_to_postgres` permette di convertire database SQLite in PostgreSQL utilizzando pgloader all'interno del container Docker.

## Prerequisiti

1. **Docker Configuration**: Il container PostgreSQL deve essere configurato con pgloader installato (già fatto nel `postgres/Dockerfile`)
2. **File SQLite**: Il file SQLite deve essere presente nella directory `data/` del progetto
3. **Container attivi**: I container `thoth-db` e `thoth-be` devono essere in esecuzione

## Utilizzo

### Sintassi Base
```bash
python manage.py sqlite_to_postgres <percorso_file_sqlite> [opzioni]
```

### Esempi di Utilizzo

#### 1. Conversione Base (File nella Root di data/)
```bash
# Converte california_schools.sqlite nel database PostgreSQL 'california_schools'
python manage.py sqlite_to_postgres california_schools.sqlite --create-db
```

#### 2. Conversione con File in Subdirectory
```bash
# Converte un file SQLite in una subdirectory
python manage.py sqlite_to_postgres dev_databases/subdir_test.sqlite --create-db

# Il database PostgreSQL sarà chiamato 'subdir_test' (nome del file senza estensione)
```

#### 3. Conversione con Database Personalizzato
```bash
# Converte il file SQLite nel database 'my_custom_db'
python manage.py sqlite_to_postgres california_schools.sqlite --target-db my_custom_db --create-db

# Anche con percorsi relativi
python manage.py sqlite_to_postgres dev_databases/subdir_test.sqlite --target-db projects_db --create-db
```

#### 3. Sovrascrittura Database Esistente
```bash
# Elimina le tabelle esistenti e ricrea tutto
python manage.py sqlite_to_postgres california_schools.sqlite --create-db --drop-tables
```

#### 4. Test del Comando (Dry Run)
```bash
# Mostra il comando pgloader che verrebbe eseguito senza eseguirlo
python manage.py sqlite_to_postgres california_schools.sqlite --dry-run
```

## Opzioni Disponibili

| Opzione | Descrizione | Default |
|---------|-------------|---------|
| `sqlite_filename` | Nome del file SQLite nella directory data/ | Obbligatorio |
| `--target-db` | Nome del database PostgreSQL di destinazione | Nome del file senza estensione |
| `--create-db` | Crea il database se non esiste | False |
| `--drop-tables` | Elimina tabelle esistenti (richiede --create-db) | False |
| `--dry-run` | Mostra il comando senza eseguirlo | False |

## Processo di Conversione

Il comando esegue i seguenti passaggi:

1. **Validazione File SQLite**
   - Verifica l'esistenza del file nella directory `data/`
   - Controlla che sia un file SQLite valido
   - Conta il numero di tabelle presenti

2. **Configurazione PostgreSQL**
   - Legge le credenziali dalle impostazioni Django
   - Utilizza le credenziali del container `thoth-db`

3. **Creazione Database** (se `--create-db` è specificato)
   - Verifica se il database esiste già
   - Crea il database se necessario
   - Elimina le tabelle esistenti se `--drop-tables` è specificato

4. **Esecuzione pgloader**
   - Costruisce il comando pgloader appropriato
   - Esegue la conversione all'interno del container PostgreSQL
   - Mostra l'output dettagliato del processo

## Gestione Errori

Il comando gestisce diversi tipi di errori:

- **File SQLite non trovato**: Verifica che il file sia nella directory `data/`
- **File SQLite non valido**: Controlla la validità del database SQLite
- **Errori di connessione PostgreSQL**: Verifica che il container sia attivo
- **Errori pgloader**: Mostra l'output dettagliato per il debugging
- **Timeout**: Gestisce database di grandi dimensioni (timeout 5 minuti)

## Configurazione Docker

### docker-compose.yml
Il servizio `db` è stato aggiornato per:
- Utilizzare il Dockerfile personalizzato con pgloader
- Montare il volume `thoth-shared-data` per accedere ai file SQLite

```yaml
db:
  build:
    context: ./postgres
  container_name: thoth-db
  volumes:
    - postgres-data:/var/lib/postgresql/data
    - thoth-shared-data:/app/data  # Accesso ai file SQLite
```

### postgres/Dockerfile
```dockerfile
FROM postgres:14
RUN apt-get update && apt-get install -y pgloader && apt-get clean && rm -rf /var/lib/apt/lists/*
```

## Credenziali di Accesso al Database Convertito

Il database PostgreSQL creato dalla conversione utilizza le **stesse credenziali del container PostgreSQL principale**:

- **Host**: `localhost` (dal container) o `127.0.0.1:5443` (dall'esterno)
- **Username**: `thoth_user`
- **Password**: `thoth_password`
- **Database**: Nome specificato nel comando (default: nome del file SQLite senza estensione)

### Accesso come Admin

```bash
# Accesso diretto dal container PostgreSQL
docker exec -it thoth-db psql -U thoth_user -d <nome_database>

# Accesso dall'esterno del container
psql -h 127.0.0.1 -p 5443 -U thoth_user -d <nome_database>

# Esempio con database convertito
docker exec -it thoth-db psql -U thoth_user -d california_schools
```

### Gestione Database via pgAdmin o Altri Client

Se usi pgAdmin o altri client PostgreSQL:
- **Host**: `127.0.0.1`
- **Port**: `5443`
- **Username**: `thoth_user`
- **Password**: `thoth_password`
- **Database**: `<nome_database_convertito>`

## Esempio Completo

Supponiamo di avere un file `california_schools.sqlite` nella directory `data/`:

```bash
# 1. Verifica che il file esista
ls data/california_schools.sqlite

# 2. Test del comando (dry run)
python manage.py sqlite_to_postgres california_schools.sqlite --dry-run

# 3. Conversione effettiva
python manage.py sqlite_to_postgres california_schools.sqlite --create-db

# 4. Verifica del risultato
docker exec thoth-db psql -U thoth_user -d california_schools -c "\dt"

# 5. Accesso interattivo al database convertito
docker exec -it thoth-db psql -U thoth_user -d california_schools
```

## Troubleshooting

### Container non attivo
```bash
docker-compose up -d db
```

### File SQLite non trovato
```bash
# Verifica che il file sia nella directory corretta
ls -la data/
```

### Errori di permessi
```bash
# Verifica i permessi del file SQLite
chmod 644 data/your_file.sqlite
```

### Database già esistente
```bash
# Usa --drop-tables per sovrascrivere
python manage.py sqlite_to_postgres file.sqlite --create-db --drop-tables
```

## Note Tecniche

- Il comando utilizza `docker exec` per eseguire pgloader nel container PostgreSQL
- I percorsi dei file sono mappati correttamente tra i container
- Le credenziali PostgreSQL sono lette dalle impostazioni Django
- Il timeout predefinito è di 5 minuti per database di grandi dimensioni
