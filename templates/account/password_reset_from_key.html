{% extends "account/base.html" %}
{% load static i18n crispy_forms_tags %}

{% block title %}{% trans "Change Password" %}{% endblock title %}

{% block body_extra %}
class="loading authentication-bg" data-layout-config='{"leftSideBarTheme":"dark","layoutBoxed":false,
"leftSidebarCondensed":false, "leftSidebarScrollable":false,"darkMode":false, "showRightSidebarOnStart": true}'
{% endblock %}

{% block page_content %}
<div class="account-pages pt-2 pt-sm-5 pb-4 pb-sm-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xxl-4 col-lg-5">
                <div class="card">

                    <!-- Logo -->
                    <div class="card-header pt-4 pb-4 text-center bg-primary">
                        <a href="{% url 'index' %}">
                            <span><img src="{% static 'images/logo.png' %}" alt="" height="18"></span>
                        </a>
                    </div>

                    <div class="card-body p-4">

                        <div class="text-center w-75 m-auto">
                            <h4 class="text-dark-50 text-center mt-0 fw-bold">{% if token_fail %}{% trans "Bad Token"
                                %}{% else %}{% trans "Change Password" %}{% endif %}</h4>
                        </div>

                        {% if token_fail %}
                        {% url 'account_reset_password' as passwd_reset_url %}
                        <p class="text-muted mb-4">{% blocktrans %}The password reset link was invalid, possibly because
                            it has already been used. Please request a <a href="{{ passwd_reset_url }}">new password
                                reset</a>.{% endblocktrans %}</p>
                        {% else %}
                        {% if form %}
                        <form method="POST" action="." novalidate>
                            {% csrf_token %}
                            {{ form|crispy }}
                            <div class="mt-2 mb-2 mb-0 text-center">
                                <button class="btn btn-primary" type="submit"> {% trans 'change password' %} </button>
                            </div>
                        </form>
                        {% else %}
                        <p class="text-muted mb-4">{% trans 'Your password is now changed.' %}</p>
                        {% endif %}
                        {% endif %}

                    </div> <!-- end card-body -->
                </div>
                <!-- end card -->

            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>
    <!-- end container -->
</div>
<!-- end page -->

<footer class="footer footer-alt">
    2018 - 2021 © Hyper - Coderthemes.com
</footer>
{% endblock %}