# LSH Similarity Search Evaluation Tool

This tool provides an interactive CLI for evaluating the efficacy of LSH (Locality-Sensitive Hashing) similarity search in your database.

## Features

- **Natural Language Processing**: Uses Google Gemini via OpenRouter to extract SQL-relevant keywords from questions
- **LSH Query Engine**: Leverages your existing LSH implementation to find similar database columns and values
- **Interactive CLI**: Continuous question/answer loop until user exits
- **User-Friendly Results**: Formatted output with similarity scores and organized by table/column
- **Export Capabilities**: Save results to JSON for further analysis
- **Performance Metrics**: Shows query times and match counts

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Settings**:
   Edit `config.yml` with your database and API settings:
   ```yaml
   DB_ROOT_PATH: /path/to/your/data
   DB_TYPE: SQLite
   DB_NAME: california_schools
   DB_MODE: dev
   OPENROUTER_API_KEY: your-api-key
   OPENROUTER_API_BASE: https://openrouter.ai/api/v1
   ```

3. **Run the Tool**:
   ```bash
   python main.py
   ```

## Usage Examples

Once running, you can ask questions like:

- "Find columns related to student performance"
- "What tables contain information about school demographics?"
- "Show me columns with test scores or grades"
- "Find schools opened after 2000"
- "List columns with contact information"

## How It Works

1. **Keyword Extraction**: Your question is sent to Google Gemini, which extracts relevant SQL keywords
2. **LSH Search**: Each keyword is used to query the LSH index for similar database values
3. **Results Aggregation**: Results are combined, deduplicated, and sorted by similarity
4. **Display**: Results are shown in a user-friendly format with similarity scores

## Configuration

### Database Settings
- `DB_ROOT_PATH`: Root directory containing your databases
- `DB_TYPE`: Database type (SQLite, PostgreSQL, etc.)
- `DB_NAME`: Name of the database to query
- `DB_MODE`: Environment mode (dev, prod, etc.)

### LSH Settings
- `LSH_SIGNATURE_SIZE`: MinHash signature size (default: 30)
- `LSH_N_GRAM`: N-gram size for MinHash (default: 3)
- `LSH_THRESHOLD`: Similarity threshold (default: 0.5)
- `LSH_TOP_N`: Maximum results per query (default: 10)

### API Settings
- `OPENROUTER_API_KEY`: Your OpenRouter API key
- `OPENROUTER_API_BASE`: OpenRouter API endpoint

## File Structure

```
lsh-test/
├── config.yml              # Configuration file
├── main.py                 # Main CLI application
├── config_loader.py        # Configuration management
├── keyword_extractor.py    # OpenRouter API integration
├── lsh_evaluator.py        # LSH query and evaluation logic
├── results_formatter.py    # User-friendly result formatting
├── requirements.txt        # Additional dependencies
└── README.md              # This file
```

## Troubleshooting

### LSH Data Not Found
Ensure your LSH data is preprocessed and located at:
```
{DB_ROOT_PATH}/{DB_MODE}_databases/{DB_NAME}/preprocessed/
```

### API Connection Issues
- Verify your OpenRouter API key is correct
- Check internet connectivity
- The tool will fall back to basic keyword extraction if API is unavailable

### No Results Found
- Try different keywords or phrasing
- Check if your LSH data contains relevant information
- Verify the database contains the expected data

## Development

The tool is designed to integrate seamlessly with your existing LSH implementation. Key components:

- **LshEvaluator**: Wraps your existing LSH manager for easy querying
- **KeywordExtractor**: Handles both API-based and fallback keyword extraction
- **ResultsFormatter**: Provides consistent, readable output formats

## License

This tool is part of the Thoth SQL Database Manager project.
