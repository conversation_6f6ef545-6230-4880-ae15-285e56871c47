# Thoth SQL Database Manager - Quick Start Guide

Get up and running with the Thoth SQL Database Manager in minutes.

## Installation

```bash
pip install thoth-sqldb2
```

## 5-Minute Quick Start

### 1. Import and Connect

```python
from dbmanager import ThothDbManager

# SQLite (easiest to get started)
manager = ThothDbManager.get_instance(
    db_type="sqlite",
    db_root_path="./data",
    db_mode="dev",
    database_path="./data/quickstart.db"
)

# PostgreSQL (if you have a server running)
# manager = ThothDbManager.get_instance(
#     db_type="postgresql",
#     db_root_path="./data",
#     db_mode="dev",
#     host="localhost",
#     port=5432,
#     database="mydb",
#     user="username",
#     password="password"
# )
```

### 2. Create Sample Data

```python
# Create a simple table
manager.execute_sql("""
    CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE,
        age INTEGER,
        city TEXT
    )
""")

# Insert sample data
sample_customers = [
    (1, "<PERSON>", "<EMAIL>", 30, "<PERSON> York"),
    (2, "<PERSON> Doe", "<EMAIL>", 25, "Los <PERSON>"),
    (3, "Bob <PERSON>", "<EMAIL>", 35, "Chicago"),
    (4, "Alice <PERSON>", "<EMAIL>", 28, "Houston"),
    (5, "Charlie <PERSON>", "<EMAIL>", 42, "<PERSON>")
]

for customer in sample_customers:
    manager.execute_sql(
        "INSERT OR REPLACE INTO customers (id, name, email, age, city) VALUES (?, ?, ?, ?, ?)",
        params=customer
    )

print("✓ Sample data created")
```

### 3. Explore Your Database

```python
# Get all tables
tables = manager.get_tables()
print(f"Tables in database: {[t['name'] for t in tables]}")

# Get column information
columns = manager.get_columns("customers")
print("\nCustomers table structure:")
for col in columns:
    print(f"  - {col['name']}: {col['data_type']}")

# Query your data
customers = manager.execute_sql("SELECT * FROM customers")
print(f"\nFound {len(customers)} customers:")
for customer in customers:
    print(f"  {customer['name']} ({customer['age']}) - {customer['city']}")
```

### 4. Try LSH Similarity Search

```python
# Initialize LSH for similarity search
print("\nInitializing LSH...")
lsh_status = manager.set_lsh()

if lsh_status == "success":
    print("✓ LSH initialized successfully")
    
    # Search for similar names
    similar = manager.query_lsh("john", top_n=3)
    
    print("\nSimilar names to 'john':")
    for table, columns in similar.items():
        for column, values in columns.items():
            if values:
                print(f"  {table}.{column}: {values}")
else:
    print("✗ LSH initialization failed")
```

### 5. Get Example Data

```python
# Understand your data better
examples = manager.get_example_data("customers", number_of_rows=10)

print("\nExample data from customers table:")
for column, values in examples.items():
    print(f"  {column}: {values}")
```

## Complete Quick Start Script

Save this as `quickstart.py`:

```python
#!/usr/bin/env python3
"""
Thoth SQL Database Manager - Quick Start Example
"""
import os
from dbmanager import ThothDbManager

def main():
    print("=== Thoth SQL Database Manager Quick Start ===\n")
    
    # Ensure data directory exists
    os.makedirs("./data", exist_ok=True)
    
    # 1. Connect to database
    print("1. Connecting to SQLite database...")
    manager = ThothDbManager.get_instance(
        db_type="sqlite",
        db_root_path="./data",
        db_mode="dev",
        database_path="./data/quickstart.db"
    )
    print("✓ Connected successfully\n")
    
    # 2. Create sample data
    print("2. Creating sample data...")
    
    # Create customers table
    manager.execute_sql("""
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE,
            age INTEGER,
            city TEXT,
            registration_date DATE DEFAULT CURRENT_DATE
        )
    """)
    
    # Create orders table
    manager.execute_sql("""
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY,
            customer_id INTEGER,
            product_name TEXT NOT NULL,
            amount DECIMAL(10,2),
            order_date DATE DEFAULT CURRENT_DATE,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
    """)
    
    # Insert sample customers
    customers = [
        (1, "John Smith", "<EMAIL>", 30, "New York"),
        (2, "Jane Doe", "<EMAIL>", 25, "Los Angeles"),
        (3, "Bob Johnson", "<EMAIL>", 35, "Chicago"),
        (4, "Alice Brown", "<EMAIL>", 28, "Houston"),
        (5, "Charlie Wilson", "<EMAIL>", 42, "Phoenix"),
        (6, "Diana Davis", "<EMAIL>", 31, "Philadelphia"),
        (7, "Frank Miller", "<EMAIL>", 29, "San Antonio"),
        (8, "Grace Lee", "<EMAIL>", 33, "San Diego")
    ]
    
    for customer in customers:
        manager.execute_sql(
            "INSERT OR REPLACE INTO customers (id, name, email, age, city) VALUES (?, ?, ?, ?, ?)",
            params=customer
        )
    
    # Insert sample orders
    orders = [
        (1, 1, "Laptop Computer", 999.99),
        (2, 1, "Wireless Mouse", 29.99),
        (3, 2, "Smartphone", 699.99),
        (4, 3, "Office Chair", 199.99),
        (5, 3, "Desk Lamp", 49.99),
        (6, 4, "Tablet", 399.99),
        (7, 5, "Headphones", 149.99),
        (8, 6, "Monitor", 299.99)
    ]
    
    for order in orders:
        manager.execute_sql(
            "INSERT OR REPLACE INTO orders (id, customer_id, product_name, amount) VALUES (?, ?, ?, ?)",
            params=order
        )
    
    print("✓ Sample data created\n")
    
    # 3. Explore database structure
    print("3. Exploring database structure...")
    
    tables = manager.get_tables()
    print(f"Tables: {[t['name'] for t in tables]}")
    
    for table in tables:
        table_name = table['name']
        columns = manager.get_columns(table_name)
        print(f"\n{table_name} table columns:")
        for col in columns:
            pk_flag = " (PK)" if col['is_pk'] else ""
            print(f"  - {col['name']}: {col['data_type']}{pk_flag}")
    
    # Show foreign keys
    foreign_keys = manager.get_foreign_keys()
    if foreign_keys:
        print("\nForeign key relationships:")
        for fk in foreign_keys:
            print(f"  {fk['source_table_name']}.{fk['source_column_name']} -> "
                  f"{fk['target_table_name']}.{fk['target_column_name']}")
    
    print()
    
    # 4. Query data
    print("4. Querying data...")
    
    # Simple queries
    customer_count = manager.execute_sql("SELECT COUNT(*) as count FROM customers")[0]['count']
    print(f"Total customers: {customer_count}")
    
    order_count = manager.execute_sql("SELECT COUNT(*) as count FROM orders")[0]['count']
    print(f"Total orders: {order_count}")
    
    # Join query
    customer_orders = manager.execute_sql("""
        SELECT 
            c.name,
            c.city,
            COUNT(o.id) as order_count,
            SUM(o.amount) as total_spent
        FROM customers c
        LEFT JOIN orders o ON c.id = o.customer_id
        GROUP BY c.id, c.name, c.city
        ORDER BY total_spent DESC
    """)
    
    print("\nCustomer order summary:")
    for row in customer_orders:
        spent = row['total_spent'] or 0
        print(f"  {row['name']} ({row['city']}): {row['order_count']} orders, ${spent:.2f} spent")
    
    print()
    
    # 5. Example data analysis
    print("5. Analyzing data patterns...")
    
    examples = manager.get_example_data("customers", number_of_rows=10)
    print("Example values in customers table:")
    for column, values in examples.items():
        print(f"  {column}: {values[:3]}...")  # Show first 3 values
    
    print()
    
    # 6. LSH similarity search
    print("6. Testing LSH similarity search...")
    
    lsh_status = manager.set_lsh()
    if lsh_status == "success":
        print("✓ LSH initialized successfully")
        
        # Search for similar customer names
        test_searches = ["john", "alice", "phone"]
        
        for search_term in test_searches:
            similar = manager.query_lsh(search_term, top_n=3)
            
            print(f"\nSimilar values to '{search_term}':")
            found_results = False
            for table, columns in similar.items():
                for column, values in columns.items():
                    if values:
                        print(f"  {table}.{column}: {values}")
                        found_results = True
            
            if not found_results:
                print(f"  No similar values found for '{search_term}'")
    else:
        print("✗ LSH initialization failed")
    
    print("\n=== Quick Start Complete! ===")
    print("\nNext steps:")
    print("1. Try connecting to your own database")
    print("2. Explore the API documentation (API_DOCUMENTATION.md)")
    print("3. Check out more examples (USAGE_EXAMPLES.md)")
    print("4. Read the full README (README.md)")

if __name__ == "__main__":
    main()
```

## Run the Quick Start

```bash
python quickstart.py
```

## What You Just Did

1. **Connected** to a SQLite database
2. **Created** tables with sample data
3. **Explored** the database structure
4. **Queried** data with SQL
5. **Analyzed** data patterns
6. **Tested** LSH similarity search

## Next Steps

### Try Different Databases

```python
# PostgreSQL
pg_manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    host="localhost",
    port=5432,
    database="mydb",
    user="username",
    password="password"
)

# MySQL
mysql_manager = ThothDbManager.get_instance(
    db_type="mysql",
    db_root_path="./data",
    host="localhost",
    port=3306,
    database="mydb",
    user="username",
    password="password"
)
```

### Use the Factory Pattern

```python
from dbmanager import ThothDbFactory

# List available databases
available = ThothDbFactory.list_available_databases()
print(f"Available: {available}")

# Get parameter requirements
params = ThothDbFactory.get_required_parameters("postgresql")
print(f"PostgreSQL needs: {params['required']}")

# Create with validation
manager = ThothDbFactory.create_with_validation(
    db_type="sqlite",
    db_root_path="./data",
    database_path="./data/mydb.db"
)
```

### Advanced Features

```python
# Document-based API (if available)
if hasattr(manager, 'get_tables_as_documents'):
    table_docs = manager.get_tables_as_documents()
    for doc in table_docs:
        print(f"Table: {doc.table_name} - {doc.text}")

# Health check
if hasattr(manager, 'health_check'):
    is_healthy = manager.health_check()
    print(f"Database healthy: {is_healthy}")

# Connection info
if hasattr(manager, 'get_connection_info'):
    info = manager.get_connection_info()
    print(f"Connection: {info}")
```

## Common Issues

### SQLite Permission Error
```bash
mkdir -p ./data
chmod 755 ./data
```

### PostgreSQL Connection Error
```python
# Check if PostgreSQL is running
# Verify host, port, database name, username, and password
# Make sure the database exists
```

### LSH Not Working
```python
# LSH requires text data in your database
# Make sure you have tables with text columns
# LSH initialization can take time for large databases
```

## Getting Help

- **Full Documentation**: See `README.md`
- **API Reference**: See `API_DOCUMENTATION.md`
- **Examples**: See `USAGE_EXAMPLES.md`
- **Issues**: Check the project repository

You're now ready to use the Thoth SQL Database Manager! 🎉
