import os
import pickle
from pathlib import Path

from datasketch import MinHash, MinHashLSH

# --- Parameters ---
SIGNATURE_SIZE = 128
N_GRAM = 9
JACCARD_THRESHOLD = 0.5
DB_DIRECTORY_PATH = "tests/test_db"

# --- Sample Data ---
sample_data = {
    "table1": {
        "col1": ["new york city", "big apple", "nyc"],
        "col2": ["los angeles", "la", "city of angels"],
    },
    "table2": {
        "col3": ["san francisco", "sf", "bay area"],
        "col4": ["chicago", "windy city", "chi-town"],
    },
}

def create_minhash(text, signature_size, n_gram):
    """Creates a MinHash for the given text."""
    m = MinHash(num_perm=signature_size)
    for i in range(len(text) - n_gram + 1):
        m.update(text[i:i+n_gram].encode('utf8'))
    return m

def generate_test_data():
    """Generates and saves LSH and MinHashes for testing."""
    # Create directory if it doesn't exist
    preprocessed_dir = Path(DB_DIRECTORY_PATH) / "preprocessed"
    os.makedirs(preprocessed_dir, exist_ok=True)

    # --- LSH and MinHashes Initialization ---
    lsh = MinHashLSH(threshold=JACCARD_THRESHOLD, num_perm=SIGNATURE_SIZE)
    minhashes = {}

    # --- Populate LSH and MinHashes ---
    print("Generating MinHashes...")
    for table_name, columns in sample_data.items():
        for column_name, values in columns.items():
            for value in values:
                key = f"{table_name}_{column_name}_{value}"
                minhash_obj = create_minhash(value, SIGNATURE_SIZE, N_GRAM)
                minhashes[key] = (minhash_obj, table_name, column_name, value)
                lsh.insert(key, minhash_obj)
    print("MinHashes generated.")

    # --- Save LSH and MinHashes ---
    db_id = Path(DB_DIRECTORY_PATH).name
    lsh_path = preprocessed_dir / f"{db_id}_lsh.pkl"
    minhashes_path = preprocessed_dir / f"{db_id}_minhashes.pkl"

    with open(lsh_path, "wb") as f:
        pickle.dump(lsh, f)
    print(f"LSH index saved to {lsh_path}")

    with open(minhashes_path, "wb") as f:
        pickle.dump(minhashes, f)
    print(f"MinHashes saved to {minhashes_path}")

if __name__ == "__main__":
    generate_test_data()
