# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django import forms
from django.contrib import admin
from django.contrib.auth.models import Group, User
from django.contrib.auth.admin import GroupAdmin as BaseGroupAdmin, UserAdmin as BaseUserAdmin
from django.utils.safestring import mark_safe
from django.contrib import messages
from django.http import HttpResponseRedirect

from thoth_core.utilities.utils import export_csv, import_csv, export_selected_columns_to_csv, export_selected_tables_to_csv, export_db_structure_to_csv
from .dbmanagement import create_tables, create_columns, create_relationships, create_db_elements
from thoth_core.thoth_ai.thoth_workflow.create_table_comments import create_table_comments
from thoth_core.thoth_ai.thoth_workflow.create_column_comments import create_selected_column_comments
from thoth_core.thoth_ai.thoth_workflow.create_db_scope import generate_scope
from thoth_core.thoth_ai.thoth_workflow.async_table_comments import (
    start_async_table_comments,
    start_async_column_comments
)
from thoth_core.utilities.task_validation import check_task_can_start, force_reset_task_status

from django.db import models

from .models import (
    BasicAiModel,
    AiModel,
    SqlDb,
    SqlTable,
    SqlColumn,
    ColumnDataTypes,
    VectorDb,
    Workspace,
    Setting,
    Relationship,
    Agent, AgentChoices,
    GroupProfile,
)

export_csv.short_description = "Export selected items to CSV"
import_csv.short_description = "Import from CSV"
export_selected_columns_to_csv.short_description = "Download selected columns to local CSV file"
export_selected_tables_to_csv.short_description = "Download selected tables to local CSV file"
export_db_structure_to_csv.short_description = "Export database structure to CSV files"

class ApiKeyField(forms.CharField):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.widget = forms.TextInput(attrs={'style': 'width:350px;'})
        self.required = False  # Rendi il campo non obbligatorio

    def prepare_value(self, value):
        # Se il valore esiste e non è vuoto, mostra asterischi + ultimi 4 caratteri
        if value and not getattr(self, '_just_submitted', False):
            return '*' * 36 + value[-4:] if len(value) > 4 else value
        return value

    def clean(self, value):
        # Se il valore è tutto asterischi tranne gli ultimi 4 caratteri, mantieni il valore originale
        if value and '*' in value:
            original_value = self.initial
            return original_value
        # Se il valore è vuoto o None, restituisci una stringa vuota per cancellare l'API key
        elif value is None or value.strip() == '':
            return ''
        # Altrimenti restituisci il nuovo valore inserito
        return value

@admin.register(BasicAiModel)  # Add this decorator and class
class BasicAiModelAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'provider')
    search_fields = ('name', 'provider')
    list_filter = ('name', 'description','provider',)
    actions = (export_csv, import_csv)

class AiModelAdminForm(forms.ModelForm):
    api_key = ApiKeyField()
    class Meta:
        model = AiModel
        fields = '__all__'
        widgets = {
            'specific_model': forms.TextInput(attrs={'style': 'width: 250px;'}),
            'name': forms.TextInput(attrs={'style': 'width: 500px;'}),
            'url': forms.URLInput(attrs={'style': 'width: 500px;'}),
        }

    def clean_api_key(self):
        api_key = self.cleaned_data.get('api_key')
        # Se il campo è vuoto, restituisci una stringa vuota per cancellare l'API key
        if api_key == '':
            return ''
        # Altrimenti restituisci il valore (che potrebbe essere il valore originale o un nuovo valore)
        return api_key

@admin.register(AiModel)
class AiModelAdmin(admin.ModelAdmin):
    form = AiModelAdminForm
    list_display = ('specific_model', 'get_basic_model', 'name')
    search_fields = ('basic_model__name', 'specific_model', 'name')
    list_filter = ('basic_model__name',)
    fieldsets = [
        (
            None,
            {
                "fields": ['basic_model', 'specific_model', 'name', 'api_key', 'url'],
            }
        ),
        (
            "Advanced",
            {
                "classes": ['collapse'],
                "fields": ['temperature_allowed','temperature','top_p','max_tokens','timeout'],
            }
        ),
    ]
    actions = (export_csv, import_csv)

    def get_basic_model(self, obj):
        return obj.basic_model.name if obj.basic_model else '-'
    get_basic_model.short_description = 'Basic Model'
    get_basic_model.admin_order_field = 'basic_model__name'

@admin.register(SqlDb)
class SqlDbAdmin(admin.ModelAdmin):
    list_display = ('name', 'db_host', 'db_type', 'db_name', 'schema', 'vector_db_name')
    search_fields = ('name', 'db_host', 'db_type', 'db_name', 'schema')
    actions = (export_csv, import_csv, create_tables, create_relationships, create_db_elements, 'validate_db_fk_fields', export_db_structure_to_csv, generate_scope)

    def vector_db_name(self, obj):
        return obj.vector_db.name if obj.vector_db else None
    vector_db_name.short_description = 'Vector DB'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "vector_db":
            kwargs["queryset"] = VectorDb.objects.all().order_by('name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def validate_db_fk_fields(self, request, queryset):
        """
        Validates the format of fk_field for all columns in all tables of the selected databases.
        """
        total_error_count = 0
        total_success_count = 0
        all_error_messages = []
        total_tables = 0
        total_checked = 0
        
        for db in queryset:
            tables = SqlTable.objects.filter(sql_db=db)
            total_tables += tables.count()
            
            for table in tables:
                columns = SqlColumn.objects.filter(sql_table=table)
                error_count, success_count, error_messages, checked = validate_fk_fields(columns, request)
                
                total_error_count += error_count
                total_success_count += success_count
                all_error_messages.extend(error_messages)
                total_checked += checked
    
        # Display error messages (limit to avoid overwhelming the admin interface)
        max_errors_to_show = 50
        for error_msg in all_error_messages[:max_errors_to_show]:
            messages.error(request, error_msg)
        
        if len(all_error_messages) > max_errors_to_show:
            messages.warning(request, f"Showing only {max_errors_to_show} of {len(all_error_messages)} errors.")
        
        # Display summary
        if total_error_count == 0:
            if total_checked > 0:
                messages.success(request, f"All {total_checked} foreign key references across {total_tables} tables in {queryset.count()} databases are valid.")
            else:
                messages.info(request, f"No foreign key references found to validate in {total_tables} tables across {queryset.count()} databases.")
        else:
            messages.warning(
                request, 
                f"Validation completed with {total_error_count} errors and {total_success_count} valid references out of {total_checked} checked across {total_tables} tables in {queryset.count()} databases."
            )

    validate_db_fk_fields.short_description = "Validate foreign key fields in all tables of selected databases"

class SqlTableAdminForm(forms.ModelForm):
    sql_db = forms.ModelChoiceField(
        queryset=SqlDb.objects.all(),
        label="SQL Db",
        widget=forms.Select(attrs={'style': 'width: 250px;'})
    )

    class Meta:
        model = SqlTable
        fields = '__all__'

class SqlDbFilter(admin.SimpleListFilter):
    title = 'Database'
    parameter_name = 'sql_db'

    def lookups(self, request, model_admin):
        dbs = SqlDb.objects.all().order_by('name')
        return [(db.id, db.name) for db in dbs]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(sql_db__id=self.value())
        return queryset

@admin.register(SqlTable)
class SqlTableAdmin(admin.ModelAdmin):
    list_display = ('db_name','name', 'description', 'generated_comment')
    search_fields = ('sql_db__name','name', 'description', 'generated_comment')
    list_filter = (SqlDbFilter,)
    actions = (export_csv, import_csv, create_columns, 'validate_table_fk_fields', 'clean_invalid_pk_fk_fields', 'copy_generated_to_description', create_table_comments, 'create_table_comments_async')

    def db_name(self, obj):
        return obj.sql_db.name

    db_name.short_description = 'Database'
    create_table_comments.short_description = 'Generate selected tables comment (AI assisted)'
    
    def create_table_comments_async(self, request, queryset):
        """
        Async version of table comment generation to prevent timeouts.
        Uses background processing with status tracking.
        """
        if not queryset.exists():
            self.message_user(request, "No tables selected for comment generation.", level=messages.WARNING)
            return
            
        # Check if we have a current workspace
        if not hasattr(request, 'current_workspace') or not request.current_workspace:
            self.message_user(request, "No active workspace found. Please select a workspace.", level=messages.ERROR)
            return
            
        workspace = request.current_workspace
        
        # Verify that all selected tables belong to the workspace's database
        first_table = queryset.first()
        if first_table.sql_db != workspace.sql_db:
            self.message_user(
                request,
                f"Selected tables do not belong to the current workspace database '{workspace.sql_db.name}'. "
                f"Please ensure you're working with tables from the correct database.",
                level=messages.ERROR
            )
            return
            
        # Check if a new task can be started (with intelligent validation)
        can_start, message = check_task_can_start(workspace, 'table_comment')
        if not can_start:
            self.message_user(
                request,
                f"Cannot start table comment generation: {message}. "
                f"Current status: {workspace.table_comment_status}",
                level=messages.WARNING
            )
            return
            
        # Get all table IDs
        table_ids = list(queryset.values_list('id', flat=True))
        
        # Start async task with workspace ID
        task_id = start_async_table_comments(workspace.id, table_ids, request.user.id)
        
        # Update workspace status
        workspace.table_comment_status = Workspace.PreprocessingStatus.RUNNING
        workspace.table_comment_task_id = task_id
        workspace.table_comment_log = f"Started processing {len(table_ids)} tables"
        workspace.save()
        
        self.message_user(
            request,
            f"Started async table comment generation for {len(table_ids)} tables. "
            f"Task ID: {task_id}. Check the workspace status for progress.",
            level=messages.SUCCESS
        )
    
    create_table_comments_async.short_description = 'Generate selected tables comment (AI assisted - async)'
    
    def clean_invalid_pk_fk_fields(self, request, queryset):
        """
        Clean invalid pk_field and fk_field values for all columns in the selected tables.
        Valid pk_field should contain 'PK' or be empty.
        Valid fk_field should be in the format 'table.column_name' or a list of such references.
        """
        total_tables = queryset.count()
        total_columns_updated = 0
        
        for table in queryset:
            columns_updated = 0
            columns = table.columns.all()
            
            for column in columns:
                updated = False
                
                # Check and clean pk_field
                if column.pk_field and 'PK' not in column.pk_field:
                    column.pk_field = ''
                    updated = True
                
                # Check and clean fk_field
                if column.fk_field:
                    # Check if fk_field is in the format 'table.column' or a list of such references
                    valid_format = False
                    
                    # Split by comma to handle lists of references
                    references = [ref.strip() for ref in column.fk_field.split(',')]
                    
                    # Check each reference
                    valid_references = []
                    for ref in references:
                        # Check if reference is in format 'table.column'
                        parts = ref.split('.')
                        if len(parts) == 2:
                            valid_references.append(ref)
                
                    # If we have valid references, join them back together
                    if valid_references:
                        column.fk_field = ', '.join(valid_references)
                    else:
                        column.fk_field = ''
                        updated = True
                
                if updated:
                    column.save()
                    columns_updated += 1
        
            total_columns_updated += columns_updated
            self.message_user(
                request, 
                f"Cleaned {columns_updated} columns in table '{table.name}'."
            )
        
        self.message_user(
            request, 
            f"Completed cleaning invalid PK/FK fields in {total_tables} tables. Total columns updated: {total_columns_updated}."
        )

    clean_invalid_pk_fk_fields.short_description = "Clean invalid PK/FK fields in selected tables"

    def copy_generated_to_description(self, request, queryset):
        """
        Copy generated_comment to description only if generated_comment contains text.
        """
        updated_count = 0
        total_count = queryset.count()
        
        for table in queryset:
            if table.generated_comment and table.generated_comment.strip():
                table.description = table.generated_comment
                table.save()
                updated_count += 1
        
        if updated_count == 0:
            self.message_user(request, f"No tables updated - none of the {total_count} selected tables had text in generated_comment field.")
        else:
            self.message_user(request, f"{updated_count} of {total_count} tables updated successfully (only tables with text in generated_comment were updated).")
    
    copy_generated_to_description.short_description = "Copy generated comment to description"

    def validate_table_fk_fields(self, request, queryset):
        """
        Validates the format of fk_field for all columns in the selected tables.
        """
        total_error_count = 0
        total_success_count = 0
        all_error_messages = []
        total_checked = 0
        
        for table in queryset:
            columns = SqlColumn.objects.filter(sql_table=table)
            error_count, success_count, error_messages, checked = validate_fk_fields(columns, request)
            
            total_error_count += error_count
            total_success_count += success_count
            all_error_messages.extend(error_messages)
            total_checked += checked
        
        # Display error messages
        for error_msg in all_error_messages:
            messages.error(request, error_msg)
        
        # Display summary
        if total_error_count == 0:
            if total_checked > 0:
                messages.success(request, f"All {total_checked} foreign key references across {queryset.count()} tables are valid.")
            else:
                messages.info(request, f"No foreign key references found to validate in the {queryset.count()} selected tables.")
        else:
            messages.warning(
                request, 
                f"Validation completed with {total_error_count} errors and {total_success_count} valid references out of {total_checked} checked across {queryset.count()} tables."
            )

    validate_table_fk_fields.short_description = "Validate foreign key fields in all columns of selected tables"

class SqlColumnAdminForm(forms.ModelForm):
    sql_table = forms.ModelChoiceField(
        queryset=SqlTable.objects.all(),
        label="SQL Table",
        widget=forms.Select(attrs={'style': 'width: 300px;'}),
    )

    class Meta:
        model = SqlColumn
        fields = ['original_column_name', 'column_name', 'data_format', 'column_description', 'generated_comment', 'value_description', 'sql_table', 'pk_field', 'fk_field']
        widgets = {
            'original_column_name': forms.TextInput(attrs={'style': 'width: 300px;'}),
            'column_name': forms.TextInput(attrs={'style': 'width: 300px;'}),
            'data_format': forms.Select(choices=ColumnDataTypes, attrs={'style': 'width: 200px;'}),
            'column_description': forms.Textarea(attrs={'rows': 3, 'cols': 40}),
            'generated_comment': forms.Textarea(attrs={'rows': 3, 'cols': 40}),
            'value_description': forms.Textarea(attrs={'rows': 3, 'cols': 40}),
            'pk_field': forms.Textarea(attrs={'rows': 3, 'cols': 40}),
            'fk_field': forms.Textarea(attrs={'rows': 3, 'cols': 40}),
         }

class SqlDbColumnFilter(admin.SimpleListFilter):
    title = 'Database'
    parameter_name = 'sql_db'

    def lookups(self, request, model_admin):
        dbs = SqlDb.objects.all().order_by('name')
        return [(db.name, db.name) for db in dbs]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(sql_table__sql_db__name=self.value())
        return queryset

class SqlTableColumnFilter(admin.SimpleListFilter):
    title = 'Table'
    parameter_name = 'sql_table'

    def lookups(self, request, model_admin):
        # Get the selected database name from the URL parameters
        db_name = request.GET.get('sql_db')
        if db_name:
            # If a database is selected, filter tables by that database
            tables = SqlTable.objects.filter(sql_db__name=db_name).order_by('name')
        else:
            # If no database is selected, show all tables
            tables = SqlTable.objects.all().order_by('name')
        return [(table.name, table.name) for table in tables]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(sql_table__name=self.value())
        return queryset

@admin.register(SqlColumn)
class SqlColumnAdmin(admin.ModelAdmin):
    form = SqlColumnAdminForm
    list_display = (
    'original_column_name', 'column_name', 'data_format', 'column_description', 'generated_comment', 'value_description', 'get_table_name', 'get_db_name', 'pk_field', 'fk_field')
    list_filter = (SqlDbColumnFilter, SqlTableColumnFilter)
    search_fields = ('original_column_name', 'column_name')
    actions = [export_csv, import_csv, 'copy_generated_to_description', 'copy_original_name_to_name', 'copy_name_to_original_name', 'validate_fk_field_format', create_selected_column_comments,  'create_column_comments_async']
    create_selected_column_comments.short_description = 'Generate selected columns comment (AI assisted)'
    
    def create_column_comments_async(self, request, queryset):
        """
        Async version of column comment generation to prevent timeouts.
        Uses background processing with status tracking.
        """
        if not queryset.exists():
            self.message_user(request, "No columns selected for comment generation.", level=messages.WARNING)
            return
            
        # Check if we have a current workspace
        if not hasattr(request, 'current_workspace') or not request.current_workspace:
            self.message_user(request, "No active workspace found. Please select a workspace.", level=messages.ERROR)
            return
            
        workspace = request.current_workspace
        
        # Verify that all selected columns belong to the workspace's database
        first_column = queryset.first()
        if first_column.sql_table.sql_db != workspace.sql_db:
            self.message_user(
                request,
                f"Selected columns do not belong to the current workspace database '{workspace.sql_db.name}'. "
                f"Please ensure you're working with columns from the correct database.",
                level=messages.ERROR
            )
            return
            
        # Check if a new task can be started (with intelligent validation)
        can_start, message = check_task_can_start(workspace, 'column_comment')
        if not can_start:
            self.message_user(
                request,
                f"Cannot start column comment generation: {message}. "
                f"Current status: {workspace.column_comment_status}",
                level=messages.WARNING
            )
            return
            
        # Get all column IDs
        column_ids = list(queryset.values_list('id', flat=True))
        
        # Start async task with workspace ID
        task_id = start_async_column_comments(workspace.id, column_ids, request.user.id)
        
        # Update workspace status
        workspace.column_comment_status = Workspace.PreprocessingStatus.RUNNING
        workspace.column_comment_task_id = task_id
        workspace.column_comment_log = f"Started processing {len(column_ids)} columns"
        workspace.save()
        
        self.message_user(
            request,
            f"Started async column comment generation for {len(column_ids)} columns. "
            f"Task ID: {task_id}. Check the workspace status for progress.",
            level=messages.SUCCESS
        )
    
    create_column_comments_async.short_description = 'Generate selected columns comment (AI assisted - async)'
    
    def get_table_name(self, obj):
        return obj.sql_table.name
    get_table_name.short_description = 'Table Name'

    def get_db_name(self, obj):
        return obj.sql_table.sql_db.name
    get_db_name.short_description = 'Database Name'

    def get_queryset(self, request):
        # Store the request object so it can be accessed in formfield_for_foreignkey
        self.request = request
        return super().get_queryset(request)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "sql_table":
            db_name = request.GET.get('sql_db')
            if db_name:
                kwargs["queryset"] = SqlTable.objects.filter(sql_db__name=db_name).order_by('name')
            else:
                kwargs["queryset"] = SqlTable.objects.select_related('sql_db').order_by('sql_db__name', 'name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ('sql_table',)
        return self.readonly_fields

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj)
        if obj:  # editing an existing object
            fields = [f for f in fields if f != 'sql_table'] + ['sql_table']
        return fields

    def copy_generated_to_description(self, request, queryset):
        """
        Copy generated_comment to column_description only if generated_comment contains text.
        """
        updated_count = 0
        total_count = queryset.count()
        
        for column in queryset:
            if column.generated_comment and column.generated_comment.strip():
                column.column_description = column.generated_comment
                column.save()
                updated_count += 1
        
        if updated_count == 0:
            self.message_user(request, f"No columns updated - none of the {total_count} selected columns had text in generated_comment field.")
        else:
            self.message_user(request, f"{updated_count} of {total_count} columns updated successfully (only columns with text in generated_comment were updated).")
    copy_generated_to_description.short_description = "Copy generated comment to column description"

    def copy_original_name_to_name(self, request, queryset):
        updated = queryset.update(column_name=models.F('original_column_name'))
        self.message_user(request, f"{updated} columns updated successfully.")
    copy_original_name_to_name.short_description = "Copy original column name to column name"

    def copy_name_to_original_name(self, request, queryset):
        updated = queryset.update(original_column_name=models.F('column_name'))
        self.message_user(request, f"{updated} columns updated successfully.")
    copy_name_to_original_name.short_description = "Copy column name to original column name"

    def validate_fk_field_format(self, request, queryset):
        """
        Validates the format of fk_field for selected columns.
        """
        error_count, success_count, error_messages, total_checked = validate_fk_fields(queryset, request)
        
        # Display error messages
        for error_msg in error_messages:
            messages.error(request, error_msg)
        
        # Display summary
        if error_count == 0:
            if total_checked > 0:
                messages.success(request, f"All {total_checked} foreign key references are valid.")
            else:
                messages.info(request, "No foreign key references found to validate.")
        else:
            messages.warning(
                request, 
                f"Validation completed with {error_count} errors and {success_count} valid references out of {total_checked} checked."
            )
            
    validate_fk_field_format.short_description = "Validate foreign key field format"

@admin.register(VectorDb)
class VectorDbAdmin(admin.ModelAdmin):
    list_display = ('name', 'vect_type', 'host', 'port')
    search_fields = ('name', 'vect_type', 'host', 'port')
    actions = (export_csv, import_csv)

@admin.register(Workspace)
class WorkspaceAdmin(admin.ModelAdmin):
    list_display = ('name', 'level', 'description', 'id', 'get_table_comment_status', 'get_column_comment_status', 'get_preprocessing_status')
    list_display_links = ('name',)
    search_fields = ('name', 'description')
    list_filter = ('level', 'table_comment_status', 'column_comment_status', 'preprocessing_status')
    filter_horizontal = ('users', 'default_workspace')
    actions = (export_csv, import_csv, 'duplicate_workspace')
    readonly_fields = ('table_comment_status', 'table_comment_task_id', 'table_comment_log',
                      'column_comment_status', 'column_comment_task_id', 'column_comment_log',
                      'preprocessing_status', 'task_id', 'last_preprocess_log')

    fieldsets = [
        (None, {
            'fields': ['name', 'level', 'description']
        }),
        ('Users', {
            'fields': ['users', 'default_workspace']
        }),
        ('Database and AI Models', {
            'fields': ['sql_db', 'explanation_ai_model', 'plotly_generator_ai_model']
        }),
        ('Agents Configuration', {
            'fields': [
                'default_agent',
                'question_validator',
                ('kw_sel_agent_1', 'kw_sel_agent_2'),
                ('sel_columns_agent_1', 'sel_columns_agent_2'),
                ('sql_basic_agent', 'sql_advanced_agent', 'sql_expert_agent'),
                ('sql_titan_agent_1', 'sql_titan_agent_2', 'sql_titan_agent_3'),  # Updated this line
                ('test_gen_agent_1', 'test_gen_agent_2'),
                ('test_exec_agent', 'explain_sql_agent'),
                ('ask_human_help_agent',)
            ]
        }),
        ('Settings', {
            'fields': ['setting']
        }),
        ('Tables and Columns Async Processing Status', {
            'classes': ['collapse'],
            'fields': [
                'table_comment_status',
                'table_comment_task_id',
                'table_comment_log',
                'column_comment_status',
                'column_comment_task_id',
                'column_comment_log',
            ]
        }),
        ('Preprocessing Async Processing Status', {
            'classes': ['collapse'],
            'fields': [
                'preprocessing_status',
                'task_id',
                'last_preprocess_log',
            ]
        })]

    def get_table_comment_status(self, obj):
        """Display table comment generation status with color coding."""
        status = obj.table_comment_status
        if status == 'RUNNING':
            return mark_safe(f'<span style="color: orange;">{status}</span>')
        elif status == 'COMPLETED':
            return mark_safe(f'<span style="color: green;">{status}</span>')
        elif status == 'FAILED':
            return mark_safe(f'<span style="color: red;">{status}</span>')
        else:
            return status
    get_table_comment_status.short_description = 'Table Comments'

    def get_column_comment_status(self, obj):
        """Display column comment generation status with color coding."""
        status = obj.column_comment_status
        if status == 'RUNNING':
            return mark_safe(f'<span style="color: orange;">{status}</span>')
        elif status == 'COMPLETED':
            return mark_safe(f'<span style="color: green;">{status}</span>')
        elif status == 'FAILED':
            return mark_safe(f'<span style="color: red;">{status}</span>')
        else:
            return status
    get_column_comment_status.short_description = 'Column Comments'

    def get_preprocessing_status(self, obj):
        """Display preprocessing status with color coding."""
        status = obj.preprocessing_status
        if status == 'RUNNING':
            return mark_safe(f'<span style="color: orange;">{status}</span>')
        elif status == 'COMPLETED':
            return mark_safe(f'<span style="color: green;">{status}</span>')
        elif status == 'FAILED':
            return mark_safe(f'<span style="color: red;">{status}</span>')
        else:
            return status
    get_preprocessing_status.short_description = 'Preprocessing'

    def get_form(self, request, obj=None, **kwargs):
        """
        Override get_form to ensure agent querysets are always fresh.
        This fixes the issue where newly created agents don't appear in select fields.
        """
        form = super().get_form(request, obj, **kwargs)
        
        # Get all agent field names
        agent_fields = [field_name for field_name in form.base_fields.keys()
                       if field_name.endswith('_agent') or field_name.endswith('_agent_1') or field_name.endswith('_agent_2') or field_name.endswith('_agent_3')]
        
        # Update queryset for all agent fields to ensure fresh data
        fresh_agent_queryset = Agent.objects.all().select_related('ai_model').order_by('name')
        for field_name in agent_fields:
            if field_name in form.base_fields:
                form.base_fields[field_name].queryset = fresh_agent_queryset
        
        return form

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name.endswith('_agent_1') or db_field.name.endswith('_agent_2') or \
           db_field.name.endswith('_agent') or db_field.name == 'question_validator':
            kwargs["queryset"] = Agent.objects.all().select_related('ai_model').order_by('name')
        elif db_field.name == 'sql_db':
            kwargs["queryset"] = SqlDb.objects.all().order_by('name')
        elif db_field.name == 'explanation_ai_model' or db_field.name == 'plotly_generator_ai_model':
            kwargs["queryset"] = AiModel.objects.all().select_related('basic_model').order_by('name')
        elif db_field.name == 'setting':
            kwargs["queryset"] = Setting.objects.all().order_by('name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    class Media:
        css = {
            'all': ('admin/css/forms.css',)
        }

    def get_agents_list(self, obj):
        # Creiamo un dizionario con tutti i possibili tipi di agente, escludendo DEFAULT
        agents_by_type = {choice[0]: None for choice in AgentChoices.choices
                         if choice[0] != 'DEFAULT'}
        
        # Popoliamo il dizionario con gli agenti esistenti, escludendo DEFAULT
        for agent in obj.agents.all().select_related('ai_model').exclude(agent_type='DEFAULT'):
            agents_by_type[agent.agent_type] = agent

        # Costruiamo l'HTML della lista
        html = ['<ul style="list-style-type: none; padding-left: 0;">']
        for agent_type, _ in [(choice[0], choice[1]) for choice in AgentChoices.choices
                             if choice[0] != 'DEFAULT']:
            agent = agents_by_type[agent_type]
            if agent:
                model_name = agent.ai_model.name if agent.ai_model else 'No model'
                html.append(f'<li style="margin-bottom: 5px;">'
                          f'<strong>{agent.get_agent_type_display()}</strong>: {model_name}'
                          f'</li>')
            else:
                html.append(f'<li style="margin-bottom: 5px; color: #999;">'
                          f'<strong>{dict(AgentChoices.choices)[agent_type]}</strong>: Not configured'
                          f'</li>')
        html.append('</ul>')
        
        return mark_safe(''.join(html))
    get_agents_list.short_description = 'Configured Agents'

    def duplicate_workspace(self, request, queryset):
        """
        Duplicate selected workspaces with name + "copy"
        """
        from django.contrib import messages
        from django.utils import timezone

        duplicated_count = 0

        for workspace in queryset:
            try:
                # Store the original many-to-many relationships
                original_users = list(workspace.users.all())
                original_default_workspace = list(workspace.default_workspace.all())

                # Create the duplicate
                workspace.pk = None  # This will create a new instance when saved
                workspace.id = None  # Ensure the ID is also reset
                workspace.name = f"{workspace.name} copy"

                # Reset status fields to default values for the copy
                workspace.preprocessing_status = Workspace.PreprocessingStatus.IDLE
                workspace.task_id = None
                workspace.last_preprocess_log = None
                workspace.preprocessing_start_time = None
                workspace.preprocessing_end_time = None

                workspace.table_comment_status = Workspace.PreprocessingStatus.IDLE
                workspace.table_comment_task_id = None
                workspace.table_comment_log = None
                workspace.table_comment_start_time = None
                workspace.table_comment_end_time = None

                workspace.column_comment_status = Workspace.PreprocessingStatus.IDLE
                workspace.column_comment_task_id = None
                workspace.column_comment_log = None
                workspace.column_comment_start_time = None
                workspace.column_comment_end_time = None

                # Reset timestamps
                workspace.last_preprocess = None
                workspace.last_hint_load = None
                workspace.last_sql_loaded = None
                workspace.created_at = timezone.now()
                workspace.updated_at = timezone.now()

                # Save the new workspace
                workspace.save()

                # Restore the many-to-many relationships
                workspace.users.set(original_users)
                workspace.default_workspace.set(original_default_workspace)

                duplicated_count += 1

            except Exception as e:
                messages.error(request, f"Error duplicating workspace '{workspace.name}': {str(e)}")
                continue

        if duplicated_count > 0:
            messages.success(request, f"Successfully duplicated {duplicated_count} workspace(s).")
        else:
            messages.warning(request, "No workspaces were duplicated.")

    duplicate_workspace.short_description = "Duplicate selected workspaces"


@admin.register(Setting)
class SettingAdmin(admin.ModelAdmin):
    list_display = ('name', 'theme', 'language')
    search_fields = ('name',)
    actions = (export_csv, import_csv)
    fieldsets = [
        (
            None,
            {
                "fields": ['name', 'theme',],
            }
        ),
        (
            "Comments generation",
            {
                "classes": ['collapse'],
                "fields": ['language', 'example_rows_for_comment', 'system_prompt', 'comment_model'],
            }
        ),
        (
            "LHS similarity process",
            {
                "classes": ['collapse'],
                "fields": ['signature_size', 'n_grams', 'threshold', 'verbose','use_value_description'],
            }
        ),
    ]

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "comment_model":
            kwargs["queryset"] = AiModel.objects.all().order_by('name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # Aggiungiamo attributi CSS per le textarea dei log
        if 'last_preprocess_log' in form.base_fields:
            form.base_fields['last_preprocess_log'].widget = forms.Textarea(attrs={
                'rows': 5,
                'cols': 80,
                'style': 'width: 80%;'
            })
        if 'table_comment_log' in form.base_fields:
            form.base_fields['table_comment_log'].widget = forms.Textarea(attrs={
                'rows': 5,
                'cols': 80,
                'style': 'width: 80%;'
            })
        if 'column_comment_log' in form.base_fields:
            form.base_fields['column_comment_log'].widget = forms.Textarea(attrs={
                'rows': 5,
                'cols': 80,
                'style': 'width: 80%;'
            })
        return form

class RelationshipAdminForm(forms.ModelForm):
    source_column = forms.ModelChoiceField(
        queryset=SqlColumn.objects.all(),
        label="Source Column",
        widget=forms.Select(attrs={'style': 'width: 300px;'})
    )
    target_column = forms.ModelChoiceField(
        queryset=SqlColumn.objects.all(),
        label="Target Column",
        widget=forms.Select(attrs={'style': 'width: 300px;'})
    )

    class Meta:
        model = Relationship
        fields = '__all__'
        widgets = {
            'source_table': forms.Select(attrs={
                'style': 'width: 300px;'
            }),
            'target_table': forms.Select(attrs={
                'style': 'width: 300px;'
            }),
        }

    def __init__(self, *args, **kwargs):
        # Extract the request object before calling super().__init__
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Initialize with all columns
        source_columns = SqlColumn.objects.all().order_by('sql_table__name', 'original_column_name')
        target_columns = SqlColumn.objects.all().order_by('sql_table__name', 'original_column_name')
        
        # If we have a request object, we can filter based on URL parameters
        if self.request:
            # Check if a database is selected
            db_id = self.request.GET.get('sql_db')
            if db_id:
                # Filter columns by database
                source_columns = source_columns.filter(sql_table__sql_db__id=db_id)
                target_columns = target_columns.filter(sql_table__sql_db__id=db_id)
        
        # Set the filtered querysets
        self.fields['source_column'].queryset = source_columns
        self.fields['target_column'].queryset = target_columns
        
        # Set the label formatting
        self.fields['source_column'].label_from_instance = self.label_from_instance
        self.fields['target_column'].label_from_instance = self.label_from_instance

    def label_from_instance(self, obj):
        return f"{obj.sql_table.name}.{obj.original_column_name}"

class SqlDbRelationshipFilter(admin.SimpleListFilter):
    title = 'Database'
    parameter_name = 'sql_db'

    def lookups(self, request, model_admin):
        dbs = SqlDb.objects.all().order_by('name')
        return [(db.id, db.name) for db in dbs]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(source_table__sql_db__id=self.value())
        return queryset

class SqlTableSourceRelationshipFilter(admin.SimpleListFilter):
    title = 'Source Table'
    parameter_name = 'source_table'

    def lookups(self, request, model_admin):
        # Get the selected database ID from the URL parameters
        db_id = request.GET.get('sql_db')
        if db_id:
            # If a database is selected, filter tables by that database
            tables = SqlTable.objects.filter(sql_db__id=db_id).order_by('name')
        else:
            # If no database is selected, show all tables
            tables = SqlTable.objects.all().order_by('name')
        return [(table.id, table.name) for table in tables]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(source_table__id=self.value())
        return queryset

class SqlTableTargetRelationshipFilter(admin.SimpleListFilter):
    title = 'Target Table'
    parameter_name = 'target_table'

    def lookups(self, request, model_admin):
        # Get the selected database ID from the URL parameters
        db_id = request.GET.get('sql_db')
        if db_id:
            # If a database is selected, filter tables by that database
            tables = SqlTable.objects.filter(sql_db__id=db_id).order_by('name')
        else:
            # If no database is selected, show all tables
            tables = SqlTable.objects.all().order_by('name')
        return [(table.id, table.name) for table in tables]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(target_table__id=self.value())
        return queryset

@admin.register(Relationship)
class RelationshipAdmin(admin.ModelAdmin):
    form = RelationshipAdminForm
    list_display = ('source_db', 'source_table_name', 'source_column_name', 'target_table_name', 'target_column_name')
    list_filter = (SqlDbRelationshipFilter, SqlTableSourceRelationshipFilter, SqlTableTargetRelationshipFilter)
    search_fields = ('source_table__name', 'target_table__name', 'source_column__original_column_name', 'target_column__original_column_name')
    actions = (export_csv, import_csv, 'update_pk_fk_fields')
    
    def get_form(self, request, obj=None, **kwargs):
        """
        Override get_form to pass the request to the form for filtering columns
        based on selected database and tables.
        """
        form = super().get_form(request, obj, **kwargs)
        form.request = request
        return form
    
    def get_changelist_form(self, request, **kwargs):
        """
        Override get_changelist_form to pass the request to the form for filtering columns
        in the changelist view.
        """
        kwargs['request'] = request
        return super().get_changelist_form(request, **kwargs)
    
    class Media:
        js = ('admin/js/jquery.init.js', 'js/relationship_admin.js',)

    def source_db(self, obj):
        return obj.source_table.sql_db.name
    source_db.short_description = 'Database'

    def source_table_name(self, obj):
        return obj.source_table.name
    source_table_name.short_description = 'Source Table'

    def source_column_name(self, obj):
        return obj.source_column.original_column_name
    source_column_name.short_description = 'Source Column'

    def target_table_name(self, obj):
        return obj.target_table.name
    target_table_name.short_description = 'Target Table'

    def target_column_name(self, obj):
        return obj.target_column.original_column_name
    target_column_name.short_description = 'Target Column'
    
    def update_pk_fk_fields(self, request, queryset):
        """
        Updates the pk_field and fk_field values for columns involved in the selected relationships:
        - Sets 'PK' in the pk_field of the target column
        - Adds 'table.column' reference to the fk_field of the source column
        """
        updated_pk_columns = 0
        updated_fk_columns = 0
        
        for relationship in queryset:
            # Update target column's pk_field to 'PK'
            target_column = relationship.target_column
            if not target_column.pk_field or 'PK' not in target_column.pk_field:
                target_column.pk_field = 'PK'
                target_column.save()
                updated_pk_columns += 1
            
            # Update source column's fk_field to include 'table.column' reference
            source_column = relationship.source_column
            target_reference = f"{relationship.target_table.name}.{relationship.target_column.original_column_name}"
            
            # Check if the reference already exists in the fk_field
            if source_column.fk_field:
                # Split existing references by comma and strip whitespace
                existing_references = [ref.strip() for ref in source_column.fk_field.split(',')]
                
                # Add the new reference if it doesn't already exist
                if target_reference not in existing_references:
                    existing_references.append(target_reference)
                    source_column.fk_field = ','.join(existing_references)  # No spaces after commas
                    source_column.save()
                    updated_fk_columns += 1
            else:
                # If fk_field is empty, just set it to the new reference
                source_column.fk_field = target_reference
                source_column.save()
                updated_fk_columns += 1
        
        self.message_user(
            request, 
            f"Successfully updated {updated_pk_columns} PK fields and {updated_fk_columns} FK fields for the selected relationships."
        )
    
    update_pk_fk_fields.short_description = "Update PK/FK fields for selected relationships"

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "source_table":
            kwargs["queryset"] = SqlTable.objects.select_related('sql_db').order_by('sql_db__name', 'name')
        elif db_field.name == "target_table":
            kwargs["queryset"] = SqlTable.objects.select_related('sql_db').order_by('sql_db__name', 'name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

class AgentAdminForm(forms.ModelForm):
    class Meta:
        model = Agent
        fields = '__all__'
        widgets = {
            'temperature': forms.NumberInput(attrs={'style': 'width: 100px;'}),
            'top_p': forms.NumberInput(attrs={'style': 'width: 100px;'}),
            'max_tokens': forms.NumberInput(attrs={'style': 'width: 100px;'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        # Il controllo che impediva di avere agenti dello stesso tipo nello stesso workspace è stato rimosso
        return cleaned_data

@admin.register(Agent)
class AgentAdmin(admin.ModelAdmin):
    form = AgentAdminForm
    list_display = ('name',  'agent_type', 'get_model', 'temperature', 'top_p', 'max_tokens', 'timeout', 'retries')
    list_filter = ('ai_model__basic_model', 'agent_type')
    search_fields = ('name',)
    actions = (export_csv, import_csv)

    def get_model(self, obj):
        return obj.ai_model.name if obj.ai_model else None
    get_model.short_description = 'AI Model'
    get_model.admin_order_field = 'ai_model__name'


    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "ai_model":
            kwargs["queryset"] = AiModel.objects.all().order_by('name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

# GroupProfile Inline for Group Admin
class GroupProfileInline(admin.StackedInline):
    model = GroupProfile
    max_num = 1
    min_num = 1
    can_delete = False
    extra = 0
    verbose_name = "Group Profile Settings"
    verbose_name_plural = "Group Profile Settings"
    
    fields = [
        'show_sql',
        'show_keywords',
        'show_hints',
        'show_process_info',
        'show_sql_generation',
        'explain_generated_query',
    ]
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs
    
    def has_add_permission(self, request, obj=None):
        return True
    
    def has_change_permission(self, request, obj=None):
        return True

# Custom Group Admin with GroupProfile inline
class GroupAdmin(BaseGroupAdmin):
    inlines = [GroupProfileInline]
    actions = [export_csv, import_csv]
    
    # BaseGroupAdmin.fieldsets is None, so we need to define our own
    fieldsets = (
        (None, {'fields': ('name', 'permissions')}),
    )

# Custom User Admin with export/import functionality
class UserAdmin(BaseUserAdmin):
    actions = list(BaseUserAdmin.actions) + [export_csv, import_csv]

# Unregister the default User admin and register our custom one
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

# Unregister the default Group admin and register our custom one
admin.site.unregister(Group)
admin.site.register(Group, GroupAdmin)

# Keep the standalone GroupProfileAdmin but make it less prominent
# (useful for bulk operations or advanced management)
@admin.register(GroupProfile)
class GroupProfileAdmin(admin.ModelAdmin):
    list_display = ('group_name', 'show_sql', 'show_keywords', 'show_hints', 'show_process_info',
                   'show_sql_generation', 'explain_generated_query')
    list_filter = ('show_sql', 'show_keywords', 'show_hints', 'show_process_info', 
                  'show_sql_generation', 'explain_generated_query')
    search_fields = ('group__name',)
    actions = (export_csv, import_csv)
    
    fieldsets = [
        (None, {
            'fields': ['group']
        }),
        ('Display Settings', {
            'fields': [
                ('show_sql', 'show_keywords'),
                ('show_hints', 'show_process_info'),
                'show_sql_generation',
                'explain_generated_query',
            ]
        })
    ]
    
    def group_name(self, obj):
        return obj.group.name
    group_name.short_description = 'Group Name'
    group_name.admin_order_field = 'group__name'
    
    class Meta:
        verbose_name = "Group Profile (Advanced)"
        verbose_name_plural = "Group Profiles (Advanced)"

# Funzione di validazione comune che può essere riutilizzata
def validate_fk_fields(columns, request=None):
    """
    Validates the format of fk_field for the given columns.
    Returns a tuple of (error_count, success_count, error_messages, total_checked)
    """
    error_count = 0
    success_count = 0
    error_messages = []
    total_checked = 0
    
    for column in columns:
        # Skip empty fk_field values without counting them
        if not column.fk_field or column.fk_field.strip() == '':
            continue
            
        # Increment the counter for columns with FK values
        total_checked += 1
        
        # Get the SQL database of the table this column belongs to
        sql_db = column.sql_table.sql_db
        
        # Parse references (either a single reference or comma-separated list)
        references = [ref.strip() for ref in column.fk_field.split(',')]
        column_has_error = False
        
        for reference in references:
            # Check format: tablename.columnname
            parts = reference.split('.')
            if len(parts) != 2:
                error_msg = (
                    f"Invalid format in column '{column.original_column_name}' of table '{column.sql_table.name}': "
                    f"'{reference}' should be in format 'tablename.columnname'"
                )
                error_messages.append(error_msg)
                column_has_error = True
                continue
                
            table_name, column_name = parts
            
            # Check if the referenced table exists in the same database
            try:
                ref_table = SqlTable.objects.get(name=table_name, sql_db=sql_db)
            except SqlTable.DoesNotExist:
                error_msg = (
                    f"Invalid reference in column '{column.original_column_name}' of table '{column.sql_table.name}': "
                    f"Table '{table_name}' does not exist in database '{sql_db.name}'"
                )
                error_messages.append(error_msg)
                column_has_error = True
                continue
                
            # Check if the referenced column exists in the referenced table
            if not SqlColumn.objects.filter(sql_table=ref_table, original_column_name=column_name).exists():
                error_msg = (
                    f"Invalid reference in column '{column.original_column_name}' of table '{column.sql_table.name}': "
                    f"Column '{column_name}' does not exist in table '{table_name}'"
                )
                error_messages.append(error_msg)
                column_has_error = True
                continue
        
        # Count errors per column, not per reference
        if column_has_error:
            error_count += 1
        else:
            success_count += 1
    
    return error_count, success_count, error_messages, total_checked
