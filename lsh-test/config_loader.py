"""
Configuration loader for LSH evaluation tool.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any


class ConfigLoader:
    """Loads and manages configuration from YAML file."""
    
    def __init__(self, config_path: str = "config.yml"):
        """Initialize configuration loader."""
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        with open(self.config_path, 'r') as file:
            config = yaml.safe_load(file)
        
        # Override with environment variables if available
        config['OPENROUTER_API_KEY'] = os.getenv('OPENROUTER_API_KEY', config.get('OPENROUTER_API_KEY', ''))
        config['OPENROUTER_API_BASE'] = os.getenv('OPENROUTER_API_BASE', config.get('OPENROUTER_API_BASE', ''))
        
        return config
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return self.config.get(key, default)
    
    def get_lsh_path(self) -> Path:
        """Get the LSH data path based on configuration."""
        db_root = Path(self.get('DB_ROOT_PATH'))
        db_mode = self.get('DB_MODE', 'dev')
        db_name = self.get('DB_NAME')
        
        return db_root / f"{db_mode}_databases" / db_name / "preprocessed"
    
    def validate(self) -> bool:
        """Validate required configuration values."""
        required_keys = [
            'DB_ROOT_PATH', 'DB_TYPE', 'DB_NAME', 'DB_MODE',
            'OPENROUTER_API_KEY', 'OPENROUTER_API_BASE'
        ]
        
        for key in required_keys:
            if not self.get(key):
                print(f"Missing required configuration: {key}")
                return False
        
        # Validate LSH path exists
        lsh_path = self.get_lsh_path()
        if not lsh_path.exists():
            print(f"LSH path does not exist: {lsh_path}")
            return False
        
        return True
