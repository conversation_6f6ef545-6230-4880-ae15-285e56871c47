Metadata-Version: 2.4
Name: thoth-sqldb
Version: 0.3.9
Summary: A Python module for managing SQL databases (SQLite and PostgreSQL) with support for LSH-based search.
Author-email: <PERSON> <<EMAIL>>
Project-URL: Homepage, https://github.com/mptyl/thoth_sqldb
Project-URL: Bug Tracker, https://github.com/mptyl/thoth_sqldb/issues
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Intended Audience :: Developers
Classifier: Topic :: Database
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: datasketch>=1.5.0
Requires-Dist: tqdm>=4.60.0
Requires-Dist: SQLAlchemy>=1.4.0
Requires-Dist: psycopg2-binary>=2.9.0
Dynamic: license-file

# Thoth SQL Database Manager

A Python module for managing SQL databases (SQLite and PostgreSQL) with support for LSH-based search.

## Installation

```bash
pip install thoth_sqldb
