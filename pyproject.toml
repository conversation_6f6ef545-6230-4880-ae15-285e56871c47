[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "thoth-dbmanager"
version = "0.4.0"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
]
description = "A Python library for managing SQL databases with support for multiple database types, LSH-based similarity search, and a modern plugin architecture."
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Intended Audience :: Developers",
    "Topic :: Database",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
dependencies = [
    "datasketch>=1.5.0",
    "tqdm>=4.60.0",
    "SQLAlchemy>=1.4.0",
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
postgresql = ["psycopg2-binary>=2.9.0"]
mysql = ["mysql-connector-python>=8.0.0"]
mariadb = ["mariadb>=1.1.0"]
sqlserver = ["pyodbc>=4.0.0"]
oracle = ["cx_Oracle>=8.3.0"]
informix = ["informixdb>=2.2.0"]
supabase = ["supabase>=2.0.0", "postgrest-py>=0.16.0", "gotrue-py>=2.0.0"]
sqlite = []  # Built into Python

# Convenience groups
all = [
    "psycopg2-binary>=2.9.0",
    "mysql-connector-python>=8.0.0",
    "mariadb>=1.1.0",
    "pyodbc>=4.0.0",
    "cx_Oracle>=8.3.0",
    "informixdb>=2.2.0",
    "supabase>=2.0.0",
    "postgrest-py>=0.16.0",
    "gotrue-py>=2.0.0"
]

# Common combinations
dev = ["pytest>=7.0.0", "pytest-cov>=4.0.0", "black>=22.0.0", "flake8>=5.0.0"]

[project.urls]
"Homepage" = "https://github.com/mptyl/thoth-dbmanager"
"Bug Tracker" = "https://github.com/mptyl/thoth-dbmanager/issues"

[tool.setuptools.packages.find]
where = ["."]  # look for packages in the current directory
include = ["thoth_dbmanager*"]  # Include the thoth_dbmanager package and its subpackages
exclude = ["tests*", "data*"]  # Exclude tests and data directories
