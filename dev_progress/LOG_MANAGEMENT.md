# Log Management System

## Overview

The Thoth application now includes an automatic log management system that:
- Rotates logs daily at midnight
- Keeps logs for the last 30 days
- Automatically deletes older logs
- Runs cleanup on container startup and every 6 hours

## Features

### Automatic Log Rotation
- Logs are rotated daily using `TimedRotatingFileHandler`
- New log files are created with timestamp suffixes (e.g., `thoth.log.2025-01-07`)
- Current log file remains as `thoth.log`

### Automatic Cleanup
- **On Startup**: Log cleanup runs every time the container starts
- **Scheduled**: Backup cleanup runs every 6 hours via cron
- **Retention**: Keeps logs for 30 days by default
- **Action**: Older logs are permanently deleted (not compressed)

## Manual Commands

### Test Log Cleanup (Dry Run)
```bash
# See what would be deleted without actually deleting
python manage.py cleanup_logs --dry-run
```

### Manual Log Cleanup
```bash
# Clean up logs older than 30 days (default)
python manage.py cleanup_logs

# Clean up logs older than 7 days
python manage.py cleanup_logs --days 7
```

### Check Cron Status (in container)
```bash
# View cron jobs
crontab -l

# Check cron log
tail -f /var/log/cron.log
```

## Configuration

### Change Retention Period
To change the default 30-day retention period, modify the crontab file:
```bash
# Edit scripts/crontab
0 */6 * * * cd /app && python manage.py cleanup_logs --days 14 >> /var/log/cron.log 2>&1
```

### Disable Automatic Cleanup
To disable automatic cleanup, you can:
1. Remove the cron job from `scripts/crontab`
2. Comment out the cleanup call in `scripts/start-with-cron.sh`

## File Locations

- **Log files**: `/app/logs/` (mounted to `./logs/` on host)
- **Cleanup command**: `thoth_core/management/commands/cleanup_logs.py`
- **Cron configuration**: `scripts/crontab`
- **Startup script**: `scripts/start-with-cron.sh`

## Monitoring

### Log File Sizes
```bash
# Check current log files and sizes
ls -lah logs/
```

### Cleanup History
The cleanup operations are logged in the main application log, so you can track when cleanups occur and how many files were removed.

## Troubleshooting

### Cron Not Running
```bash
# Check if cron is running in container
ps aux | grep cron

# Restart cron if needed
service cron restart
```

### Manual Cleanup Not Working
```bash
# Check if logs directory exists
ls -la logs/

# Run with verbose output
python manage.py cleanup_logs --dry-run
```

### Logs Growing Too Large
If you need more aggressive cleanup:
```bash
# Clean up logs older than 7 days
python manage.py cleanup_logs --days 7
```

## Security Notes

- Log files may contain sensitive information
- Ensure proper file permissions on the logs directory
- Consider log rotation frequency based on disk space and compliance requirements
