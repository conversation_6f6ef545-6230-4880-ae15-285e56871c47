FROM python:3.12.5-slim-bullseye
LABEL authors="marco pancotti"

ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

WORKDIR /app

# Install system dependencies including cron
RUN apt-get update && apt-get install -y \
    cron \
    && rm -rf /var/lib/apt/lists/*

# Copiamo prima solo il file requirements.txt
COPY ./requirements.txt /app/
RUN pip install -r requirements.txt

# Ora copiamo tutti gli altri file del progetto
COPY . /app/

# Le seguenti istruzioni verranno eseguite solo dopo che la copia è completata
RUN chmod +x /app/scripts/start.sh
RUN chmod +x /app/scripts/start-with-cron.sh

# Convertiamo i line endings in LF (solo se necessario)
RUN sed -i 's/\r$//' /app/scripts/start.sh
RUN sed -i 's/\r$//' /app/scripts/start-with-cron.sh

# Usiamo CMD invece di ENTRYPOINT per maggiore flessibilità
CMD ["/app/scripts/start-with-cron.sh", "gunicorn", "--config", "gunicorn.conf.py", "Thoth.wsgi:application"]
