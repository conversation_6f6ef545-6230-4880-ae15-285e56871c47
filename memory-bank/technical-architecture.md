# Technical Architecture Details

## Core Components

### 1. Database Manager Factory
- **Location**: `dbmanager/core/factory.py`
- **Purpose**: Centralized creation of database manager instances
- **Pattern**: Factory pattern with registry system

### 2. Core Interfaces
- **Location**: `dbmanager/core/interfaces.py`
- **Key Classes**:
  - `IDatabaseManager`: Main database interface
  - `IConnectionManager`: Connection handling
  - `IQueryBuilder`: Query construction
  - `IResultHandler`: Result processing

### 3. Database Implementations
- **SQLite**: `dbmanager/impl/ThothSqliteManager.py`
- **PostgreSQL**: `dbmanager/impl/ThothPgManager.py`
- **MySQL**: `dbmanager/impl/ThothMySqlManager.py`
- **MariaDB**: `dbmanager/impl/ThothMariaDbManager.py`
- **Oracle**: `dbmanager/impl/ThothOracleManager.py`
- **SQL Server**: `dbmanager/impl/ThothSqlServerManager.py`
- **Informix**: `dbmanager/impl/ThothInformixManager.py`

### 4. LSH Subsystem
- **Core**: `dbmanager/lsh/core.py`
- **Factory**: `dbmanager/lsh/factory.py`
- **Manager**: `dbmanager/lsh/manager.py`
- **Storage**: `dbmanager/lsh/storage.py`

### 5. Plugin System
- **Location**: `dbmanager/plugins/`
- **Purpose**: Extensible functionality for specific databases
- **Current Plugins**: PostgreSQL, SQLite

## Key Design Patterns
1. **Factory Pattern**: For database manager creation
2. **Adapter Pattern**: For database-specific implementations
3. **Plugin Pattern**: For extensible functionality
4. **Strategy Pattern**: For different database behaviors
5. **Repository Pattern**: For data access abstraction

## Configuration
- **Database Types**: Supported through enum
- **Connection Parameters**: Database-specific configurations
- **LSH Parameters**: Configurable similarity thresholds
