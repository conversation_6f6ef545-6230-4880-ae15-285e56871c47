# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.core.exceptions import ValidationError
from vdbmanager.impl.QdrantHaystackStore import QdrantHaystackStore  # Assuming this is the base or compatible type
# Assuming HintDocument, ColumnNameDocument, DocumentationDocument, SqlDocument are accessible here
# Also importing ThothType
from vdbmanager.ThothVectorStore import HintDocument, ColumnNameDocument, SqlDocument, ThothType

from thoth_core.models import VectorDbChoices
from .session_utils import get_current_workspace

import csv
import io
import os
import logging

logger = logging.getLogger(__name__)

def get_csv_export_path(request):
    """
    Determine the CSV export/import path based on IO_DIR environment variable
    and the vector database type and name.
    
    Args:
        request: The HTTP request object containing the session
        
    Returns:
        str: The path for CSV files in format {IO_DIR}/{vect_type}/{name}/
        
    Raises:
        ValueError: If workspace or vector database configuration is invalid
    """
    # Get IO_DIR from environment variable with fallback to 'exports'
    io_dir = os.environ.get('IO_DIR', 'exports')
    
    try:
        # Get current workspace
        workspace = get_current_workspace(request)
        
        if not workspace.sql_db:
            raise ValueError("No SQL database associated with the current workspace")
        
        if not workspace.sql_db.vector_db:
            raise ValueError("No vector database associated with the SQL database")
        
        vector_db = workspace.sql_db.vector_db
        
        # Build hierarchical directory structure: vect_type/name
        vect_type = vector_db.vect_type if vector_db.vect_type else "Unknown"
        vdb_name = vector_db.name if vector_db.name else "default"
        
        # Sanitize the vector type for directory creation
        sanitized_vect_type = "".join(c if c.isalnum() or c in ['_', '-'] else "_" for c in vect_type).strip('_')
        if not sanitized_vect_type:
            sanitized_vect_type = "Unknown"
            logger.warning("Vector database type resulted in empty string after sanitization, using 'Unknown'")
        
        # Sanitize the vector database name for directory creation
        sanitized_vdb_name = "".join(c if c.isalnum() or c in ['_', '-'] else "_" for c in vdb_name).strip('_')
        if not sanitized_vdb_name:
            sanitized_vdb_name = "default"
            logger.warning("Vector database name resulted in empty string after sanitization, using 'default'")
        
        # Create hierarchical path: IO_DIR/vect_type/name
        export_path = os.path.join(io_dir, sanitized_vect_type, sanitized_vdb_name)
        logger.info(f"CSV export path resolved to: {export_path}")
        
        return export_path
        
    except Exception as e:
        logger.error(f"Error resolving CSV export path: {e}")
        # Fallback to old behavior in case of error
        fallback_path = os.path.join(os.environ.get('IO_DIR', 'exports'), 'default')
        logger.warning(f"Using fallback path: {fallback_path}")
        return fallback_path

def get_vector_store(request):
    """
    Get the vector store for the current workspace.
    
    Args:
        request: The HTTP request object containing the session
        
    Returns:
        QdrantHaystackStore: The vector store for the current workspace
        
    Raises:
        ValueError: If no workspace is found in the session or if the vector database type is not supported
    """
    workspace = get_current_workspace(request)
    
    if not workspace.sql_db:
        raise ValueError("No SQL database associated with the current workspace")
    
    if not workspace.sql_db.vector_db:
        raise ValueError("No vector database associated with the SQL database")
    
    vector_db = workspace.sql_db.vector_db
    
    if vector_db.vect_type != VectorDbChoices.QDRANT:
        # This implies that currently only Qdrant is supported by this utility.
        # The get_store_type() method should exist on the returned QdrantHaystackStore instance.
        raise ValueError(f"Vector database type {vector_db.vect_type} is not currently supported by get_vector_store")
    
    collection_name = vector_db.name
    
    # Use the host and port from the vector_db if provided, otherwise use defaults
    host = vector_db.host if vector_db.host else "localhost"
    port = vector_db.port if vector_db.port else 6333
    
    # This returns a QdrantHaystackStore instance.
    # We expect QdrantHaystackStore to have a get_store_type() method.
    return QdrantHaystackStore(collection=collection_name, host=host, port=port)

def export_hints_to_csv_file(vector_store, request=None):
    """
    Exports all hints from the given vector_store to a CSV file.

    Args:
        vector_store: An instance of a vector store (e.g., QdrantHaystackStore).
        request: The HTTP request object (optional, for new path resolution)

    Returns:
        A tuple (filepath, csv_content_string) if successful,
        otherwise raises an exception.
    """
    try:
        hint_documents = vector_store.get_all_hint_documents()

        # Use new path resolution if request is provided
        if request is not None:
            try:
                export_dir = get_csv_export_path(request)
            except Exception as e:
                logger.warning(f"Failed to get new CSV export path: {e}. Falling back to old method.")
                request = None  # Fall back to old method
        
        # Fallback to old method if request not provided or new method failed
        if request is None:
            # Determine vector database type for directory naming
            # Use get_store_type() method as requested
            if hasattr(vector_store, 'get_store_type') and callable(vector_store.get_store_type):
                try:
                    vector_db_type_folder = vector_store.get_store_type()
                    if not vector_db_type_folder or not isinstance(vector_db_type_folder, str):
                        logger.warning(f"vector_store.get_store_type() returned '{vector_db_type_folder}'. Falling back to class name.")
                        vector_db_type_folder = vector_store.__class__.__name__
                    elif not vector_db_type_folder.strip(): # Check for empty or whitespace-only strings
                        logger.warning("vector_store.get_store_type() returned an empty string. Falling back to class name.")
                        vector_db_type_folder = vector_store.__class__.__name__

                except Exception as e_gst:
                    logger.error(f"Error calling vector_store.get_store_type(): {e_gst}. Falling back to class name.")
                    vector_db_type_folder = vector_store.__class__.__name__
            else:
                logger.warning(f"Vector store instance of type {vector_store.__class__.__name__} does not have a get_store_type() method. Falling back to class name.")
                vector_db_type_folder = vector_store.__class__.__name__

            # Sanitize the folder name to ensure it's valid for directory creation
            # Replace non-alphanumeric characters (except underscore) with an underscore
            vector_db_type_folder = "".join(c if c.isalnum() else "_" for c in vector_db_type_folder).strip('_')
            if not vector_db_type_folder: # If sanitization results in an empty string
                vector_db_type_folder = "default_vdb_type"

            export_base_dir = "exports"
            export_dir = os.path.join(export_base_dir, vector_db_type_folder)
        
        # Create directories if they don't exist
        os.makedirs(export_dir, exist_ok=True)

        csv_filename = "hints_export.csv" # No timestamp, overwrite
        filepath = os.path.join(export_dir, csv_filename)

        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(['id', 'hint']) # CSV Headers

        for doc in hint_documents:
            if isinstance(doc, HintDocument): # Ensure it's the correct document type
                writer.writerow([getattr(doc, 'id', ''), getattr(doc, 'hint', '')])
            else:
                logger.warning(f"Skipping document of type {type(doc)} during hint export, expected HintDocument.")

        csv_content_string = output.getvalue()
        output.close()

        # Save the file to the server, overwriting if it exists
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            f.write(csv_content_string)
        
        logger.info(f"Hints CSV successfully saved to {filepath}")
        return filepath, csv_content_string

    except Exception as e:
        logger.error(f"Error during hints CSV export process: {e}", exc_info=True)
        raise # Re-raise the exception to be handled by the calling view

# --- DELETE ALL FUNCTIONS ---

def delete_all_hints_from_vector_store(vector_store):
    """
    Deletes all HintDocuments from the vector store using delete_collection.
    """
    try:
        # Ensure vector_store has delete_collection method.
        # The type hint for vector_store might ideally be ThothHaystackVectorStore if that's where delete_collection is defined.
        if not hasattr(vector_store, 'delete_collection'):
            logger.error("Vector store object does not have a 'delete_collection' method.")
            raise NotImplementedError("Vector store 'delete_collection' method not found.")

        vector_store.delete_collection(thoth_type=ThothType.HINT)
        logger.info("Successfully called delete_collection for hints (ThothType.HINT).")
    except Exception as e:
        logger.error(f"Error deleting all hints using delete_collection: {e}", exc_info=True)
        raise

def delete_all_columns_from_vector_store(vector_store):
    """
    Deletes all ColumnNameDocuments from the vector store using delete_collection.
    """
    try:
        if not hasattr(vector_store, 'delete_collection'):
            logger.error("Vector store object does not have a 'delete_collection' method.")
            raise NotImplementedError("Vector store 'delete_collection' method not found.")

        vector_store.delete_collection(thoth_type=ThothType.COLUMN_NAME) # Assuming ThothType.COLUMN_NAME
        logger.info("Successfully called delete_collection for columns (ThothType.COLUMN_NAME).")
    except Exception as e:
        logger.error(f"Error deleting all columns using delete_collection: {e}", exc_info=True)
        raise

def delete_all_docs_from_vector_store(vector_store):
    """
    Deletes all DocumentationDocuments from the vector store using delete_collection.
    """
    try:
        if not hasattr(vector_store, 'delete_collection'):
            logger.error("Vector store object does not have a 'delete_collection' method.")
            raise NotImplementedError("Vector store 'delete_collection' method not found.")

        vector_store.delete_collection(thoth_type=ThothType.DOCUMENTATION) # Assuming ThothType.DOCUMENTATION
        logger.info("Successfully called delete_collection for docs (ThothType.DOCUMENTATION).")
    except Exception as e:
        logger.error(f"Error deleting all docs using delete_collection: {e}", exc_info=True)
        raise

def delete_all_questions_from_vector_store(vector_store):
    """
    Deletes all SqlDocuments (questions) from the vector store using delete_collection.
    """
    try:
        if not hasattr(vector_store, 'delete_collection'):
            logger.error("Vector store object does not have a 'delete_collection' method.")
            raise NotImplementedError("Vector store 'delete_collection' method not found.")

        vector_store.delete_collection(thoth_type=ThothType.SQL) # Assuming ThothType.SQL
        logger.info("Successfully called delete_collection for questions (ThothType.SQL).")
    except Exception as e:
        logger.error(f"Error deleting all questions using delete_collection: {e}", exc_info=True)
        raise

# --- IMPORT FUNCTIONS ---

def _get_csv_file_path(vector_store, filename_suffix, request=None):
    """
    Helper function to determine the CSV file path for import.
    
    Args:
        vector_store: The vector store instance
        filename_suffix: The filename suffix (e.g., 'hints', 'columns', 'questions')
        request: The HTTP request object (optional, for new path resolution)
    
    Returns:
        str: The full path to the CSV file
    """
    # Use new path resolution if request is provided
    if request is not None:
        try:
            export_dir = get_csv_export_path(request)
        except Exception as e:
            logger.warning(f"Failed to get new CSV export path: {e}. Falling back to old method.")
            request = None  # Fall back to old method
    
    # Fallback to old method if request not provided or new method failed
    if request is None:
        if hasattr(vector_store, 'get_store_type') and callable(vector_store.get_store_type):
            try:
                vector_db_type_folder = vector_store.get_store_type()
                if not vector_db_type_folder or not isinstance(vector_db_type_folder, str) or not vector_db_type_folder.strip():
                    logger.warning(f"vector_store.get_store_type() returned invalid value '{vector_db_type_folder}'. Falling back to class name.")
                    vector_db_type_folder = vector_store.__class__.__name__
            except Exception as e_gst:
                logger.error(f"Error calling vector_store.get_store_type(): {e_gst}. Falling back to class name.")
                vector_db_type_folder = vector_store.__class__.__name__
        else:
            logger.warning(f"Vector store instance of type {vector_store.__class__.__name__} does not have a get_store_type() method. Falling back to class name.")
            vector_db_type_folder = vector_store.__class__.__name__

        vector_db_type_folder = "".join(c if c.isalnum() else "_" for c in vector_db_type_folder).strip('_')
        if not vector_db_type_folder:
            vector_db_type_folder = "default_vdb_type"

        export_base_dir = "exports"
        export_dir = os.path.join(export_base_dir, vector_db_type_folder)
    
    csv_filename = f"{filename_suffix}_export.csv" # Assumes export files are named like 'hints_export.csv'
    filepath = os.path.join(export_dir, csv_filename)
    return filepath

def import_hints_from_csv_file(vector_store, request=None):
    """
    Imports hints from a CSV file into the vector store.
    Assumes CSV has 'id' and 'hint' columns.
    Uses the ID from CSV for potential overwrites based on vector_store's DuplicatePolicy.
    
    Args:
        vector_store: The vector store instance
        request: The HTTP request object (optional, for new path resolution)
    """
    filepath = _get_csv_file_path(vector_store, "hints", request)
    imported_count = 0
    error_count = 0
    messages = []

    if not os.path.exists(filepath):
        messages.append(f"Error: File not found at {filepath}")
        logger.error(f"Hint import CSV file not found: {filepath}")
        return {"imported_count": 0, "error_count": 1, "messages": messages}

    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            if 'id' not in reader.fieldnames or 'hint' not in reader.fieldnames:
                msg = "Error: CSV file for hints must contain 'id' and 'hint' columns."
                messages.append(msg)
                logger.error(msg + f" File: {filepath}")
                return {"imported_count": 0, "error_count": 1, "messages": messages}
                
            for row in reader:
                try:
                    hint_id = row.get('id')
                    hint_text = row.get('hint')
                    if not hint_id or hint_text is None: # hint_text can be empty string
                        logger.warning(f"Skipping hint row due to missing id or hint key: {row}")
                        error_count += 1
                        messages.append(f"Skipped hint row: missing id or hint data - ID: {hint_id if hint_id else 'N/A'}")
                        continue
                    
                    hint_doc = HintDocument(id=hint_id, hint=hint_text)
                    vector_store.add_hint(hint_doc) 
                    imported_count += 1
                except Exception as e_row:
                    logger.error(f"Error importing hint row (ID: {hint_id}): {e_row}")
                    error_count += 1
                    messages.append(f"Error for hint ID {hint_id}: {e_row}")
        if error_count > 0:
            messages.insert(0, f"Import process completed with {error_count} errors.")
        messages.insert(0, f"Successfully imported {imported_count} hints.")
        logger.info(f"Hint import complete. Imported: {imported_count}, Errors: {error_count} from {filepath}")
    except Exception as e_file:
        logger.error(f"Error processing CSV file {filepath} for hints: {e_file}")
        error_count += 1 # This might double count if an error also happened per row
        messages.append(f"Failed to process CSV file {filepath}: {e_file}")
        
    return {"imported_count": imported_count, "error_count": error_count, "messages": messages}

def import_columns_from_csv_file(vector_store, request=None):
    """
    Imports column documents from a CSV file into the vector store.
    Uses IDs from CSV for potential overwrites.
    
    Args:
        vector_store: The vector store instance
        request: The HTTP request object (optional, for new path resolution)
    """
    filepath = _get_csv_file_path(vector_store, "columns", request)
    imported_count = 0
    error_count = 0
    messages = []

    if not os.path.exists(filepath):
        messages.append(f"Error: File not found at {filepath}")
        logger.error(f"Columns import CSV file not found: {filepath}")
        return {"imported_count": 0, "error_count": 1, "messages": messages}

    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            expected_headers = ['id', 'table_name', 'original_column_name', 'column_description', 'value_description']
            if not all(header in reader.fieldnames for header in expected_headers):
                msg = f"Error: CSV file for columns must contain all expected columns: {', '.join(expected_headers)}."
                messages.append(msg)
                logger.error(msg + f" File: {filepath}")
                return {"imported_count": 0, "error_count": 1, "messages": messages}

            for row in reader:
                try:
                    col_id = row.get('id')
                    if not col_id:
                        logger.warning(f"Skipping column row due to missing id: {row}")
                        error_count += 1
                        messages.append(f"Skipped column row: missing id - Name: {row.get('original_column_name', 'N/A')}")
                        continue
                    
                    table_name_val = row.get('table_name')
                    original_column_name_val = row.get('original_column_name')
                    column_description_val = row.get('column_description')
                    value_description_val = row.get('value_description')

                    # Ensure required string fields get strings, not None.
                    # This addresses potential string_type errors and the "missing" error if it was due to None for a required string.
                    csv_original_col_name = original_column_name_val if original_column_name_val is not None else ""
                    csv_value_desc = value_description_val if value_description_val is not None else ""

                    column_doc = ColumnNameDocument(
                        id=col_id,
                        table_name=table_name_val if table_name_val else '',
                        column_name=csv_original_col_name, # Fed from CSV's original_column_name
                        original_column_name=csv_original_col_name, # Fed from CSV's original_column_name
                        column_description=column_description_val if column_description_val else '',
                        value_description=csv_value_desc
                    )
                    vector_store.add_column_description(column_doc)
                    imported_count += 1
                except Exception as e_row:
                    logger.error(f"Error importing column row (ID: {col_id}): {e_row}")
                    error_count += 1
                    messages.append(f"Error for column ID {col_id}: {e_row}")
        if error_count > 0:
            messages.insert(0, f"Import process completed with {error_count} errors.")
        messages.insert(0, f"Successfully imported {imported_count} columns.")
        logger.info(f"Column import complete. Imported: {imported_count}, Errors: {error_count} from {filepath}")
    except Exception as e_file:
        logger.error(f"Error processing CSV file {filepath} for columns: {e_file}")
        messages.append(f"Failed to process CSV file {filepath}: {e_file}")

    return {"imported_count": imported_count, "error_count": error_count, "messages": messages}

def import_docs_from_csv_file(vector_store):
    """
    Imports documentation documents from a CSV file into the vector store.
    Uses IDs from CSV for potential overwrites.
    """
    filepath = _get_csv_file_path(vector_store, "docs")
    imported_count = 0
    error_count = 0
    messages = []

    if not os.path.exists(filepath):
        messages.append(f"Error: File not found at {filepath}")
        logger.error(f"Docs import CSV file not found: {filepath}")
        return {"imported_count": 0, "error_count": 1, "messages": messages}

    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            if 'id' not in reader.fieldnames or 'content' not in reader.fieldnames:
                msg = "Error: CSV file for documents must contain 'id' and 'content' columns."
                messages.append(msg)
                logger.error(msg + f" File: {filepath}")
                return {"imported_count": 0, "error_count": 1, "messages": messages}

            for row in reader:
                try:
                    doc_id = row.get('id')
                    doc_content = row.get('content') # Content can be an empty string
                    if not doc_id or doc_content is None:
                        logger.warning(f"Skipping doc row due to missing id or content key: {row}")
                        error_count += 1
                        messages.append(f"Skipped doc row: missing id or content data - ID: {doc_id if doc_id else 'N/A'}")
                        continue
                        
                    doc_doc = DocumentationDocument(id=doc_id, content=doc_content)
                    vector_store.add_documentation(doc_doc) 
                    imported_count += 1
                except Exception as e_row:
                    logger.error(f"Error importing doc row (ID: {doc_id}): {e_row}")
                    error_count += 1
                    messages.append(f"Error for doc ID {doc_id}: {e_row}")
        if error_count > 0:
            messages.insert(0, f"Import process completed with {error_count} errors.")
        messages.insert(0, f"Successfully imported {imported_count} documents.")
        logger.info(f"Docs import complete. Imported: {imported_count}, Errors: {error_count} from {filepath}")
    except Exception as e_file:
        logger.error(f"Error processing CSV file {filepath} for docs: {e_file}")
        messages.append(f"Failed to process CSV file {filepath}: {e_file}")

    return {"imported_count": imported_count, "error_count": error_count, "messages": messages}

def import_questions_from_csv_file(vector_store, request=None):
    """
    Imports SQL documents (questions) from a CSV file into the vector store.
    Uses IDs from CSV for potential overwrites.
    
    Args:
        vector_store: The vector store instance
        request: The HTTP request object (optional, for new path resolution)
    """
    filepath = _get_csv_file_path(vector_store, "questions", request)
    imported_count = 0
    error_count = 0
    messages = []

    if not os.path.exists(filepath):
        messages.append(f"Error: File not found at {filepath}")
        logger.error(f"Questions import CSV file not found: {filepath}")
        return {"imported_count": 0, "error_count": 1, "messages": messages}

    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            expected_headers = ['id', 'question', 'sql']
            if not all(header in reader.fieldnames for header in expected_headers):
                msg = f"Error: CSV file for questions must contain all expected columns: {', '.join(expected_headers)}."
                messages.append(msg)
                logger.error(msg + f" File: {filepath}")
                return {"imported_count": 0, "error_count": 1, "messages": messages}

            for row in reader:
                try:
                    q_id = row.get('id')
                    q_text = row.get('question')
                    q_sql = row.get('sql')
                    if not q_id or q_text is None or q_sql is None: # question and sql can be empty strings
                        logger.warning(f"Skipping question row due to missing id, question key, or sql key: {row}")
                        error_count += 1
                        messages.append(f"Skipped question row: missing id, question or sql data - ID: {q_id if q_id else 'N/A'}")
                        continue
                        
                    question_doc = SqlDocument(id=q_id, question=q_text, sql=q_sql)
                    vector_store.add_sql(question_doc) 
                    imported_count += 1
                except Exception as e_row:
                    logger.error(f"Error importing question row (ID: {q_id}): {e_row}")
                    error_count += 1
                    messages.append(f"Error for question ID {q_id}: {e_row}")
        if error_count > 0:
            messages.insert(0, f"Import process completed with {error_count} errors.")
        messages.insert(0, f"Successfully imported {imported_count} questions.")
        logger.info(f"Questions import complete. Imported: {imported_count}, Errors: {error_count} from {filepath}")
    except Exception as e_file:
        logger.error(f"Error processing CSV file {filepath} for questions: {e_file}")
        messages.append(f"Failed to process CSV file {filepath}: {e_file}")

    return {"imported_count": imported_count, "error_count": error_count, "messages": messages}

def export_columns_to_csv_file(vector_store, request=None):
    """
    Exports all column documents from the given vector_store to a CSV file.

    Args:
        vector_store: An instance of a vector store (e.g., QdrantHaystackStore).
        request: The HTTP request object (optional, for new path resolution)

    Returns:
        A tuple (filepath, csv_content_string) if successful,
        otherwise raises an exception.
    """
    try:
        column_documents = vector_store.get_all_column_documents()

        # Use new path resolution if request is provided
        if request is not None:
            try:
                export_dir = get_csv_export_path(request)
            except Exception as e:
                logger.warning(f"Failed to get new CSV export path: {e}. Falling back to old method.")
                request = None  # Fall back to old method
        
        # Fallback to old method if request not provided or new method failed
        if request is None:
            if hasattr(vector_store, 'get_store_type') and callable(vector_store.get_store_type):
                try:
                    vector_db_type_folder = vector_store.get_store_type()
                    if not vector_db_type_folder or not isinstance(vector_db_type_folder, str):
                        logger.warning(f"vector_store.get_store_type() returned '{vector_db_type_folder}'. Falling back to class name.")
                        vector_db_type_folder = vector_store.__class__.__name__
                    elif not vector_db_type_folder.strip():
                        logger.warning("vector_store.get_store_type() returned an empty string. Falling back to class name.")
                        vector_db_type_folder = vector_store.__class__.__name__
                except Exception as e_gst:
                    logger.error(f"Error calling vector_store.get_store_type(): {e_gst}. Falling back to class name.")
                    vector_db_type_folder = vector_store.__class__.__name__
            else:
                logger.warning(f"Vector store instance of type {vector_store.__class__.__name__} does not have a get_store_type() method. Falling back to class name.")
                vector_db_type_folder = vector_store.__class__.__name__

            vector_db_type_folder = "".join(c if c.isalnum() else "_" for c in vector_db_type_folder).strip('_')
            if not vector_db_type_folder:
                vector_db_type_folder = "default_vdb_type"

            export_base_dir = "exports"
            export_dir = os.path.join(export_base_dir, vector_db_type_folder)
        
        os.makedirs(export_dir, exist_ok=True)

        csv_filename = "columns_export.csv"
        filepath = os.path.join(export_dir, csv_filename)

        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(['id', 'table_name', 'original_column_name', 'column_description', 'value_description']) # CSV Headers

        for doc in column_documents:
            if isinstance(doc, ColumnNameDocument):
                writer.writerow([
                    getattr(doc, 'id', ''),
                    getattr(doc, 'table_name', ''),
                    getattr(doc, 'column_name', ''),
                    getattr(doc, 'column_description', ''),
                    getattr(doc, 'value_description', '')
                ])
            else:
                logger.warning(f"Skipping document of type {type(doc)} during column export, expected ColumnNameDocument.")

        csv_content_string = output.getvalue()
        output.close()

        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            f.write(csv_content_string)
        
        logger.info(f"Columns CSV successfully saved to {filepath}")
        return filepath, csv_content_string

    except Exception as e:
        logger.error(f"Error during columns CSV export process: {e}", exc_info=True)
        raise

def export_docs_to_csv_file(vector_store):
    """
    Exports all documentation documents from the given vector_store to a CSV file.

    Args:
        vector_store: An instance of a vector store (e.g., QdrantHaystackStore).

    Returns:
        A tuple (filepath, csv_content_string) if successful,
        otherwise raises an exception.
    """
    try:
        documentation_documents = vector_store.get_all_documentation_documents()

        if hasattr(vector_store, 'get_store_type') and callable(vector_store.get_store_type):
            try:
                vector_db_type_folder = vector_store.get_store_type()
                if not vector_db_type_folder or not isinstance(vector_db_type_folder, str):
                    logger.warning(f"vector_store.get_store_type() returned '{vector_db_type_folder}'. Falling back to class name.")
                    vector_db_type_folder = vector_store.__class__.__name__
                elif not vector_db_type_folder.strip():
                    logger.warning("vector_store.get_store_type() returned an empty string. Falling back to class name.")
                    vector_db_type_folder = vector_store.__class__.__name__
            except Exception as e_gst:
                logger.error(f"Error calling vector_store.get_store_type(): {e_gst}. Falling back to class name.")
                vector_db_type_folder = vector_store.__class__.__name__
        else:
            logger.warning(f"Vector store instance of type {vector_store.__class__.__name__} does not have a get_store_type() method. Falling back to class name.")
            vector_db_type_folder = vector_store.__class__.__name__

        vector_db_type_folder = "".join(c if c.isalnum() else "_" for c in vector_db_type_folder).strip('_')
        if not vector_db_type_folder:
            vector_db_type_folder = "default_vdb_type"

        export_base_dir = "exports"
        export_dir = os.path.join(export_base_dir, vector_db_type_folder)
        os.makedirs(export_dir, exist_ok=True)

        csv_filename = "docs_export.csv"
        filepath = os.path.join(export_dir, csv_filename)

        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(['id', 'content']) # CSV Headers

        for doc in documentation_documents:
            if isinstance(doc, DocumentationDocument):
                writer.writerow([
                    getattr(doc, 'id', ''),
                    getattr(doc, 'content', '')
                ])
            else:
                logger.warning(f"Skipping document of type {type(doc)} during docs export, expected DocumentationDocument.")

        csv_content_string = output.getvalue()
        output.close()

        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            f.write(csv_content_string)
        
        logger.info(f"Docs CSV successfully saved to {filepath}")
        return filepath, csv_content_string

    except Exception as e:
        logger.error(f"Error during docs CSV export process: {e}", exc_info=True)
        raise

def export_questions_to_csv_file(vector_store, request=None):
    """
    Exports all SQL documents (questions) from the given vector_store to a CSV file.

    Args:
        vector_store: An instance of a vector store (e.g., QdrantHaystackStore).
        request: The HTTP request object (optional, for new path resolution)

    Returns:
        A tuple (filepath, csv_content_string) if successful,
        otherwise raises an exception.
    """
    try:
        sql_documents = vector_store.get_all_sql_documents()

        # Use new path resolution if request is provided
        if request is not None:
            try:
                export_dir = get_csv_export_path(request)
            except Exception as e:
                logger.warning(f"Failed to get new CSV export path: {e}. Falling back to old method.")
                request = None  # Fall back to old method
        
        # Fallback to old method if request not provided or new method failed
        if request is None:
            if hasattr(vector_store, 'get_store_type') and callable(vector_store.get_store_type):
                try:
                    vector_db_type_folder = vector_store.get_store_type()
                    if not vector_db_type_folder or not isinstance(vector_db_type_folder, str):
                        logger.warning(f"vector_store.get_store_type() returned '{vector_db_type_folder}'. Falling back to class name.")
                        vector_db_type_folder = vector_store.__class__.__name__
                    elif not vector_db_type_folder.strip():
                        logger.warning("vector_store.get_store_type() returned an empty string. Falling back to class name.")
                        vector_db_type_folder = vector_store.__class__.__name__
                except Exception as e_gst:
                    logger.error(f"Error calling vector_store.get_store_type(): {e_gst}. Falling back to class name.")
                    vector_db_type_folder = vector_store.__class__.__name__
            else:
                logger.warning(f"Vector store instance of type {vector_store.__class__.__name__} does not have a get_store_type() method. Falling back to class name.")
                vector_db_type_folder = vector_store.__class__.__name__

            vector_db_type_folder = "".join(c if c.isalnum() else "_" for c in vector_db_type_folder).strip('_')
            if not vector_db_type_folder:
                vector_db_type_folder = "default_vdb_type"

            export_base_dir = "exports"
            export_dir = os.path.join(export_base_dir, vector_db_type_folder)
        
        os.makedirs(export_dir, exist_ok=True)

        csv_filename = "questions_export.csv"
        filepath = os.path.join(export_dir, csv_filename)

        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(['id', 'question', 'sql']) # CSV Headers

        for doc in sql_documents:
            if isinstance(doc, SqlDocument):
                writer.writerow([
                    getattr(doc, 'id', ''),
                    getattr(doc, 'question', ''),
                    getattr(doc, 'sql', '')
                ])
            else:
                logger.warning(f"Skipping document of type {type(doc)} during questions export, expected SqlDocument.")

        csv_content_string = output.getvalue()
        output.close()

        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            f.write(csv_content_string)
        
        logger.info(f"Questions CSV successfully saved to {filepath}")
        return filepath, csv_content_string

    except Exception as e:
        logger.error(f"Error during questions CSV export process: {e}", exc_info=True)
        raise
