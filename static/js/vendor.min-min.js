!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";var i=[],n=Object.getPrototypeOf,s=i.slice,o=i.flat?function(t){return i.flat.call(t)}:function(t){return i.concat.apply([],t)},r=i.push,a=i.indexOf,l={},c=l.toString,h=l.hasOwnProperty,u=h.toString,d=u.call(Object),p={},f=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},m=function(t){return null!=t&&t===t.window},g=t.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function y(t,e,i){var n,s,o=(i=i||g).createElement("script");if(o.text=t,e)for(n in v)(s=e[n]||e.getAttribute&&e.getAttribute(n))&&o.setAttribute(n,s);i.head.appendChild(o).parentNode.removeChild(o)}function b(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?l[c.call(t)]||"object":typeof t}var _=/HTML$/i,w=function(t,e){return new w.fn.init(t,e)};function x(t){var e=!!t&&"length"in t&&t.length,i=b(t);return!f(t)&&!m(t)&&("array"===i||0===e||"number"==typeof e&&e>0&&e-1 in t)}function k(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}w.fn=w.prototype={jquery:"3.7.1",constructor:w,length:0,toArray:function(){return s.call(this)},get:function(t){return null==t?s.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=w.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return w.each(this,t)},map:function(t){return this.pushStack(w.map(this,function(e,i){return t.call(e,i,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(w.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(w.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(i>=0&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:r,sort:i.sort,splice:i.splice},w.extend=w.fn.extend=function(){var t,e,i,n,s,o,r=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[a]||{},a++),"object"==typeof r||f(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)n=t[e],"__proto__"!==e&&r!==n&&(c&&n&&(w.isPlainObject(n)||(s=Array.isArray(n)))?(i=r[e],o=s&&!Array.isArray(i)?[]:s||w.isPlainObject(i)?i:{},s=!1,r[e]=w.extend(c,o,n)):void 0!==n&&(r[e]=n));return r},w.extend({expando:"jQuery"+("3.7.1"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,i;return!(!t||"[object Object]"!==c.call(t))&&(!(e=n(t))||"function"==typeof(i=h.call(e,"constructor")&&e.constructor)&&u.call(i)===d)},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,i){y(t,{nonce:e&&e.nonce},i)},each:function(t,e){var i,n=0;if(x(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},text:function(t){var e,i="",n=0,s=t.nodeType;if(!s)for(;e=t[n++];)i+=w.text(e);return 1===s||11===s?t.textContent:9===s?t.documentElement.textContent:3===s||4===s?t.nodeValue:i},makeArray:function(t,e){var i=e||[];return null!=t&&(x(Object(t))?w.merge(i,"string"==typeof t?[t]:t):r.call(i,t)),i},inArray:function(t,e,i){return null==e?-1:a.call(e,t,i)},isXMLDoc:function(t){var e=t&&t.namespaceURI,i=t&&(t.ownerDocument||t).documentElement;return!_.test(e||i&&i.nodeName||"HTML")},merge:function(t,e){for(var i=+e.length,n=0,s=t.length;n<i;n++)t[s++]=e[n];return t.length=s,t},grep:function(t,e,i){for(var n=[],s=0,o=t.length,r=!i;s<o;s++)!e(t[s],s)!==r&&n.push(t[s]);return n},map:function(t,e,i){var n,s,r=0,a=[];if(x(t))for(n=t.length;r<n;r++)null!=(s=e(t[r],r,i))&&a.push(s);else for(r in t)null!=(s=e(t[r],r,i))&&a.push(s);return o(a)},guid:1,support:p}),"function"==typeof Symbol&&(w.fn[Symbol.iterator]=i[Symbol.iterator]),w.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){l["[object "+e+"]"]=e.toLowerCase()});var D=i.pop,C=i.sort,S=i.splice,T="[\\x20\\t\\r\\n\\f]",M=new RegExp("^"+T+"+|((?:^|[^\\\\])(?:\\\\.)*)"+T+"+$","g");w.contains=function(t,e){var i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(t.contains?t.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))};var E=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function A(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}w.escapeSelector=function(t){return(t+"").replace(E,A)};var O=g,N=r;!function(){var e,n,o,r,l,c,u,d,f,m,g=N,v=w.expando,y=0,b=0,_=tt(),x=tt(),E=tt(),A=tt(),L=function(t,e){return t===e&&(l=!0),0},P="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",j="(?:\\\\[\\da-fA-F]{1,6}"+T+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",$="\\["+T+"*("+j+")(?:"+T+"*([*^$|!~]?=)"+T+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+j+"))|)"+T+"*\\]",Y=":("+j+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+$+")*)|.*)\\)|)",I=new RegExp(T+"+","g"),W=new RegExp("^"+T+"*,"+T+"*"),H=new RegExp("^"+T+"*([>+~]|"+T+")"+T+"*"),R=new RegExp(T+"|>"),F=new RegExp(Y),U=new RegExp("^"+j+"$"),q={ID:new RegExp("^#("+j+")"),CLASS:new RegExp("^\\.("+j+")"),TAG:new RegExp("^("+j+"|[*])"),ATTR:new RegExp("^"+$),PSEUDO:new RegExp("^"+Y),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+T+"*(even|odd|(([+-]|)(\\d*)n|)"+T+"*(?:([+-]|)"+T+"*(\\d+)|))"+T+"*\\)|)","i"),bool:new RegExp("^(?:"+P+")$","i"),needsContext:new RegExp("^"+T+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+T+"*((?:-\\d)?\\d*)"+T+"*\\)|)(?=[^-]|$)","i")},z=/^(?:input|select|textarea|button)$/i,V=/^h\d$/i,B=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,G=/[+~]/,X=new RegExp("\\\\[\\da-fA-F]{1,6}"+T+"?|\\\\([^\\r\\n\\f])","g"),K=function(t,e){var i="0x"+t.slice(1)-65536;return e||(i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320))},Z=function(){lt()},Q=dt(function(t){return!0===t.disabled&&k(t,"fieldset")},{dir:"parentNode",next:"legend"});try{g.apply(i=s.call(O.childNodes),O.childNodes),i[O.childNodes.length].nodeType}catch(t){g={apply:function(t,e){N.apply(t,s.call(e))},call:function(t){N.apply(t,s.call(arguments,1))}}}function J(t,e,i,n){var s,o,r,a,l,h,u,m=e&&e.ownerDocument,y=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==y&&9!==y&&11!==y)return i;if(!n&&(lt(e),e=e||c,d)){if(11!==y&&(l=B.exec(t)))if(s=l[1]){if(9===y){if(!(r=e.getElementById(s)))return i;if(r.id===s)return g.call(i,r),i}else if(m&&(r=m.getElementById(s))&&J.contains(e,r)&&r.id===s)return g.call(i,r),i}else{if(l[2])return g.apply(i,e.getElementsByTagName(t)),i;if((s=l[3])&&e.getElementsByClassName)return g.apply(i,e.getElementsByClassName(s)),i}if(!(A[t+" "]||f&&f.test(t))){if(u=t,m=e,1===y&&(R.test(t)||H.test(t))){for((m=G.test(t)&&at(e.parentNode)||e)==e&&p.scope||((a=e.getAttribute("id"))?a=w.escapeSelector(a):e.setAttribute("id",a=v)),o=(h=ht(t)).length;o--;)h[o]=(a?"#"+a:":scope")+" "+ut(h[o]);u=h.join(",")}try{return g.apply(i,m.querySelectorAll(u)),i}catch(e){A(t,!0)}finally{a===v&&e.removeAttribute("id")}}}return yt(t.replace(M,"$1"),e,i,n)}function tt(){var t=[];return function e(i,s){return t.push(i+" ")>n.cacheLength&&delete e[t.shift()],e[i+" "]=s}}function et(t){return t[v]=!0,t}function it(t){var e=c.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function nt(t){return function(e){return k(e,"input")&&e.type===t}}function st(t){return function(e){return(k(e,"input")||k(e,"button"))&&e.type===t}}function ot(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&Q(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function rt(t){return et(function(e){return e=+e,et(function(i,n){for(var s,o=t([],i.length,e),r=o.length;r--;)i[s=o[r]]&&(i[s]=!(n[s]=i[s]))})})}function at(t){return t&&void 0!==t.getElementsByTagName&&t}function lt(t){var e,i=t?t.ownerDocument||t:O;return i!=c&&9===i.nodeType&&i.documentElement?(u=(c=i).documentElement,d=!w.isXMLDoc(c),m=u.matches||u.webkitMatchesSelector||u.msMatchesSelector,u.msMatchesSelector&&O!=c&&(e=c.defaultView)&&e.top!==e&&e.addEventListener("unload",Z),p.getById=it(function(t){return u.appendChild(t).id=w.expando,!c.getElementsByName||!c.getElementsByName(w.expando).length}),p.disconnectedMatch=it(function(t){return m.call(t,"*")}),p.scope=it(function(){return c.querySelectorAll(":scope")}),p.cssHas=it(function(){try{return c.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}}),p.getById?(n.filter.ID=function(t){var e=t.replace(X,K);return function(t){return t.getAttribute("id")===e}},n.find.ID=function(t,e){if(void 0!==e.getElementById&&d){var i=e.getElementById(t);return i?[i]:[]}}):(n.filter.ID=function(t){var e=t.replace(X,K);return function(t){var i=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return i&&i.value===e}},n.find.ID=function(t,e){if(void 0!==e.getElementById&&d){var i,n,s,o=e.getElementById(t);if(o){if((i=o.getAttributeNode("id"))&&i.value===t)return[o];for(s=e.getElementsByName(t),n=0;o=s[n++];)if((i=o.getAttributeNode("id"))&&i.value===t)return[o]}return[]}}),n.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},n.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&d)return e.getElementsByClassName(t)},f=[],it(function(t){var e;u.appendChild(t).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||f.push("\\["+T+"*(?:value|"+P+")"),t.querySelectorAll("[id~="+v+"-]").length||f.push("~="),t.querySelectorAll("a#"+v+"+*").length||f.push(".#.+[+~]"),t.querySelectorAll(":checked").length||f.push(":checked"),(e=c.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),u.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&f.push(":enabled",":disabled"),(e=c.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||f.push("\\["+T+"*name"+T+"*="+T+"*(?:''|\"\")")}),p.cssHas||f.push(":has"),f=f.length&&new RegExp(f.join("|")),L=function(t,e){if(t===e)return l=!0,0;var i=!t.compareDocumentPosition-!e.compareDocumentPosition;return i||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!p.sortDetached&&e.compareDocumentPosition(t)===i?t===c||t.ownerDocument==O&&J.contains(O,t)?-1:e===c||e.ownerDocument==O&&J.contains(O,e)?1:r?a.call(r,t)-a.call(r,e):0:4&i?-1:1)},c):c}for(e in J.matches=function(t,e){return J(t,null,null,e)},J.matchesSelector=function(t,e){if(lt(t),d&&!A[e+" "]&&(!f||!f.test(e)))try{var i=m.call(t,e);if(i||p.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){A(e,!0)}return J(e,c,null,[t]).length>0},J.contains=function(t,e){return(t.ownerDocument||t)!=c&&lt(t),w.contains(t,e)},J.attr=function(t,e){(t.ownerDocument||t)!=c&&lt(t);var i=n.attrHandle[e.toLowerCase()],s=i&&h.call(n.attrHandle,e.toLowerCase())?i(t,e,!d):void 0;return void 0!==s?s:t.getAttribute(e)},J.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},w.uniqueSort=function(t){var e,i=[],n=0,o=0;if(l=!p.sortStable,r=!p.sortStable&&s.call(t,0),C.call(t,L),l){for(;e=t[o++];)e===t[o]&&(n=i.push(o));for(;n--;)S.call(t,i[n],1)}return r=null,t},w.fn.uniqueSort=function(){return this.pushStack(w.uniqueSort(s.apply(this)))},(n=w.expr={cacheLength:50,createPseudo:et,match:q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(X,K),t[3]=(t[3]||t[4]||t[5]||"").replace(X,K),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||J.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&J.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return q.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&F.test(i)&&(e=ht(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(X,K).toLowerCase();return"*"===t?function(){return!0}:function(t){return k(t,e)}},CLASS:function(t){var e=_[t+" "];return e||(e=new RegExp("(^|"+T+")"+t+"("+T+"|$)"))&&_(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,e,i){return function(n){var s=J.attr(n,t);return null==s?"!="===e:!e||(s+="","="===e?s===i:"!="===e?s!==i:"^="===e?i&&0===s.indexOf(i):"*="===e?i&&s.indexOf(i)>-1:"$="===e?i&&s.slice(-i.length)===i:"~="===e?(" "+s.replace(I," ")+" ").indexOf(i)>-1:"|="===e&&(s===i||s.slice(0,i.length+1)===i+"-"))}},CHILD:function(t,e,i,n,s){var o="nth"!==t.slice(0,3),r="last"!==t.slice(-4),a="of-type"===e;return 1===n&&0===s?function(t){return!!t.parentNode}:function(e,i,l){var c,h,u,d,p,f=o!==r?"nextSibling":"previousSibling",m=e.parentNode,g=a&&e.nodeName.toLowerCase(),b=!l&&!a,_=!1;if(m){if(o){for(;f;){for(u=e;u=u[f];)if(a?k(u,g):1===u.nodeType)return!1;p=f="only"===t&&!p&&"nextSibling"}return!0}if(p=[r?m.firstChild:m.lastChild],r&&b){for(_=(d=(c=(h=m[v]||(m[v]={}))[t]||[])[0]===y&&c[1])&&c[2],u=d&&m.childNodes[d];u=++d&&u&&u[f]||(_=d=0)||p.pop();)if(1===u.nodeType&&++_&&u===e){h[t]=[y,d,_];break}}else if(b&&(_=d=(c=(h=e[v]||(e[v]={}))[t]||[])[0]===y&&c[1]),!1===_)for(;(u=++d&&u&&u[f]||(_=d=0)||p.pop())&&((a?!k(u,g):1!==u.nodeType)||!++_||(b&&((h=u[v]||(u[v]={}))[t]=[y,_]),u!==e)););return(_-=s)===n||_%n==0&&_/n>=0}}},PSEUDO:function(t,e){var i,s=n.pseudos[t]||n.setFilters[t.toLowerCase()]||J.error("unsupported pseudo: "+t);return s[v]?s(e):s.length>1?(i=[t,t,"",e],n.setFilters.hasOwnProperty(t.toLowerCase())?et(function(t,i){for(var n,o=s(t,e),r=o.length;r--;)t[n=a.call(t,o[r])]=!(i[n]=o[r])}):function(t){return s(t,0,i)}):s}},pseudos:{not:et(function(t){var e=[],i=[],n=vt(t.replace(M,"$1"));return n[v]?et(function(t,e,i,s){for(var o,r=n(t,null,s,[]),a=t.length;a--;)(o=r[a])&&(t[a]=!(e[a]=o))}):function(t,s,o){return e[0]=t,n(e,null,o,i),e[0]=null,!i.pop()}}),has:et(function(t){return function(e){return J(t,e).length>0}}),contains:et(function(t){return t=t.replace(X,K),function(e){return(e.textContent||w.text(e)).indexOf(t)>-1}}),lang:et(function(t){return U.test(t||"")||J.error("unsupported lang: "+t),t=t.replace(X,K).toLowerCase(),function(e){var i;do{if(i=d?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(i=i.toLowerCase())===t||0===i.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var i=t.location&&t.location.hash;return i&&i.slice(1)===e.id},root:function(t){return t===u},focus:function(t){return t===function(){try{return c.activeElement}catch(t){}}()&&c.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:ot(!1),disabled:ot(!0),checked:function(t){return k(t,"input")&&!!t.checked||k(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!n.pseudos.empty(t)},header:function(t){return V.test(t.nodeName)},input:function(t){return z.test(t.nodeName)},button:function(t){return k(t,"input")&&"button"===t.type||k(t,"button")},text:function(t){var e;return k(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:rt(function(){return[0]}),last:rt(function(t,e){return[e-1]}),eq:rt(function(t,e,i){return[i<0?i+e:i]}),even:rt(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:rt(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:rt(function(t,e,i){var n;for(n=i<0?i+e:i>e?e:i;--n>=0;)t.push(n);return t}),gt:rt(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=n.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})n.pseudos[e]=nt(e);for(e in{submit:!0,reset:!0})n.pseudos[e]=st(e);function ct(){}function ht(t,e){var i,s,o,r,a,l,c,h=x[t+" "];if(h)return e?0:h.slice(0);for(a=t,l=[],c=n.preFilter;a;){for(r in i&&!(s=W.exec(a))||(s&&(a=a.slice(s[0].length)||a),l.push(o=[])),i=!1,(s=H.exec(a))&&(i=s.shift(),o.push({value:i,type:s[0].replace(M," ")}),a=a.slice(i.length)),n.filter)!(s=q[r].exec(a))||c[r]&&!(s=c[r](s))||(i=s.shift(),o.push({value:i,type:r,matches:s}),a=a.slice(i.length));if(!i)break}return e?a.length:a?J.error(t):x(t,l).slice(0)}function ut(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function dt(t,e,i){var n=e.dir,s=e.next,o=s||n,r=i&&"parentNode"===o,a=b++;return e.first?function(e,i,s){for(;e=e[n];)if(1===e.nodeType||r)return t(e,i,s);return!1}:function(e,i,l){var c,h,u=[y,a];if(l){for(;e=e[n];)if((1===e.nodeType||r)&&t(e,i,l))return!0}else for(;e=e[n];)if(1===e.nodeType||r)if(h=e[v]||(e[v]={}),s&&k(e,s))e=e[n]||e;else{if((c=h[o])&&c[0]===y&&c[1]===a)return u[2]=c[2];if(h[o]=u,u[2]=t(e,i,l))return!0}return!1}}function pt(t){return t.length>1?function(e,i,n){for(var s=t.length;s--;)if(!t[s](e,i,n))return!1;return!0}:t[0]}function ft(t,e,i,n,s){for(var o,r=[],a=0,l=t.length,c=null!=e;a<l;a++)(o=t[a])&&(i&&!i(o,n,s)||(r.push(o),c&&e.push(a)));return r}function mt(t,e,i,n,s,o){return n&&!n[v]&&(n=mt(n)),s&&!s[v]&&(s=mt(s,o)),et(function(o,r,l,c){var h,u,d,p,f=[],m=[],v=r.length,y=o||function(t,e,i){for(var n=0,s=e.length;n<s;n++)J(t,e[n],i);return i}(e||"*",l.nodeType?[l]:l,[]),b=!t||!o&&e?y:ft(y,f,t,l,c);if(i?i(b,p=s||(o?t:v||n)?[]:r,l,c):p=b,n)for(h=ft(p,m),n(h,[],l,c),u=h.length;u--;)(d=h[u])&&(p[m[u]]=!(b[m[u]]=d));if(o){if(s||t){if(s){for(h=[],u=p.length;u--;)(d=p[u])&&h.push(b[u]=d);s(null,p=[],h,c)}for(u=p.length;u--;)(d=p[u])&&(h=s?a.call(o,d):f[u])>-1&&(o[h]=!(r[h]=d))}}else p=ft(p===r?p.splice(v,p.length):p),s?s(null,r,p,c):g.apply(r,p)})}function gt(t){for(var e,i,s,r=t.length,l=n.relative[t[0].type],c=l||n.relative[" "],h=l?1:0,u=dt(function(t){return t===e},c,!0),d=dt(function(t){return a.call(e,t)>-1},c,!0),p=[function(t,i,n){var s=!l&&(n||i!=o)||((e=i).nodeType?u(t,i,n):d(t,i,n));return e=null,s}];h<r;h++)if(i=n.relative[t[h].type])p=[dt(pt(p),i)];else{if((i=n.filter[t[h].type].apply(null,t[h].matches))[v]){for(s=++h;s<r&&!n.relative[t[s].type];s++);return mt(h>1&&pt(p),h>1&&ut(t.slice(0,h-1).concat({value:" "===t[h-2].type?"*":""})).replace(M,"$1"),i,h<s&&gt(t.slice(h,s)),s<r&&gt(t=t.slice(s)),s<r&&ut(t))}p.push(i)}return pt(p)}function vt(t,e){var i,s=[],r=[],a=E[t+" "];if(!a){for(e||(e=ht(t)),i=e.length;i--;)(a=gt(e[i]))[v]?s.push(a):r.push(a);(a=E(t,function(t,e){var i=e.length>0,s=t.length>0,r=function(r,a,l,h,u){var p,f,m,v=0,b="0",_=r&&[],x=[],k=o,C=r||s&&n.find.TAG("*",u),S=y+=null==k?1:Math.random()||.1,T=C.length;for(u&&(o=a==c||a||u);b!==T&&null!=(p=C[b]);b++){if(s&&p){for(f=0,a||p.ownerDocument==c||(lt(p),l=!d);m=t[f++];)if(m(p,a||c,l)){g.call(h,p);break}u&&(y=S)}i&&((p=!m&&p)&&v--,r&&_.push(p))}if(v+=b,i&&b!==v){for(f=0;m=e[f++];)m(_,x,a,l);if(r){if(v>0)for(;b--;)_[b]||x[b]||(x[b]=D.call(h));x=ft(x)}g.apply(h,x),u&&!r&&x.length>0&&v+e.length>1&&w.uniqueSort(h)}return u&&(y=S,o=k),_};return i?et(r):r}(r,s))).selector=t}return a}function yt(t,e,i,s){var o,r,a,l,c,h="function"==typeof t&&t,u=!s&&ht(t=h.selector||t);if(i=i||[],1===u.length){if((r=u[0]=u[0].slice(0)).length>2&&"ID"===(a=r[0]).type&&9===e.nodeType&&d&&n.relative[r[1].type]){if(!(e=(n.find.ID(a.matches[0].replace(X,K),e)||[])[0]))return i;h&&(e=e.parentNode),t=t.slice(r.shift().value.length)}for(o=q.needsContext.test(t)?0:r.length;o--&&(a=r[o],!n.relative[l=a.type]);)if((c=n.find[l])&&(s=c(a.matches[0].replace(X,K),G.test(r[0].type)&&at(e.parentNode)||e))){if(r.splice(o,1),!(t=s.length&&ut(r)))return g.apply(i,s),i;break}}return(h||vt(t,u))(s,e,!d,i,!e||G.test(t)&&at(e.parentNode)||e),i}ct.prototype=n.filters=n.pseudos,n.setFilters=new ct,p.sortStable=v.split("").sort(L).join("")===v,lt(),p.sortDetached=it(function(t){return 1&t.compareDocumentPosition(c.createElement("fieldset"))}),w.find=J,w.expr[":"]=w.expr.pseudos,w.unique=w.uniqueSort,J.compile=vt,J.select=yt,J.setDocument=lt,J.tokenize=ht,J.escape=w.escapeSelector,J.getText=w.text,J.isXML=w.isXMLDoc,J.selectors=w.expr,J.support=w.support,J.uniqueSort=w.uniqueSort}();var L=function(t,e,i){for(var n=[],s=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(s&&w(t).is(i))break;n.push(t)}return n},P=function(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i},j=w.expr.match.needsContext,$=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Y(t,e,i){return f(e)?w.grep(t,function(t,n){return!!e.call(t,n,t)!==i}):e.nodeType?w.grep(t,function(t){return t===e!==i}):"string"!=typeof e?w.grep(t,function(t){return a.call(e,t)>-1!==i}):w.filter(e,t,i)}w.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?w.find.matchesSelector(n,t)?[n]:[]:w.find.matches(t,w.grep(e,function(t){return 1===t.nodeType}))},w.fn.extend({find:function(t){var e,i,n=this.length,s=this;if("string"!=typeof t)return this.pushStack(w(t).filter(function(){for(e=0;e<n;e++)if(w.contains(s[e],this))return!0}));for(i=this.pushStack([]),e=0;e<n;e++)w.find(t,s[e],i);return n>1?w.uniqueSort(i):i},filter:function(t){return this.pushStack(Y(this,t||[],!1))},not:function(t){return this.pushStack(Y(this,t||[],!0))},is:function(t){return!!Y(this,"string"==typeof t&&j.test(t)?w(t):t||[],!1).length}});var I,W=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(w.fn.init=function(t,e,i){var n,s;if(!t)return this;if(i=i||I,"string"==typeof t){if(!(n="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:W.exec(t))||!n[1]&&e)return!e||e.jquery?(e||i).find(t):this.constructor(e).find(t);if(n[1]){if(e=e instanceof w?e[0]:e,w.merge(this,w.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:g,!0)),$.test(n[1])&&w.isPlainObject(e))for(n in e)f(this[n])?this[n](e[n]):this.attr(n,e[n]);return this}return(s=g.getElementById(n[2]))&&(this[0]=s,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):f(t)?void 0!==i.ready?i.ready(t):t(w):w.makeArray(t,this)}).prototype=w.fn,I=w(g);var H=/^(?:parents|prev(?:Until|All))/,R={children:!0,contents:!0,next:!0,prev:!0};function F(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}w.fn.extend({has:function(t){var e=w(t,this),i=e.length;return this.filter(function(){for(var t=0;t<i;t++)if(w.contains(this,e[t]))return!0})},closest:function(t,e){var i,n=0,s=this.length,o=[],r="string"!=typeof t&&w(t);if(!j.test(t))for(;n<s;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?r.index(i)>-1:1===i.nodeType&&w.find.matchesSelector(i,t))){o.push(i);break}return this.pushStack(o.length>1?w.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?a.call(w(t),this[0]):a.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(w.uniqueSort(w.merge(this.get(),w(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),w.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return L(t,"parentNode")},parentsUntil:function(t,e,i){return L(t,"parentNode",i)},next:function(t){return F(t,"nextSibling")},prev:function(t){return F(t,"previousSibling")},nextAll:function(t){return L(t,"nextSibling")},prevAll:function(t){return L(t,"previousSibling")},nextUntil:function(t,e,i){return L(t,"nextSibling",i)},prevUntil:function(t,e,i){return L(t,"previousSibling",i)},siblings:function(t){return P((t.parentNode||{}).firstChild,t)},children:function(t){return P(t.firstChild)},contents:function(t){return null!=t.contentDocument&&n(t.contentDocument)?t.contentDocument:(k(t,"template")&&(t=t.content||t),w.merge([],t.childNodes))}},function(t,e){w.fn[t]=function(i,n){var s=w.map(this,e,i);return"Until"!==t.slice(-5)&&(n=i),n&&"string"==typeof n&&(s=w.filter(n,s)),this.length>1&&(R[t]||w.uniqueSort(s),H.test(t)&&s.reverse()),this.pushStack(s)}});var U=/[^\x20\t\r\n\f]+/g;function q(t){return t}function z(t){throw t}function V(t,e,i,n){var s;try{t&&f(s=t.promise)?s.call(t).done(e).fail(i):t&&f(s=t.then)?s.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}w.Callbacks=function(t){t="string"==typeof t?function(t){var e={};return w.each(t.match(U)||[],function(t,i){e[i]=!0}),e}(t):w.extend({},t);var e,i,n,s,o=[],r=[],a=-1,l=function(){for(s=s||t.once,n=e=!0;r.length;a=-1)for(i=r.shift();++a<o.length;)!1===o[a].apply(i[0],i[1])&&t.stopOnFalse&&(a=o.length,i=!1);t.memory||(i=!1),e=!1,s&&(o=i?[]:"")},c={add:function(){return o&&(i&&!e&&(a=o.length-1,r.push(i)),function e(i){w.each(i,function(i,n){f(n)?t.unique&&c.has(n)||o.push(n):n&&n.length&&"string"!==b(n)&&e(n)})}(arguments),i&&!e&&l()),this},remove:function(){return w.each(arguments,function(t,e){for(var i;(i=w.inArray(e,o,i))>-1;)o.splice(i,1),i<=a&&a--}),this},has:function(t){return t?w.inArray(t,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return s=r=[],o=i="",this},disabled:function(){return!o},lock:function(){return s=r=[],i||e||(o=i=""),this},locked:function(){return!!s},fireWith:function(t,i){return s||(i=[t,(i=i||[]).slice?i.slice():i],r.push(i),e||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!n}};return c},w.extend({Deferred:function(e){var i=[["notify","progress",w.Callbacks("memory"),w.Callbacks("memory"),2],["resolve","done",w.Callbacks("once memory"),w.Callbacks("once memory"),0,"resolved"],["reject","fail",w.Callbacks("once memory"),w.Callbacks("once memory"),1,"rejected"]],n="pending",s={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(t){return s.then(null,t)},pipe:function(){var t=arguments;return w.Deferred(function(e){w.each(i,function(i,n){var s=f(t[n[4]])&&t[n[4]];o[n[1]](function(){var t=s&&s.apply(this,arguments);t&&f(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[n[0]+"With"](this,s?[t]:arguments)})}),t=null}).promise()},then:function(e,n,s){var o=0;function r(e,i,n,s){return function(){var a=this,l=arguments,c=function(){var t,c;if(!(e<o)){if((t=n.apply(a,l))===i.promise())throw new TypeError("Thenable self-resolution");c=t&&("object"==typeof t||"function"==typeof t)&&t.then,f(c)?s?c.call(t,r(o,i,q,s),r(o,i,z,s)):(o++,c.call(t,r(o,i,q,s),r(o,i,z,s),r(o,i,q,i.notifyWith))):(n!==q&&(a=void 0,l=[t]),(s||i.resolveWith)(a,l))}},h=s?c:function(){try{c()}catch(t){w.Deferred.exceptionHook&&w.Deferred.exceptionHook(t,h.error),e+1>=o&&(n!==z&&(a=void 0,l=[t]),i.rejectWith(a,l))}};e?h():(w.Deferred.getErrorHook?h.error=w.Deferred.getErrorHook():w.Deferred.getStackHook&&(h.error=w.Deferred.getStackHook()),t.setTimeout(h))}}return w.Deferred(function(t){i[0][3].add(r(0,t,f(s)?s:q,t.notifyWith)),i[1][3].add(r(0,t,f(e)?e:q)),i[2][3].add(r(0,t,f(n)?n:z))}).promise()},promise:function(t){return null!=t?w.extend(t,s):s}},o={};return w.each(i,function(t,e){var r=e[2],a=e[5];s[e[1]]=r.add,a&&r.add(function(){n=a},i[3-t][2].disable,i[3-t][3].disable,i[0][2].lock,i[0][3].lock),r.add(e[3].fire),o[e[0]]=function(){return o[e[0]+"With"](this===o?void 0:this,arguments),this},o[e[0]+"With"]=r.fireWith}),s.promise(o),e&&e.call(o,o),o},when:function(t){var e=arguments.length,i=e,n=Array(i),o=s.call(arguments),r=w.Deferred(),a=function(t){return function(i){n[t]=this,o[t]=arguments.length>1?s.call(arguments):i,--e||r.resolveWith(n,o)}};if(e<=1&&(V(t,r.done(a(i)).resolve,r.reject,!e),"pending"===r.state()||f(o[i]&&o[i].then)))return r.then();for(;i--;)V(o[i],a(i),r.reject);return r.promise()}});var B=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;w.Deferred.exceptionHook=function(e,i){t.console&&t.console.warn&&e&&B.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,i)},w.readyException=function(e){t.setTimeout(function(){throw e})};var G=w.Deferred();function X(){g.removeEventListener("DOMContentLoaded",X),t.removeEventListener("load",X),w.ready()}w.fn.ready=function(t){return G.then(t).catch(function(t){w.readyException(t)}),this},w.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--w.readyWait:w.isReady)||(w.isReady=!0,!0!==t&&--w.readyWait>0||G.resolveWith(g,[w]))}}),w.ready.then=G.then,"complete"===g.readyState||"loading"!==g.readyState&&!g.documentElement.doScroll?t.setTimeout(w.ready):(g.addEventListener("DOMContentLoaded",X),t.addEventListener("load",X));var K=function(t,e,i,n,s,o,r){var a=0,l=t.length,c=null==i;if("object"===b(i))for(a in s=!0,i)K(t,e,a,i[a],!0,o,r);else if(void 0!==n&&(s=!0,f(n)||(r=!0),c&&(r?(e.call(t,n),e=null):(c=e,e=function(t,e,i){return c.call(w(t),i)})),e))for(;a<l;a++)e(t[a],i,r?n:n.call(t[a],a,e(t[a],i)));return s?t:c?e.call(t):l?e(t[0],i):o},Z=/^-ms-/,Q=/-([a-z])/g;function J(t,e){return e.toUpperCase()}function tt(t){return t.replace(Z,"ms-").replace(Q,J)}var et=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function it(){this.expando=w.expando+it.uid++}it.uid=1,it.prototype={cache:function(t){var e=t[this.expando];return e||(e={},et(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,s=this.cache(t);if("string"==typeof e)s[tt(e)]=i;else for(n in e)s[tt(n)]=e[n];return s},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][tt(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(tt):(e=tt(e))in n?[e]:e.match(U)||[]).length;for(;i--;)delete n[e[i]]}(void 0===e||w.isEmptyObject(n))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!w.isEmptyObject(e)}};var nt=new it,st=new it,ot=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,rt=/[A-Z]/g;function at(t,e,i){var n;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(rt,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i=function(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:ot.test(t)?JSON.parse(t):t)}(i)}catch(t){}st.set(t,e,i)}else i=void 0;return i}w.extend({hasData:function(t){return st.hasData(t)||nt.hasData(t)},data:function(t,e,i){return st.access(t,e,i)},removeData:function(t,e){st.remove(t,e)},_data:function(t,e,i){return nt.access(t,e,i)},_removeData:function(t,e){nt.remove(t,e)}}),w.fn.extend({data:function(t,e){var i,n,s,o=this[0],r=o&&o.attributes;if(void 0===t){if(this.length&&(s=st.get(o),1===o.nodeType&&!nt.get(o,"hasDataAttrs"))){for(i=r.length;i--;)r[i]&&0===(n=r[i].name).indexOf("data-")&&(n=tt(n.slice(5)),at(o,n,s[n]));nt.set(o,"hasDataAttrs",!0)}return s}return"object"==typeof t?this.each(function(){st.set(this,t)}):K(this,function(e){var i;if(o&&void 0===e)return void 0!==(i=st.get(o,t))?i:void 0!==(i=at(o,t))?i:void 0;this.each(function(){st.set(this,t,e)})},null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each(function(){st.remove(this,t)})}}),w.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=nt.get(t,e),i&&(!n||Array.isArray(i)?n=nt.access(t,e,w.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=w.queue(t,e),n=i.length,s=i.shift(),o=w._queueHooks(t,e);"inprogress"===s&&(s=i.shift(),n--),s&&("fx"===e&&i.unshift("inprogress"),delete o.stop,s.call(t,function(){w.dequeue(t,e)},o)),!n&&o&&o.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return nt.get(t,i)||nt.access(t,i,{empty:w.Callbacks("once memory").add(function(){nt.remove(t,[e+"queue",i])})})}}),w.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?w.queue(this[0],t):void 0===e?this:this.each(function(){var i=w.queue(this,t,e);w._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&w.dequeue(this,t)})},dequeue:function(t){return this.each(function(){w.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var i,n=1,s=w.Deferred(),o=this,r=this.length,a=function(){--n||s.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)(i=nt.get(o[r],t+"queueHooks"))&&i.empty&&(n++,i.empty.add(a));return a(),s.promise(e)}});var lt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ct=new RegExp("^(?:([+-])=|)("+lt+")([a-z%]*)$","i"),ht=["Top","Right","Bottom","Left"],ut=g.documentElement,dt=function(t){return w.contains(t.ownerDocument,t)},pt={composed:!0};ut.getRootNode&&(dt=function(t){return w.contains(t.ownerDocument,t)||t.getRootNode(pt)===t.ownerDocument});var ft=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&dt(t)&&"none"===w.css(t,"display")};function mt(t,e,i,n){var s,o,r=20,a=n?function(){return n.cur()}:function(){return w.css(t,e,"")},l=a(),c=i&&i[3]||(w.cssNumber[e]?"":"px"),h=t.nodeType&&(w.cssNumber[e]||"px"!==c&&+l)&&ct.exec(w.css(t,e));if(h&&h[3]!==c){for(l/=2,c=c||h[3],h=+l||1;r--;)w.style(t,e,h+c),(1-o)*(1-(o=a()/l||.5))<=0&&(r=0),h/=o;h*=2,w.style(t,e,h+c),i=i||[]}return i&&(h=+h||+l||0,s=i[1]?h+(i[1]+1)*i[2]:+i[2],n&&(n.unit=c,n.start=h,n.end=s)),s}var gt={};function vt(t){var e,i=t.ownerDocument,n=t.nodeName,s=gt[n];return s||(e=i.body.appendChild(i.createElement(n)),s=w.css(e,"display"),e.parentNode.removeChild(e),"none"===s&&(s="block"),gt[n]=s,s)}function yt(t,e){for(var i,n,s=[],o=0,r=t.length;o<r;o++)(n=t[o]).style&&(i=n.style.display,e?("none"===i&&(s[o]=nt.get(n,"display")||null,s[o]||(n.style.display="")),""===n.style.display&&ft(n)&&(s[o]=vt(n))):"none"!==i&&(s[o]="none",nt.set(n,"display",i)));for(o=0;o<r;o++)null!=s[o]&&(t[o].style.display=s[o]);return t}w.fn.extend({show:function(){return yt(this,!0)},hide:function(){return yt(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){ft(this)?w(this).show():w(this).hide()})}});var bt,_t,wt=/^(?:checkbox|radio)$/i,xt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,kt=/^$|^module$|\/(?:java|ecma)script/i;bt=g.createDocumentFragment().appendChild(g.createElement("div")),(_t=g.createElement("input")).setAttribute("type","radio"),_t.setAttribute("checked","checked"),_t.setAttribute("name","t"),bt.appendChild(_t),p.checkClone=bt.cloneNode(!0).cloneNode(!0).lastChild.checked,bt.innerHTML="<textarea>x</textarea>",p.noCloneChecked=!!bt.cloneNode(!0).lastChild.defaultValue,bt.innerHTML="<option></option>",p.option=!!bt.lastChild;var Dt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Ct(t,e){var i;return i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&k(t,e)?w.merge([t],i):i}function St(t,e){for(var i=0,n=t.length;i<n;i++)nt.set(t[i],"globalEval",!e||nt.get(e[i],"globalEval"))}Dt.tbody=Dt.tfoot=Dt.colgroup=Dt.caption=Dt.thead,Dt.th=Dt.td,p.option||(Dt.optgroup=Dt.option=[1,"<select multiple='multiple'>","</select>"]);var Tt=/<|&#?\w+;/;function Mt(t,e,i,n,s){for(var o,r,a,l,c,h,u=e.createDocumentFragment(),d=[],p=0,f=t.length;p<f;p++)if((o=t[p])||0===o)if("object"===b(o))w.merge(d,o.nodeType?[o]:o);else if(Tt.test(o)){for(r=r||u.appendChild(e.createElement("div")),a=(xt.exec(o)||["",""])[1].toLowerCase(),l=Dt[a]||Dt._default,r.innerHTML=l[1]+w.htmlPrefilter(o)+l[2],h=l[0];h--;)r=r.lastChild;w.merge(d,r.childNodes),(r=u.firstChild).textContent=""}else d.push(e.createTextNode(o));for(u.textContent="",p=0;o=d[p++];)if(n&&w.inArray(o,n)>-1)s&&s.push(o);else if(c=dt(o),r=Ct(u.appendChild(o),"script"),c&&St(r),i)for(h=0;o=r[h++];)kt.test(o.type||"")&&i.push(o);return u}var Et=/^([^.]*)(?:\.(.+)|)/;function At(){return!0}function Ot(){return!1}function Nt(t,e,i,n,s,o){var r,a;if("object"==typeof e){for(a in"string"!=typeof i&&(n=n||i,i=void 0),e)Nt(t,a,i,n,e[a],o);return t}if(null==n&&null==s?(s=i,n=i=void 0):null==s&&("string"==typeof i?(s=n,n=void 0):(s=n,n=i,i=void 0)),!1===s)s=Ot;else if(!s)return t;return 1===o&&(r=s,(s=function(t){return w().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=w.guid++)),t.each(function(){w.event.add(this,e,s,n,i)})}function Lt(t,e,i){i?(nt.set(t,e,!1),w.event.add(t,e,{namespace:!1,handler:function(t){var i,n=nt.get(this,e);if(1&t.isTrigger&&this[e]){if(n)(w.event.special[e]||{}).delegateType&&t.stopPropagation();else if(n=s.call(arguments),nt.set(this,e,n),this[e](),i=nt.get(this,e),nt.set(this,e,!1),n!==i)return t.stopImmediatePropagation(),t.preventDefault(),i}else n&&(nt.set(this,e,w.event.trigger(n[0],n.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=At)}})):void 0===nt.get(t,e)&&w.event.add(t,e,At)}w.event={global:{},add:function(t,e,i,n,s){var o,r,a,l,c,h,u,d,p,f,m,g=nt.get(t);if(et(t))for(i.handler&&(i=(o=i).handler,s=o.selector),s&&w.find.matchesSelector(ut,s),i.guid||(i.guid=w.guid++),(l=g.events)||(l=g.events=Object.create(null)),(r=g.handle)||(r=g.handle=function(e){return void 0!==w&&w.event.triggered!==e.type?w.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(U)||[""]).length;c--;)p=m=(a=Et.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),p&&(u=w.event.special[p]||{},p=(s?u.delegateType:u.bindType)||p,u=w.event.special[p]||{},h=w.extend({type:p,origType:m,data:n,handler:i,guid:i.guid,selector:s,needsContext:s&&w.expr.match.needsContext.test(s),namespace:f.join(".")},o),(d=l[p])||((d=l[p]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,n,f,r)||t.addEventListener&&t.addEventListener(p,r)),u.add&&(u.add.call(t,h),h.handler.guid||(h.handler.guid=i.guid)),s?d.splice(d.delegateCount++,0,h):d.push(h),w.event.global[p]=!0)},remove:function(t,e,i,n,s){var o,r,a,l,c,h,u,d,p,f,m,g=nt.hasData(t)&&nt.get(t);if(g&&(l=g.events)){for(c=(e=(e||"").match(U)||[""]).length;c--;)if(p=m=(a=Et.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),p){for(u=w.event.special[p]||{},d=l[p=(n?u.delegateType:u.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=o=d.length;o--;)h=d[o],!s&&m!==h.origType||i&&i.guid!==h.guid||a&&!a.test(h.namespace)||n&&n!==h.selector&&("**"!==n||!h.selector)||(d.splice(o,1),h.selector&&d.delegateCount--,u.remove&&u.remove.call(t,h));r&&!d.length&&(u.teardown&&!1!==u.teardown.call(t,f,g.handle)||w.removeEvent(t,p,g.handle),delete l[p])}else for(p in l)w.event.remove(t,p+e[c],i,n,!0);w.isEmptyObject(l)&&nt.remove(t,"handle events")}},dispatch:function(t){var e,i,n,s,o,r,a=new Array(arguments.length),l=w.event.fix(t),c=(nt.get(this,"events")||Object.create(null))[l.type]||[],h=w.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!h.preDispatch||!1!==h.preDispatch.call(this,l)){for(r=w.event.handlers.call(this,l,c),e=0;(s=r[e++])&&!l.isPropagationStopped();)for(l.currentTarget=s.elem,i=0;(o=s.handlers[i++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(n=((w.event.special[o.origType]||{}).handle||o.handler).apply(s.elem,a))&&!1===(l.result=n)&&(l.preventDefault(),l.stopPropagation()));return h.postDispatch&&h.postDispatch.call(this,l),l.result}},handlers:function(t,e){var i,n,s,o,r,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&t.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(o=[],r={},i=0;i<l;i++)void 0===r[s=(n=e[i]).selector+" "]&&(r[s]=n.needsContext?w(s,this).index(c)>-1:w.find(s,this,null,[c]).length),r[s]&&o.push(n);o.length&&a.push({elem:c,handlers:o})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(w.Event.prototype,t,{enumerable:!0,configurable:!0,get:f(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[w.expando]?t:new w.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return wt.test(e.type)&&e.click&&k(e,"input")&&Lt(e,"click",!0),!1},trigger:function(t){var e=this||t;return wt.test(e.type)&&e.click&&k(e,"input")&&Lt(e,"click"),!0},_default:function(t){var e=t.target;return wt.test(e.type)&&e.click&&k(e,"input")&&nt.get(e,"click")||k(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},w.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},w.Event=function(t,e){if(!(this instanceof w.Event))return new w.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?At:Ot,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&w.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[w.expando]=!0},w.Event.prototype={constructor:w.Event,isDefaultPrevented:Ot,isPropagationStopped:Ot,isImmediatePropagationStopped:Ot,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=At,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=At,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=At,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},w.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},w.event.addProp),w.each({focus:"focusin",blur:"focusout"},function(t,e){function i(t){if(g.documentMode){var i=nt.get(this,"handle"),n=w.event.fix(t);n.type="focusin"===t.type?"focus":"blur",n.isSimulated=!0,i(t),n.target===n.currentTarget&&i(n)}else w.event.simulate(e,t.target,w.event.fix(t))}w.event.special[t]={setup:function(){var n;if(Lt(this,t,!0),!g.documentMode)return!1;(n=nt.get(this,e))||this.addEventListener(e,i),nt.set(this,e,(n||0)+1)},trigger:function(){return Lt(this,t),!0},teardown:function(){var t;if(!g.documentMode)return!1;(t=nt.get(this,e)-1)?nt.set(this,e,t):(this.removeEventListener(e,i),nt.remove(this,e))},_default:function(e){return nt.get(e.target,t)},delegateType:e},w.event.special[e]={setup:function(){var n=this.ownerDocument||this.document||this,s=g.documentMode?this:n,o=nt.get(s,e);o||(g.documentMode?this.addEventListener(e,i):n.addEventListener(t,i,!0)),nt.set(s,e,(o||0)+1)},teardown:function(){var n=this.ownerDocument||this.document||this,s=g.documentMode?this:n,o=nt.get(s,e)-1;o?nt.set(s,e,o):(g.documentMode?this.removeEventListener(e,i):n.removeEventListener(t,i,!0),nt.remove(s,e))}}}),w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){w.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=t.relatedTarget,s=t.handleObj;return n&&(n===this||w.contains(this,n))||(t.type=s.origType,i=s.handler.apply(this,arguments),t.type=e),i}}}),w.fn.extend({on:function(t,e,i,n){return Nt(this,t,e,i,n)},one:function(t,e,i,n){return Nt(this,t,e,i,n,1)},off:function(t,e,i){var n,s;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,w(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof t){for(s in t)this.off(s,e,t[s]);return this}return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=Ot),this.each(function(){w.event.remove(this,t,i,e)})}});var Pt=/<script|<style|<link/i,jt=/checked\s*(?:[^=]|=\s*.checked.)/i,$t=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Yt(t,e){return k(t,"table")&&k(11!==e.nodeType?e:e.firstChild,"tr")&&w(t).children("tbody")[0]||t}function It(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Wt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Ht(t,e){var i,n,s,o,r,a;if(1===e.nodeType){if(nt.hasData(t)&&(a=nt.get(t).events))for(s in nt.remove(e,"handle events"),a)for(i=0,n=a[s].length;i<n;i++)w.event.add(e,s,a[s][i]);st.hasData(t)&&(o=st.access(t),r=w.extend({},o),st.set(e,r))}}function Rt(t,e,i,n){e=o(e);var s,r,a,l,c,h,u=0,d=t.length,m=d-1,g=e[0],v=f(g);if(v||d>1&&"string"==typeof g&&!p.checkClone&&jt.test(g))return t.each(function(s){var o=t.eq(s);v&&(e[0]=g.call(this,s,o.html())),Rt(o,e,i,n)});if(d&&(r=(s=Mt(e,t[0].ownerDocument,!1,t,n)).firstChild,1===s.childNodes.length&&(s=r),r||n)){for(l=(a=w.map(Ct(s,"script"),It)).length;u<d;u++)c=s,u!==m&&(c=w.clone(c,!0,!0),l&&w.merge(a,Ct(c,"script"))),i.call(t[u],c,u);if(l)for(h=a[a.length-1].ownerDocument,w.map(a,Wt),u=0;u<l;u++)c=a[u],kt.test(c.type||"")&&!nt.access(c,"globalEval")&&w.contains(h,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?w._evalUrl&&!c.noModule&&w._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},h):y(c.textContent.replace($t,""),c,h))}return t}function Ft(t,e,i){for(var n,s=e?w.filter(e,t):t,o=0;null!=(n=s[o]);o++)i||1!==n.nodeType||w.cleanData(Ct(n)),n.parentNode&&(i&&dt(n)&&St(Ct(n,"script")),n.parentNode.removeChild(n));return t}w.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,s,o,r,a,l,c,h=t.cloneNode(!0),u=dt(t);if(!(p.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||w.isXMLDoc(t)))for(r=Ct(h),n=0,s=(o=Ct(t)).length;n<s;n++)a=o[n],l=r[n],c=void 0,"input"===(c=l.nodeName.toLowerCase())&&wt.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(e)if(i)for(o=o||Ct(t),r=r||Ct(h),n=0,s=o.length;n<s;n++)Ht(o[n],r[n]);else Ht(t,h);return(r=Ct(h,"script")).length>0&&St(r,!u&&Ct(t,"script")),h},cleanData:function(t){for(var e,i,n,s=w.event.special,o=0;void 0!==(i=t[o]);o++)if(et(i)){if(e=i[nt.expando]){if(e.events)for(n in e.events)s[n]?w.event.remove(i,n):w.removeEvent(i,n,e.handle);i[nt.expando]=void 0}i[st.expando]&&(i[st.expando]=void 0)}}}),w.fn.extend({detach:function(t){return Ft(this,t,!0)},remove:function(t){return Ft(this,t)},text:function(t){return K(this,function(t){return void 0===t?w.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Rt(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Yt(this,t).appendChild(t)})},prepend:function(){return Rt(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=Yt(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return Rt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Rt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(w.cleanData(Ct(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return w.clone(this,t,e)})},html:function(t){return K(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Pt.test(t)&&!Dt[(xt.exec(t)||["",""])[1].toLowerCase()]){t=w.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(w.cleanData(Ct(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return Rt(this,arguments,function(e){var i=this.parentNode;w.inArray(this,t)<0&&(w.cleanData(Ct(this)),i&&i.replaceChild(e,this))},t)}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){w.fn[t]=function(t){for(var i,n=[],s=w(t),o=s.length-1,a=0;a<=o;a++)i=a===o?this:this.clone(!0),w(s[a])[e](i),r.apply(n,i.get());return this.pushStack(n)}});var Ut=new RegExp("^("+lt+")(?!px)[a-z%]+$","i"),qt=/^--/,zt=function(e){var i=e.ownerDocument.defaultView;return i&&i.opener||(i=t),i.getComputedStyle(e)},Vt=function(t,e,i){var n,s,o={};for(s in e)o[s]=t.style[s],t.style[s]=e[s];for(s in n=i.call(t),e)t.style[s]=o[s];return n},Bt=new RegExp(ht.join("|"),"i");function Gt(t,e,i){var n,s,o,r,a=qt.test(e),l=t.style;return(i=i||zt(t))&&(r=i.getPropertyValue(e)||i[e],a&&r&&(r=r.replace(M,"$1")||void 0),""!==r||dt(t)||(r=w.style(t,e)),!p.pixelBoxStyles()&&Ut.test(r)&&Bt.test(e)&&(n=l.width,s=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=r,r=i.width,l.width=n,l.minWidth=s,l.maxWidth=o)),void 0!==r?r+"":r}function Xt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function e(){if(h){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",h.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ut.appendChild(c).appendChild(h);var e=t.getComputedStyle(h);n="1%"!==e.top,l=12===i(e.marginLeft),h.style.right="60%",r=36===i(e.right),s=36===i(e.width),h.style.position="absolute",o=12===i(h.offsetWidth/3),ut.removeChild(c),h=null}}function i(t){return Math.round(parseFloat(t))}var n,s,o,r,a,l,c=g.createElement("div"),h=g.createElement("div");h.style&&(h.style.backgroundClip="content-box",h.cloneNode(!0).style.backgroundClip="",p.clearCloneStyle="content-box"===h.style.backgroundClip,w.extend(p,{boxSizingReliable:function(){return e(),s},pixelBoxStyles:function(){return e(),r},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,i,n,s;return null==a&&(e=g.createElement("table"),i=g.createElement("tr"),n=g.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",i.style.cssText="box-sizing:content-box;border:1px solid",i.style.height="1px",n.style.height="9px",n.style.display="block",ut.appendChild(e).appendChild(i).appendChild(n),s=t.getComputedStyle(i),a=parseInt(s.height,10)+parseInt(s.borderTopWidth,10)+parseInt(s.borderBottomWidth,10)===i.offsetHeight,ut.removeChild(e)),a}}))}();var Kt=["Webkit","Moz","ms"],Zt=g.createElement("div").style,Qt={};function Jt(t){var e=w.cssProps[t]||Qt[t];return e||(t in Zt?t:Qt[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),i=Kt.length;i--;)if((t=Kt[i]+e)in Zt)return t}(t)||t)}var te=/^(none|table(?!-c[ea]).+)/,ee={position:"absolute",visibility:"hidden",display:"block"},ie={letterSpacing:"0",fontWeight:"400"};function ne(t,e,i){var n=ct.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function se(t,e,i,n,s,o){var r="width"===e?1:0,a=0,l=0,c=0;if(i===(n?"border":"content"))return 0;for(;r<4;r+=2)"margin"===i&&(c+=w.css(t,i+ht[r],!0,s)),n?("content"===i&&(l-=w.css(t,"padding"+ht[r],!0,s)),"margin"!==i&&(l-=w.css(t,"border"+ht[r]+"Width",!0,s))):(l+=w.css(t,"padding"+ht[r],!0,s),"padding"!==i?l+=w.css(t,"border"+ht[r]+"Width",!0,s):a+=w.css(t,"border"+ht[r]+"Width",!0,s));return!n&&o>=0&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-a-.5))||0),l+c}function oe(t,e,i){var n=zt(t),s=(!p.boxSizingReliable()||i)&&"border-box"===w.css(t,"boxSizing",!1,n),o=s,r=Gt(t,e,n),a="offset"+e[0].toUpperCase()+e.slice(1);if(Ut.test(r)){if(!i)return r;r="auto"}return(!p.boxSizingReliable()&&s||!p.reliableTrDimensions()&&k(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===w.css(t,"display",!1,n))&&t.getClientRects().length&&(s="border-box"===w.css(t,"boxSizing",!1,n),(o=a in t)&&(r=t[a])),(r=parseFloat(r)||0)+se(t,e,i||(s?"border":"content"),o,n,r)+"px"}function re(t,e,i,n,s){return new re.prototype.init(t,e,i,n,s)}w.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=Gt(t,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,o,r,a=tt(e),l=qt.test(e),c=t.style;if(l||(e=Jt(a)),r=w.cssHooks[e]||w.cssHooks[a],void 0===i)return r&&"get"in r&&void 0!==(s=r.get(t,!1,n))?s:c[e];"string"===(o=typeof i)&&(s=ct.exec(i))&&s[1]&&(i=mt(t,e,s),o="number"),null!=i&&i==i&&("number"!==o||l||(i+=s&&s[3]||(w.cssNumber[a]?"":"px")),p.clearCloneStyle||""!==i||0!==e.indexOf("background")||(c[e]="inherit"),r&&"set"in r&&void 0===(i=r.set(t,i,n))||(l?c.setProperty(e,i):c[e]=i))}},css:function(t,e,i,n){var s,o,r,a=tt(e);return qt.test(e)||(e=Jt(a)),(r=w.cssHooks[e]||w.cssHooks[a])&&"get"in r&&(s=r.get(t,!0,i)),void 0===s&&(s=Gt(t,e,n)),"normal"===s&&e in ie&&(s=ie[e]),""===i||i?(o=parseFloat(s),!0===i||isFinite(o)?o||0:s):s}}),w.each(["height","width"],function(t,e){w.cssHooks[e]={get:function(t,i,n){if(i)return!te.test(w.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?oe(t,e,n):Vt(t,ee,function(){return oe(t,e,n)})},set:function(t,i,n){var s,o=zt(t),r=!p.scrollboxSize()&&"absolute"===o.position,a=(r||n)&&"border-box"===w.css(t,"boxSizing",!1,o),l=n?se(t,e,n,a,o):0;return a&&r&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(o[e])-se(t,e,"border",!1,o)-.5)),l&&(s=ct.exec(i))&&"px"!==(s[3]||"px")&&(t.style[e]=i,i=w.css(t,e)),ne(0,i,l)}}}),w.cssHooks.marginLeft=Xt(p.reliableMarginLeft,function(t,e){if(e)return(parseFloat(Gt(t,"marginLeft"))||t.getBoundingClientRect().left-Vt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),w.each({margin:"",padding:"",border:"Width"},function(t,e){w.cssHooks[t+e]={expand:function(i){for(var n=0,s={},o="string"==typeof i?i.split(" "):[i];n<4;n++)s[t+ht[n]+e]=o[n]||o[n-2]||o[0];return s}},"margin"!==t&&(w.cssHooks[t+e].set=ne)}),w.fn.extend({css:function(t,e){return K(this,function(t,e,i){var n,s,o={},r=0;if(Array.isArray(e)){for(n=zt(t),s=e.length;r<s;r++)o[e[r]]=w.css(t,e[r],!1,n);return o}return void 0!==i?w.style(t,e,i):w.css(t,e)},t,e,arguments.length>1)}}),w.Tween=re,re.prototype={constructor:re,init:function(t,e,i,n,s,o){this.elem=t,this.prop=i,this.easing=s||w.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=o||(w.cssNumber[i]?"":"px")},cur:function(){var t=re.propHooks[this.prop];return t&&t.get?t.get(this):re.propHooks._default.get(this)},run:function(t){var e,i=re.propHooks[this.prop];return this.options.duration?this.pos=e=w.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):re.propHooks._default.set(this),this}},re.prototype.init.prototype=re.prototype,re.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=w.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){w.fx.step[t.prop]?w.fx.step[t.prop](t):1!==t.elem.nodeType||!w.cssHooks[t.prop]&&null==t.elem.style[Jt(t.prop)]?t.elem[t.prop]=t.now:w.style(t.elem,t.prop,t.now+t.unit)}}},re.propHooks.scrollTop=re.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},w.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},w.fx=re.prototype.init,w.fx.step={};var ae,le,ce=/^(?:toggle|show|hide)$/,he=/queueHooks$/;function ue(){le&&(!1===g.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(ue):t.setTimeout(ue,w.fx.interval),w.fx.tick())}function de(){return t.setTimeout(function(){ae=void 0}),ae=Date.now()}function pe(t,e){var i,n=0,s={height:t};for(e=e?1:0;n<4;n+=2-e)s["margin"+(i=ht[n])]=s["padding"+i]=t;return e&&(s.opacity=s.width=t),s}function fe(t,e,i){for(var n,s=(me.tweeners[e]||[]).concat(me.tweeners["*"]),o=0,r=s.length;o<r;o++)if(n=s[o].call(i,e,t))return n}function me(t,e,i){var n,s,o=0,r=me.prefilters.length,a=w.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var e=ae||de(),i=Math.max(0,c.startTime+c.duration-e),n=1-(i/c.duration||0),o=0,r=c.tweens.length;o<r;o++)c.tweens[o].run(n);return a.notifyWith(t,[c,n,i]),n<1&&r?i:(r||a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c]),!1)},c=a.promise({elem:t,props:w.extend({},e),opts:w.extend(!0,{specialEasing:{},easing:w.easing._default},i),originalProperties:e,originalOptions:i,startTime:ae||de(),duration:i.duration,tweens:[],createTween:function(e,i){var n=w.Tween(t,c.opts,e,i,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(n),n},stop:function(e){var i=0,n=e?c.tweens.length:0;if(s)return this;for(s=!0;i<n;i++)c.tweens[i].run(1);return e?(a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c,e])):a.rejectWith(t,[c,e]),this}}),h=c.props;for(!function(t,e){var i,n,s,o,r;for(i in t)if(s=e[n=tt(i)],o=t[i],Array.isArray(o)&&(s=o[1],o=t[i]=o[0]),i!==n&&(t[n]=o,delete t[i]),(r=w.cssHooks[n])&&"expand"in r)for(i in o=r.expand(o),delete t[n],o)i in t||(t[i]=o[i],e[i]=s);else e[n]=s}(h,c.opts.specialEasing);o<r;o++)if(n=me.prefilters[o].call(c,t,h,c.opts))return f(n.stop)&&(w._queueHooks(c.elem,c.opts.queue).stop=n.stop.bind(n)),n;return w.map(h,fe,c),f(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),w.fx.timer(w.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}w.Animation=w.extend(me,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return mt(i.elem,t,ct.exec(e),i),i}]},tweener:function(t,e){f(t)?(e=t,t=["*"]):t=t.match(U);for(var i,n=0,s=t.length;n<s;n++)i=t[n],me.tweeners[i]=me.tweeners[i]||[],me.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,s,o,r,a,l,c,h,u="width"in e||"height"in e,d=this,p={},f=t.style,m=t.nodeType&&ft(t),g=nt.get(t,"fxshow");for(n in i.queue||(null==(r=w._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,a=r.empty.fire,r.empty.fire=function(){r.unqueued||a()}),r.unqueued++,d.always(function(){d.always(function(){r.unqueued--,w.queue(t,"fx").length||r.empty.fire()})})),e)if(s=e[n],ce.test(s)){if(delete e[n],o=o||"toggle"===s,s===(m?"hide":"show")){if("show"!==s||!g||void 0===g[n])continue;m=!0}p[n]=g&&g[n]||w.style(t,n)}if((l=!w.isEmptyObject(e))||!w.isEmptyObject(p))for(n in u&&1===t.nodeType&&(i.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=g&&g.display)&&(c=nt.get(t,"display")),"none"===(h=w.css(t,"display"))&&(c?h=c:(yt([t],!0),c=t.style.display||c,h=w.css(t,"display"),yt([t]))),("inline"===h||"inline-block"===h&&null!=c)&&"none"===w.css(t,"float")&&(l||(d.done(function(){f.display=c}),null==c&&(h=f.display,c="none"===h?"":h)),f.display="inline-block")),i.overflow&&(f.overflow="hidden",d.always(function(){f.overflow=i.overflow[0],f.overflowX=i.overflow[1],f.overflowY=i.overflow[2]})),l=!1,p)l||(g?"hidden"in g&&(m=g.hidden):g=nt.access(t,"fxshow",{display:c}),o&&(g.hidden=!m),m&&yt([t],!0),d.done(function(){for(n in m||yt([t]),nt.remove(t,"fxshow"),p)w.style(t,n,p[n])})),l=fe(m?g[n]:0,n,d),n in g||(g[n]=l.start,m&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?me.prefilters.unshift(t):me.prefilters.push(t)}}),w.speed=function(t,e,i){var n=t&&"object"==typeof t?w.extend({},t):{complete:i||!i&&e||f(t)&&t,duration:t,easing:i&&e||e&&!f(e)&&e};return w.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in w.fx.speeds?n.duration=w.fx.speeds[n.duration]:n.duration=w.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){f(n.old)&&n.old.call(this),n.queue&&w.dequeue(this,n.queue)},n},w.fn.extend({fadeTo:function(t,e,i,n){return this.filter(ft).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){var s=w.isEmptyObject(t),o=w.speed(e,i,n),r=function(){var e=me(this,w.extend({},t),o);(s||nt.get(this,"finish"))&&e.stop(!0)};return r.finish=r,s||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(t,e,i){var n=function(t){var e=t.stop;delete t.stop,e(i)};return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,s=null!=t&&t+"queueHooks",o=w.timers,r=nt.get(this);if(s)r[s]&&r[s].stop&&n(r[s]);else for(s in r)r[s]&&r[s].stop&&he.test(s)&&n(r[s]);for(s=o.length;s--;)o[s].elem!==this||null!=t&&o[s].queue!==t||(o[s].anim.stop(i),e=!1,o.splice(s,1));!e&&i||w.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,i=nt.get(this),n=i[t+"queue"],s=i[t+"queueHooks"],o=w.timers,r=n?n.length:0;for(i.finish=!0,w.queue(this,t,[]),s&&s.stop&&s.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish})}}),w.each(["toggle","show","hide"],function(t,e){var i=w.fn[e];w.fn[e]=function(t,n,s){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(pe(e,!0),t,n,s)}}),w.each({slideDown:pe("show"),slideUp:pe("hide"),slideToggle:pe("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){w.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}}),w.timers=[],w.fx.tick=function(){var t,e=0,i=w.timers;for(ae=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||w.fx.stop(),ae=void 0},w.fx.timer=function(t){w.timers.push(t),w.fx.start()},w.fx.interval=13,w.fx.start=function(){le||(le=!0,ue())},w.fx.stop=function(){le=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(e,i){return e=w.fx&&w.fx.speeds[e]||e,i=i||"fx",this.queue(i,function(i,n){var s=t.setTimeout(i,e);n.stop=function(){t.clearTimeout(s)}})},function(){var t=g.createElement("input"),e=g.createElement("select").appendChild(g.createElement("option"));t.type="checkbox",p.checkOn=""!==t.value,p.optSelected=e.selected,(t=g.createElement("input")).value="t",t.type="radio",p.radioValue="t"===t.value}();var ge,ve=w.expr.attrHandle;w.fn.extend({attr:function(t,e){return K(this,w.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each(function(){w.removeAttr(this,t)})}}),w.extend({attr:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?w.prop(t,e,i):(1===o&&w.isXMLDoc(t)||(s=w.attrHooks[e.toLowerCase()]||(w.expr.match.bool.test(e)?ge:void 0)),void 0!==i?null===i?void w.removeAttr(t,e):s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:(t.setAttribute(e,i+""),i):s&&"get"in s&&null!==(n=s.get(t,e))?n:null==(n=w.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){if(!p.radioValue&&"radio"===e&&k(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}},removeAttr:function(t,e){var i,n=0,s=e&&e.match(U);if(s&&1===t.nodeType)for(;i=s[n++];)t.removeAttribute(i)}}),ge={set:function(t,e,i){return!1===e?w.removeAttr(t,i):t.setAttribute(i,i),i}},w.each(w.expr.match.bool.source.match(/\w+/g),function(t,e){var i=ve[e]||w.find.attr;ve[e]=function(t,e,n){var s,o,r=e.toLowerCase();return n||(o=ve[r],ve[r]=s,s=null!=i(t,e,n)?r:null,ve[r]=o),s}});var ye=/^(?:input|select|textarea|button)$/i,be=/^(?:a|area)$/i;function _e(t){return(t.match(U)||[]).join(" ")}function we(t){return t.getAttribute&&t.getAttribute("class")||""}function xe(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(U)||[]}w.fn.extend({prop:function(t,e){return K(this,w.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each(function(){delete this[w.propFix[t]||t]})}}),w.extend({prop:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&w.isXMLDoc(t)||(e=w.propFix[e]||e,s=w.propHooks[e]),void 0!==i?s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:t[e]=i:s&&"get"in s&&null!==(n=s.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=w.find.attr(t,"tabindex");return e?parseInt(e,10):ye.test(t.nodeName)||be.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),p.optSelected||(w.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this}),w.fn.extend({addClass:function(t){var e,i,n,s,o,r;return f(t)?this.each(function(e){w(this).addClass(t.call(this,e,we(this)))}):(e=xe(t)).length?this.each(function(){if(n=we(this),i=1===this.nodeType&&" "+_e(n)+" "){for(o=0;o<e.length;o++)s=e[o],i.indexOf(" "+s+" ")<0&&(i+=s+" ");r=_e(i),n!==r&&this.setAttribute("class",r)}}):this},removeClass:function(t){var e,i,n,s,o,r;return f(t)?this.each(function(e){w(this).removeClass(t.call(this,e,we(this)))}):arguments.length?(e=xe(t)).length?this.each(function(){if(n=we(this),i=1===this.nodeType&&" "+_e(n)+" "){for(o=0;o<e.length;o++)for(s=e[o];i.indexOf(" "+s+" ")>-1;)i=i.replace(" "+s+" "," ");r=_e(i),n!==r&&this.setAttribute("class",r)}}):this:this.attr("class","")},toggleClass:function(t,e){var i,n,s,o,r=typeof t,a="string"===r||Array.isArray(t);return f(t)?this.each(function(i){w(this).toggleClass(t.call(this,i,we(this),e),e)}):"boolean"==typeof e&&a?e?this.addClass(t):this.removeClass(t):(i=xe(t),this.each(function(){if(a)for(o=w(this),s=0;s<i.length;s++)n=i[s],o.hasClass(n)?o.removeClass(n):o.addClass(n);else void 0!==t&&"boolean"!==r||((n=we(this))&&nt.set(this,"__className__",n),this.setAttribute&&this.setAttribute("class",n||!1===t?"":nt.get(this,"__className__")||""))}))},hasClass:function(t){var e,i,n=0;for(e=" "+t+" ";i=this[n++];)if(1===i.nodeType&&(" "+_e(we(i))+" ").indexOf(e)>-1)return!0;return!1}});var ke=/\r/g;w.fn.extend({val:function(t){var e,i,n,s=this[0];return arguments.length?(n=f(t),this.each(function(i){var s;1===this.nodeType&&(null==(s=n?t.call(this,i,w(this).val()):t)?s="":"number"==typeof s?s+="":Array.isArray(s)&&(s=w.map(s,function(t){return null==t?"":t+""})),(e=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,s,"value")||(this.value=s))})):s?(e=w.valHooks[s.type]||w.valHooks[s.nodeName.toLowerCase()])&&"get"in e&&void 0!==(i=e.get(s,"value"))?i:"string"==typeof(i=s.value)?i.replace(ke,""):null==i?"":i:void 0}}),w.extend({valHooks:{option:{get:function(t){var e=w.find.attr(t,"value");return null!=e?e:_e(w.text(t))}},select:{get:function(t){var e,i,n,s=t.options,o=t.selectedIndex,r="select-one"===t.type,a=r?null:[],l=r?o+1:s.length;for(n=o<0?l:r?o:0;n<l;n++)if(((i=s[n]).selected||n===o)&&!i.disabled&&(!i.parentNode.disabled||!k(i.parentNode,"optgroup"))){if(e=w(i).val(),r)return e;a.push(e)}return a},set:function(t,e){for(var i,n,s=t.options,o=w.makeArray(e),r=s.length;r--;)((n=s[r]).selected=w.inArray(w.valHooks.option.get(n),o)>-1)&&(i=!0);return i||(t.selectedIndex=-1),o}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=w.inArray(w(t).val(),e)>-1}},p.checkOn||(w.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var De=t.location,Ce={guid:Date.now()},Se=/\?/;w.parseXML=function(e){var i,n;if(!e||"string"!=typeof e)return null;try{i=(new t.DOMParser).parseFromString(e,"text/xml")}catch(t){}return n=i&&i.getElementsByTagName("parsererror")[0],i&&!n||w.error("Invalid XML: "+(n?w.map(n.childNodes,function(t){return t.textContent}).join("\n"):e)),i};var Te=/^(?:focusinfocus|focusoutblur)$/,Me=function(t){t.stopPropagation()};w.extend(w.event,{trigger:function(e,i,n,s){var o,r,a,l,c,u,d,p,v=[n||g],y=h.call(e,"type")?e.type:e,b=h.call(e,"namespace")?e.namespace.split("."):[];if(r=p=a=n=n||g,3!==n.nodeType&&8!==n.nodeType&&!Te.test(y+w.event.triggered)&&(y.indexOf(".")>-1&&(b=y.split("."),y=b.shift(),b.sort()),c=y.indexOf(":")<0&&"on"+y,(e=e[w.expando]?e:new w.Event(y,"object"==typeof e&&e)).isTrigger=s?2:3,e.namespace=b.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),i=null==i?[e]:w.makeArray(i,[e]),d=w.event.special[y]||{},s||!d.trigger||!1!==d.trigger.apply(n,i))){if(!s&&!d.noBubble&&!m(n)){for(l=d.delegateType||y,Te.test(l+y)||(r=r.parentNode);r;r=r.parentNode)v.push(r),a=r;a===(n.ownerDocument||g)&&v.push(a.defaultView||a.parentWindow||t)}for(o=0;(r=v[o++])&&!e.isPropagationStopped();)p=r,e.type=o>1?l:d.bindType||y,(u=(nt.get(r,"events")||Object.create(null))[e.type]&&nt.get(r,"handle"))&&u.apply(r,i),(u=c&&r[c])&&u.apply&&et(r)&&(e.result=u.apply(r,i),!1===e.result&&e.preventDefault());return e.type=y,s||e.isDefaultPrevented()||d._default&&!1!==d._default.apply(v.pop(),i)||!et(n)||c&&f(n[y])&&!m(n)&&((a=n[c])&&(n[c]=null),w.event.triggered=y,e.isPropagationStopped()&&p.addEventListener(y,Me),n[y](),e.isPropagationStopped()&&p.removeEventListener(y,Me),w.event.triggered=void 0,a&&(n[c]=a)),e.result}},simulate:function(t,e,i){var n=w.extend(new w.Event,i,{type:t,isSimulated:!0});w.event.trigger(n,null,e)}}),w.fn.extend({trigger:function(t,e){return this.each(function(){w.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return w.event.trigger(t,e,i,!0)}});var Ee=/\[\]$/,Ae=/\r?\n/g,Oe=/^(?:submit|button|image|reset|file)$/i,Ne=/^(?:input|select|textarea|keygen)/i;function Le(t,e,i,n){var s;if(Array.isArray(e))w.each(e,function(e,s){i||Ee.test(t)?n(t,s):Le(t+"["+("object"==typeof s&&null!=s?e:"")+"]",s,i,n)});else if(i||"object"!==b(e))n(t,e);else for(s in e)Le(t+"["+s+"]",e[s],i,n)}w.param=function(t,e){var i,n=[],s=function(t,e){var i=f(e)?e():e;n[n.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==i?"":i)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!w.isPlainObject(t))w.each(t,function(){s(this.name,this.value)});else for(i in t)Le(i,t[i],e,s);return n.join("&")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=w.prop(this,"elements");return t?w.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!w(this).is(":disabled")&&Ne.test(this.nodeName)&&!Oe.test(t)&&(this.checked||!wt.test(t))}).map(function(t,e){var i=w(this).val();return null==i?null:Array.isArray(i)?w.map(i,function(t){return{name:e.name,value:t.replace(Ae,"\r\n")}}):{name:e.name,value:i.replace(Ae,"\r\n")}}).get()}});var Pe=/%20/g,je=/#.*$/,$e=/([?&])_=[^&]*/,Ye=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ie=/^(?:GET|HEAD)$/,We=/^\/\//,He={},Re={},Fe="*/".concat("*"),Ue=g.createElement("a");function qe(t){return function(e,i){"string"!=typeof e&&(i=e,e="*");var n,s=0,o=e.toLowerCase().match(U)||[];if(f(i))for(;n=o[s++];)"+"===n[0]?(n=n.slice(1)||"*",(t[n]=t[n]||[]).unshift(i)):(t[n]=t[n]||[]).push(i)}}function ze(t,e,i,n){var s={},o=t===Re;function r(a){var l;return s[a]=!0,w.each(t[a]||[],function(t,a){var c=a(e,i,n);return"string"!=typeof c||o||s[c]?o?!(l=c):void 0:(e.dataTypes.unshift(c),r(c),!1)}),l}return r(e.dataTypes[0])||!s["*"]&&r("*")}function Ve(t,e){var i,n,s=w.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((s[i]?t:n||(n={}))[i]=e[i]);return n&&w.extend(!0,t,n),t}Ue.href=De.href,w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:De.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(De.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Fe,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Ve(Ve(t,w.ajaxSettings),e):Ve(w.ajaxSettings,t)},ajaxPrefilter:qe(He),ajaxTransport:qe(Re),ajax:function(e,i){"object"==typeof e&&(i=e,e=void 0),i=i||{};var n,s,o,r,a,l,c,h,u,d,p=w.ajaxSetup({},i),f=p.context||p,m=p.context&&(f.nodeType||f.jquery)?w(f):w.event,v=w.Deferred(),y=w.Callbacks("once memory"),b=p.statusCode||{},_={},x={},k="canceled",D={readyState:0,getResponseHeader:function(t){var e;if(c){if(!r)for(r={};e=Ye.exec(o);)r[e[1].toLowerCase()+" "]=(r[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=r[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return c?o:null},setRequestHeader:function(t,e){return null==c&&(t=x[t.toLowerCase()]=x[t.toLowerCase()]||t,_[t]=e),this},overrideMimeType:function(t){return null==c&&(p.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)D.always(t[D.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||k;return n&&n.abort(e),C(0,e),this}};if(v.promise(D),p.url=((e||p.url||De.href)+"").replace(We,De.protocol+"//"),p.type=i.method||i.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(U)||[""],null==p.crossDomain){l=g.createElement("a");try{l.href=p.url,l.href=l.href,p.crossDomain=Ue.protocol+"//"+Ue.host!=l.protocol+"//"+l.host}catch(t){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=w.param(p.data,p.traditional)),ze(He,p,i,D),c)return D;for(u in(h=w.event&&p.global)&&0==w.active++&&w.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Ie.test(p.type),s=p.url.replace(je,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Pe,"+")):(d=p.url.slice(s.length),p.data&&(p.processData||"string"==typeof p.data)&&(s+=(Se.test(s)?"&":"?")+p.data,delete p.data),!1===p.cache&&(s=s.replace($e,"$1"),d=(Se.test(s)?"&":"?")+"_="+Ce.guid+++d),p.url=s+d),p.ifModified&&(w.lastModified[s]&&D.setRequestHeader("If-Modified-Since",w.lastModified[s]),w.etag[s]&&D.setRequestHeader("If-None-Match",w.etag[s])),(p.data&&p.hasContent&&!1!==p.contentType||i.contentType)&&D.setRequestHeader("Content-Type",p.contentType),D.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Fe+"; q=0.01":""):p.accepts["*"]),p.headers)D.setRequestHeader(u,p.headers[u]);if(p.beforeSend&&(!1===p.beforeSend.call(f,D,p)||c))return D.abort();if(k="abort",y.add(p.complete),D.done(p.success),D.fail(p.error),n=ze(Re,p,i,D)){if(D.readyState=1,h&&m.trigger("ajaxSend",[D,p]),c)return D;p.async&&p.timeout>0&&(a=t.setTimeout(function(){D.abort("timeout")},p.timeout));try{c=!1,n.send(_,C)}catch(t){if(c)throw t;C(-1,t)}}else C(-1,"No Transport");function C(e,i,r,l){var u,d,g,_,x,k=i;c||(c=!0,a&&t.clearTimeout(a),n=void 0,o=l||"",D.readyState=e>0?4:0,u=e>=200&&e<300||304===e,r&&(_=function(t,e,i){for(var n,s,o,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(s in a)if(a[s]&&a[s].test(n)){l.unshift(s);break}if(l[0]in i)o=l[0];else{for(s in i){if(!l[0]||t.converters[s+" "+l[0]]){o=s;break}r||(r=s)}o=o||r}if(o)return o!==l[0]&&l.unshift(o),i[o]}(p,D,r)),!u&&w.inArray("script",p.dataTypes)>-1&&w.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),_=function(t,e,i,n){var s,o,r,a,l,c={},h=t.dataTypes.slice();if(h[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(o=h.shift();o;)if(t.responseFields[o]&&(i[t.responseFields[o]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=h.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(r=c[l+" "+o]||c["* "+o]))for(s in c)if((a=s.split(" "))[1]===o&&(r=c[l+" "+a[0]]||c["* "+a[0]])){!0===r?r=c[s]:!0!==c[s]&&(o=a[0],h.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(p,_,D,u),u?(p.ifModified&&((x=D.getResponseHeader("Last-Modified"))&&(w.lastModified[s]=x),(x=D.getResponseHeader("etag"))&&(w.etag[s]=x)),204===e||"HEAD"===p.type?k="nocontent":304===e?k="notmodified":(k=_.state,d=_.data,u=!(g=_.error))):(g=k,!e&&k||(k="error",e<0&&(e=0))),D.status=e,D.statusText=(i||k)+"",u?v.resolveWith(f,[d,k,D]):v.rejectWith(f,[D,k,g]),D.statusCode(b),b=void 0,h&&m.trigger(u?"ajaxSuccess":"ajaxError",[D,p,u?d:g]),y.fireWith(f,[D,k]),h&&(m.trigger("ajaxComplete",[D,p]),--w.active||w.event.trigger("ajaxStop")))}return D},getJSON:function(t,e,i){return w.get(t,e,i,"json")},getScript:function(t,e){return w.get(t,void 0,e,"script")}}),w.each(["get","post"],function(t,e){w[e]=function(t,i,n,s){return f(i)&&(s=s||n,n=i,i=void 0),w.ajax(w.extend({url:t,type:e,dataType:s,data:i,success:n},w.isPlainObject(t)&&t))}}),w.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),w._evalUrl=function(t,e,i){return w.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){w.globalEval(t,e,i)}})},w.fn.extend({wrapAll:function(t){var e;return this[0]&&(f(t)&&(t=t.call(this[0])),e=w(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return f(t)?this.each(function(e){w(this).wrapInner(t.call(this,e))}):this.each(function(){var e=w(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)})},wrap:function(t){var e=f(t);return this.each(function(i){w(this).wrapAll(e?t.call(this,i):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){w(this).replaceWith(this.childNodes)}),this}}),w.expr.pseudos.hidden=function(t){return!w.expr.pseudos.visible(t)},w.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},w.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Be={0:200,1223:204},Ge=w.ajaxSettings.xhr();p.cors=!!Ge&&"withCredentials"in Ge,p.ajax=Ge=!!Ge,w.ajaxTransport(function(e){var i,n;if(p.cors||Ge&&!e.crossDomain)return{send:function(s,o){var r,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)a[r]=e.xhrFields[r];for(r in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||s["X-Requested-With"]||(s["X-Requested-With"]="XMLHttpRequest"),s)a.setRequestHeader(r,s[r]);i=function(t){return function(){i&&(i=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Be[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=i(),n=a.onerror=a.ontimeout=i("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&t.setTimeout(function(){i&&n()})},i=i("abort");try{a.send(e.hasContent&&e.data||null)}catch(t){if(i)throw t}},abort:function(){i&&i()}}}),w.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return w.globalEval(t),t}}}),w.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),w.ajaxTransport("script",function(t){var e,i;if(t.crossDomain||t.scriptAttrs)return{send:function(n,s){e=w("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",i=function(t){e.remove(),i=null,t&&s("error"===t.type?404:200,t.type)}),g.head.appendChild(e[0])},abort:function(){i&&i()}}});var Xe,Ke=[],Ze=/(=)\?(?=&|$)|\?\?/;w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Ke.pop()||w.expando+"_"+Ce.guid++;return this[t]=!0,t}}),w.ajaxPrefilter("json jsonp",function(e,i,n){var s,o,r,a=!1!==e.jsonp&&(Ze.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ze.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return s=e.jsonpCallback=f(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Ze,"$1"+s):!1!==e.jsonp&&(e.url+=(Se.test(e.url)?"&":"?")+e.jsonp+"="+s),e.converters["script json"]=function(){return r||w.error(s+" was not called"),r[0]},e.dataTypes[0]="json",o=t[s],t[s]=function(){r=arguments},n.always(function(){void 0===o?w(t).removeProp(s):t[s]=o,e[s]&&(e.jsonpCallback=i.jsonpCallback,Ke.push(s)),r&&f(o)&&o(r[0]),r=o=void 0}),"script"}),p.createHTMLDocument=((Xe=g.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Xe.childNodes.length),w.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(p.createHTMLDocument?((n=(e=g.implementation.createHTMLDocument("")).createElement("base")).href=g.location.href,e.head.appendChild(n)):e=g),o=!i&&[],(s=$.exec(t))?[e.createElement(s[1])]:(s=Mt([t],e,o),o&&o.length&&w(o).remove(),w.merge([],s.childNodes)));var n,s,o},w.fn.load=function(t,e,i){var n,s,o,r=this,a=t.indexOf(" ");return a>-1&&(n=_e(t.slice(a)),t=t.slice(0,a)),f(e)?(i=e,e=void 0):e&&"object"==typeof e&&(s="POST"),r.length>0&&w.ajax({url:t,type:s||"GET",dataType:"html",data:e}).done(function(t){o=arguments,r.html(n?w("<div>").append(w.parseHTML(t)).find(n):t)}).always(i&&function(t,e){r.each(function(){i.apply(this,o||[t.responseText,e,t])})}),this},w.expr.pseudos.animated=function(t){return w.grep(w.timers,function(e){return t===e.elem}).length},w.offset={setOffset:function(t,e,i){var n,s,o,r,a,l,c=w.css(t,"position"),h=w(t),u={};"static"===c&&(t.style.position="relative"),a=h.offset(),o=w.css(t,"top"),l=w.css(t,"left"),("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1?(r=(n=h.position()).top,s=n.left):(r=parseFloat(o)||0,s=parseFloat(l)||0),f(e)&&(e=e.call(t,i,w.extend({},a))),null!=e.top&&(u.top=e.top-a.top+r),null!=e.left&&(u.left=e.left-a.left+s),"using"in e?e.using.call(t,u):h.css(u)}},w.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){w.offset.setOffset(this,t,e)});var e,i,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),i=n.ownerDocument.defaultView,{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],s={top:0,left:0};if("fixed"===w.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===w.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((s=w(t).offset()).top+=w.css(t,"borderTopWidth",!0),s.left+=w.css(t,"borderLeftWidth",!0))}return{top:e.top-s.top-w.css(n,"marginTop",!0),left:e.left-s.left-w.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===w.css(t,"position");)t=t.offsetParent;return t||ut})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var i="pageYOffset"===e;w.fn[t]=function(n){return K(this,function(t,n,s){var o;if(m(t)?o=t:9===t.nodeType&&(o=t.defaultView),void 0===s)return o?o[e]:t[n];o?o.scrollTo(i?o.pageXOffset:s,i?s:o.pageYOffset):t[n]=s},t,n,arguments.length)}}),w.each(["top","left"],function(t,e){w.cssHooks[e]=Xt(p.pixelPosition,function(t,i){if(i)return i=Gt(t,e),Ut.test(i)?w(t).position()[e]+"px":i})}),w.each({Height:"height",Width:"width"},function(t,e){w.each({padding:"inner"+t,content:e,"":"outer"+t},function(i,n){w.fn[n]=function(s,o){var r=arguments.length&&(i||"boolean"!=typeof s),a=i||(!0===s||!0===o?"margin":"border");return K(this,function(e,i,s){var o;return m(e)?0===n.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===s?w.css(e,i,a):w.style(e,i,s,a)},e,r?s:void 0,r)}})}),w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){w.fn[e]=function(t){return this.on(e,t)}}),w.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),w.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){w.fn[e]=function(t,i){return arguments.length>0?this.on(e,null,t,i):this.trigger(e)}});var Qe=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;w.proxy=function(t,e){var i,n,o;if("string"==typeof e&&(i=t[e],e=t,t=i),f(t))return n=s.call(arguments,2),(o=function(){return t.apply(e||this,n.concat(s.call(arguments)))}).guid=t.guid=t.guid||w.guid++,o},w.holdReady=function(t){t?w.readyWait++:w.ready(!0)},w.isArray=Array.isArray,w.parseJSON=JSON.parse,w.nodeName=k,w.isFunction=f,w.isWindow=m,w.camelCase=tt,w.type=b,w.now=Date.now,w.isNumeric=function(t){var e=w.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},w.trim=function(t){return null==t?"":(t+"").replace(Qe,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return w});var Je=t.jQuery,ti=t.$;return w.noConflict=function(e){return t.$===w&&(t.$=ti),e&&t.jQuery===w&&(t.jQuery=Je),w},void 0===e&&(t.jQuery=t.$=w),w}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap=e()}(this,function(){"use strict";const t=new Map,e={set(e,i,n){t.has(e)||t.set(e,new Map);const s=t.get(e);s.has(i)||0===s.size?s.set(i,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(e,i)=>t.has(e)&&t.get(e).get(i)||null,remove(e,i){if(!t.has(e))return;const n=t.get(e);n.delete(i),0===n.size&&t.delete(e)}},i=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,(t,e)=>`#${CSS.escape(e)}`)),t),n=t=>null==t?`${t}`:Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase(),s=t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t},o=t=>{t.dispatchEvent(new Event("transitionend"))},r=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),a=t=>r(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(i(t)):null,l=t=>{if(!r(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),i=t.closest("details:not([open])");if(!i)return e;if(i!==t){const e=t.closest("summary");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},c=t=>!t||t.nodeType!==Node.ELEMENT_NODE||(!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled"))),h=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?h(t.parentNode):null},u=()=>{},d=t=>{t.offsetHeight},p=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,f=[],m=()=>"rtl"===document.documentElement.dir,g=t=>{(t=>{"loading"===document.readyState?(f.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of f)t()}),f.push(t)):t()})(()=>{const e=p();if(e){const i=t.NAME,n=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=(()=>(e.fn[i]=n,t.jQueryInterface))}})},v=(t,e=[],i=t)=>"function"==typeof t?t(...e):i,y=(t,e,i=!0)=>{if(!i)return void v(t);const n=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const n=Number.parseFloat(e),s=Number.parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(i))):0})(e)+5;let s=!1;const r=({target:i})=>{i===e&&(s=!0,e.removeEventListener("transitionend",r),v(t))};e.addEventListener("transitionend",r),setTimeout(()=>{s||o(e)},n)},b=(t,e,i,n)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!i&&n?t[s-1]:t[0]:(o+=i?1:-1,n&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},_=/[^.]*(?=\..*)\.|.*/,w=/\..*/,x=/::\d+$/,k={};let D=1;const C={mouseenter:"mouseover",mouseleave:"mouseout"},S=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function T(t,e){return e&&`${e}::${D++}`||t.uidEvent||D++}function M(t){const e=T(t);return t.uidEvent=e,k[e]=k[e]||{},k[e]}function E(t,e,i=null){return Object.values(t).find(t=>t.callable===e&&t.delegationSelector===i)}function A(t,e,i){const n="string"==typeof e,s=n?i:e||i;let o=P(t);return S.has(o)||(o=t),[n,s,o]}function O(t,e,i,n,s){if("string"!=typeof e||!t)return;let[o,r,a]=A(e,i,n);if(e in C){r=(t=>(function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)}))(r)}const l=M(t),c=l[a]||(l[a]={}),h=E(c,r,o?i:null);if(h)return void(h.oneOff=h.oneOff&&s);const u=T(r,e.replace(_,"")),d=o?function(t,e,i){return function n(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return $(s,{delegateTarget:r}),n.oneOff&&j.off(t,s.type,e,i),i.apply(r,[s])}}(t,i,r):function(t,e){return function i(n){return $(n,{delegateTarget:t}),i.oneOff&&j.off(t,n.type,e),e.apply(t,[n])}}(t,r);d.delegationSelector=o?i:null,d.callable=r,d.oneOff=s,d.uidEvent=u,c[u]=d,t.addEventListener(a,d,o)}function N(t,e,i,n,s){const o=E(e[i],n,s);o&&(t.removeEventListener(i,o,Boolean(s)),delete e[i][o.uidEvent])}function L(t,e,i,n){const s=e[i]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&N(t,e,i,r.callable,r.delegationSelector)}function P(t){return t=t.replace(w,""),C[t]||t}const j={on(t,e,i,n){O(t,e,i,n,!1)},one(t,e,i,n){O(t,e,i,n,!0)},off(t,e,i,n){if("string"!=typeof e||!t)return;const[s,o,r]=A(e,i,n),a=r!==e,l=M(t),c=l[r]||{},h=e.startsWith(".");if(void 0===o){if(h)for(const i of Object.keys(l))L(t,l,i,e.slice(1));for(const[i,n]of Object.entries(c)){const s=i.replace(x,"");a&&!e.includes(s)||N(t,l,r,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;N(t,l,r,o,s?i:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const n=p();let s=null,o=!0,r=!0,a=!1;e!==P(e)&&n&&(s=n.Event(e,i),n(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const l=$(new Event(e,{bubbles:o,cancelable:!0}),i);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function $(t,e={}){for(const[i,n]of Object.entries(e))try{t[i]=n}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>n})}return t}function Y(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function I(t){return t.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const W={setDataAttribute(t,e,i){t.setAttribute(`data-bs-${I(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${I(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter(t=>t.startsWith("bs")&&!t.startsWith("bsConfig"));for(const n of i){let i=n.replace(/^bs/,"");e[i=i.charAt(0).toLowerCase()+i.slice(1,i.length)]=Y(t.dataset[n])}return e},getDataAttribute:(t,e)=>Y(t.getAttribute(`data-bs-${I(e)}`))};class H{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=r(e)?W.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...r(e)?W.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[i,s]of Object.entries(e)){const e=t[i],o=r(e)?"element":n(e);if(!new RegExp(s).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${o}" but expected type "${s}".`)}}}const R="5.3.3";class F extends H{constructor(t,i){super(),(t=a(t))&&(this._element=t,this._config=this._getConfig(i),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),j.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){y(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return e.get(a(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return R}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const U=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let i=t.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),e=i&&"#"!==i?i.trim():null}return e?e.split(",").map(t=>i(t)).join(","):null},q={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter(t=>t.matches(e)),parents(t,e){const i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(t=>`${t}:not([tabindex^="-"])`).join(",");return this.find(e,t).filter(t=>!c(t)&&l(t))},getSelectorFromElement(t){const e=U(t);return e&&q.findOne(e)?e:null},getElementFromSelector(t){const e=U(t);return e?q.findOne(e):null},getMultipleElementsFromSelector(t){const e=U(t);return e?q.find(e):[]}},z=(t,e="hide")=>{const i=`click.dismiss${t.EVENT_KEY}`,n=t.NAME;j.on(document,i,`[data-bs-dismiss="${n}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),c(this))return;const s=q.getElementFromSelector(this)||this.closest(`.${n}`);t.getOrCreateInstance(s)[e]()})},V="alert",B="close.bs.alert",G="closed.bs.alert",X="fade",K="show";class Z extends F{static get NAME(){return V}close(){if(j.trigger(this._element,B).defaultPrevented)return;this._element.classList.remove(K);const t=this._element.classList.contains(X);this._queueCallback(()=>this._destroyElement(),this._element,t)}_destroyElement(){this._element.remove(),j.trigger(this._element,G),this.dispose()}static jQueryInterface(t){return this.each(function(){const e=Z.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}})}}z(Z,"close"),g(Z);const Q="button",J="active";class tt extends F{static get NAME(){return Q}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(J))}static jQueryInterface(t){return this.each(function(){const e=tt.getOrCreateInstance(this);"toggle"===t&&e[t]()})}}j.on(document,"click.bs.button.data-api",'[data-bs-toggle="button"]',t=>{t.preventDefault();const e=t.target.closest('[data-bs-toggle="button"]');tt.getOrCreateInstance(e).toggle()}),g(tt);const et="swipe",it=".bs.swipe",nt=`touchstart${it}`,st=`touchmove${it}`,ot=`touchend${it}`,rt=`pointerdown${it}`,at=`pointerup${it}`,lt="touch",ct="pen",ht="pointer-event",ut=40,dt={endCallback:null,leftCallback:null,rightCallback:null},pt={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class ft extends H{constructor(t,e){super(),this._element=t,t&&ft.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return dt}static get DefaultType(){return pt}static get NAME(){return et}dispose(){j.off(this._element,it)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),v(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=ut)return;const e=t/this._deltaX;this._deltaX=0,e&&v(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(j.on(this._element,rt,t=>this._start(t)),j.on(this._element,at,t=>this._end(t)),this._element.classList.add(ht)):(j.on(this._element,nt,t=>this._start(t)),j.on(this._element,st,t=>this._move(t)),j.on(this._element,ot,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===ct||t.pointerType===lt)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const mt="carousel",gt=500,vt="next",yt="prev",bt="left",_t="right",wt="slide.bs.carousel",xt="slid.bs.carousel",kt="keydown.bs.carousel",Dt="mouseenter.bs.carousel",Ct="mouseleave.bs.carousel",St="dragstart.bs.carousel",Tt="carousel",Mt="active",Et="slide",At="carousel-item-end",Ot="carousel-item-start",Nt="carousel-item-next",Lt="carousel-item-prev",Pt=".active",jt=".carousel-item",$t=Pt+jt,Yt=".carousel-item img",It=".carousel-indicators",Wt={ArrowLeft:_t,ArrowRight:bt},Ht={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Rt={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Ft extends F{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=q.findOne(It,this._element),this._addEventListeners(),this._config.ride===Tt&&this.cycle()}static get Default(){return Ht}static get DefaultType(){return Rt}static get NAME(){return mt}next(){this._slide(vt)}nextWhenVisible(){!document.hidden&&l(this._element)&&this.next()}prev(){this._slide(yt)}pause(){this._isSliding&&o(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?j.one(this._element,xt,()=>this.cycle()):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void j.one(this._element,xt,()=>this.to(t));const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?vt:yt;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&j.on(this._element,kt,t=>this._keydown(t)),"hover"===this._config.pause&&(j.on(this._element,Dt,()=>this.pause()),j.on(this._element,Ct,()=>this._maybeEnableCycle())),this._config.touch&&ft.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of q.find(Yt,this._element))j.on(t,St,t=>t.preventDefault());const t={leftCallback:()=>this._slide(this._directionToOrder(bt)),rightCallback:()=>this._slide(this._directionToOrder(_t)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),gt+this._config.interval))}};this._swipeHelper=new ft(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=Wt[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=q.findOne(Pt,this._indicatorsElement);e.classList.remove(Mt),e.removeAttribute("aria-current");const i=q.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(Mt),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===vt,s=e||b(this._getItems(),i,n,this._config.wrap);if(s===i)return;const o=this._getItemIndex(s),r=e=>j.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(wt).defaultPrevented)return;if(!i||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=n?Ot:At,c=n?Nt:Lt;s.classList.add(c),d(s),i.classList.add(l),s.classList.add(l);this._queueCallback(()=>{s.classList.remove(l,c),s.classList.add(Mt),i.classList.remove(Mt,c,l),this._isSliding=!1,r(xt)},i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains(Et)}_getActive(){return q.findOne($t,this._element)}_getItems(){return q.find(jt,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return m()?t===bt?yt:vt:t===bt?vt:yt}_orderToDirection(t){return m()?t===yt?bt:_t:t===yt?_t:bt}static jQueryInterface(t){return this.each(function(){const e=Ft.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)})}}j.on(document,"click.bs.carousel.data-api","[data-bs-slide], [data-bs-slide-to]",function(t){const e=q.getElementFromSelector(this);if(!e||!e.classList.contains(Tt))return;t.preventDefault();const i=Ft.getOrCreateInstance(e),n=this.getAttribute("data-bs-slide-to");return n?(i.to(n),void i._maybeEnableCycle()):"next"===W.getDataAttribute(this,"slide")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())}),j.on(window,"load.bs.carousel.data-api",()=>{const t=q.find('[data-bs-ride="carousel"]');for(const e of t)Ft.getOrCreateInstance(e)}),g(Ft);const Ut="collapse",qt="show.bs.collapse",zt="shown.bs.collapse",Vt="hide.bs.collapse",Bt="hidden.bs.collapse",Gt="show",Xt="collapse",Kt="collapsing",Zt="collapsed",Qt=`:scope .${Xt} .${Xt}`,Jt="collapse-horizontal",te="width",ee="height",ie=".collapse.show, .collapse.collapsing",ne='[data-bs-toggle="collapse"]',se={parent:null,toggle:!0},oe={parent:"(null|element)",toggle:"boolean"};class re extends F{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=q.find(ne);for(const t of i){const e=q.getSelectorFromElement(t),i=q.find(e).filter(t=>t===this._element);null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return se}static get DefaultType(){return oe}static get NAME(){return Ut}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(ie).filter(t=>t!==this._element).map(t=>re.getOrCreateInstance(t,{toggle:!1}))),t.length&&t[0]._isTransitioning)return;if(j.trigger(this._element,qt).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(Xt),this._element.classList.add(Kt),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Kt),this._element.classList.add(Xt,Gt),this._element.style[e]="",j.trigger(this._element,zt)},this._element,!0),this._element.style[e]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(j.trigger(this._element,Vt).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,d(this._element),this._element.classList.add(Kt),this._element.classList.remove(Xt,Gt);for(const t of this._triggerArray){const e=q.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0;this._element.style[t]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Kt),this._element.classList.add(Xt),j.trigger(this._element,Bt)},this._element,!0)}_isShown(t=this._element){return t.classList.contains(Gt)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=a(t.parent),t}_getDimension(){return this._element.classList.contains(Jt)?te:ee}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(ne);for(const e of t){const t=q.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=q.find(Qt,this._config.parent);return q.find(t,this._config.parent).filter(t=>!e.includes(t))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle(Zt,!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each(function(){const i=re.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}})}}j.on(document,"click.bs.collapse.data-api",ne,function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of q.getMultipleElementsFromSelector(this))re.getOrCreateInstance(t,{toggle:!1}).toggle()}),g(re);var ae="top",le="bottom",ce="right",he="left",ue="auto",de=[ae,le,ce,he],pe="start",fe="end",me="clippingParents",ge="viewport",ve="popper",ye="reference",be=de.reduce(function(t,e){return t.concat([e+"-"+pe,e+"-"+fe])},[]),_e=[].concat(de,[ue]).reduce(function(t,e){return t.concat([e,e+"-"+pe,e+"-"+fe])},[]),we=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function xe(t){return t?(t.nodeName||"").toLowerCase():null}function ke(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function De(t){return t instanceof ke(t).Element||t instanceof Element}function Ce(t){return t instanceof ke(t).HTMLElement||t instanceof HTMLElement}function Se(t){return"undefined"!=typeof ShadowRoot&&(t instanceof ke(t).ShadowRoot||t instanceof ShadowRoot)}const Te={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach(function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];Ce(s)&&xe(s)&&(Object.assign(s.style,i),Object.keys(n).forEach(function(t){var e=n[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?"":e)}))})},effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach(function(t){var n=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce(function(t,e){return t[e]="",t},{});Ce(n)&&xe(n)&&(Object.assign(n.style,o),Object.keys(s).forEach(function(t){n.removeAttribute(t)}))})}},requires:["computeStyles"]};function Me(t){return t.split("-")[0]}var Ee=Math.max,Ae=Math.min,Oe=Math.round;function Ne(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Le(){return!/^((?!chrome|android).)*safari/i.test(Ne())}function Pe(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),s=1,o=1;e&&Ce(t)&&(s=t.offsetWidth>0&&Oe(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&Oe(n.height)/t.offsetHeight||1);var r=(De(t)?ke(t):window).visualViewport,a=!Le()&&i,l=(n.left+(a&&r?r.offsetLeft:0))/s,c=(n.top+(a&&r?r.offsetTop:0))/o,h=n.width/s,u=n.height/o;return{width:h,height:u,top:c,right:l+h,bottom:c+u,left:l,x:l,y:c}}function je(t){var e=Pe(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function $e(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&Se(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Ye(t){return ke(t).getComputedStyle(t)}function Ie(t){return["table","td","th"].indexOf(xe(t))>=0}function We(t){return((De(t)?t.ownerDocument:t.document)||window.document).documentElement}function He(t){return"html"===xe(t)?t:t.assignedSlot||t.parentNode||(Se(t)?t.host:null)||We(t)}function Re(t){return Ce(t)&&"fixed"!==Ye(t).position?t.offsetParent:null}function Fe(t){for(var e=ke(t),i=Re(t);i&&Ie(i)&&"static"===Ye(i).position;)i=Re(i);return i&&("html"===xe(i)||"body"===xe(i)&&"static"===Ye(i).position)?e:i||function(t){var e=/firefox/i.test(Ne());if(/Trident/i.test(Ne())&&Ce(t)&&"fixed"===Ye(t).position)return null;var i=He(t);for(Se(i)&&(i=i.host);Ce(i)&&["html","body"].indexOf(xe(i))<0;){var n=Ye(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(t)||e}function Ue(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function qe(t,e,i){return Ee(t,Ae(e,i))}function ze(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Ve(t,e){return e.reduce(function(e,i){return e[i]=t,e},{})}var Be=function(t,e){return ze("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Ve(t,de))};const Ge={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i=t.state,n=t.name,s=t.options,o=i.elements.arrow,r=i.modifiersData.popperOffsets,a=Me(i.placement),l=Ue(a),c=[he,ce].indexOf(a)>=0?"height":"width";if(o&&r){var h=Be(s.padding,i),u=je(o),d="y"===l?ae:he,p="y"===l?le:ce,f=i.rects.reference[c]+i.rects.reference[l]-r[l]-i.rects.popper[c],m=r[l]-i.rects.reference[l],g=Fe(o),v=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,y=f/2-m/2,b=h[d],_=v-u[c]-h[p],w=v/2-u[c]/2+y,x=qe(b,w,_),k=l;i.modifiersData[n]=((e={})[k]=x,e.centerOffset=x-w,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&$e(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Xe(t){return t.split("-")[1]}var Ke={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ze(t){var e,i=t.popper,n=t.popperRect,s=t.placement,o=t.variation,r=t.offsets,a=t.position,l=t.gpuAcceleration,c=t.adaptive,h=t.roundOffsets,u=t.isFixed,d=r.x,p=void 0===d?0:d,f=r.y,m=void 0===f?0:f,g="function"==typeof h?h({x:p,y:m}):{x:p,y:m};p=g.x,m=g.y;var v=r.hasOwnProperty("x"),y=r.hasOwnProperty("y"),b=he,_=ae,w=window;if(c){var x=Fe(i),k="clientHeight",D="clientWidth";if(x===ke(i)&&"static"!==Ye(x=We(i)).position&&"absolute"===a&&(k="scrollHeight",D="scrollWidth"),x=x,s===ae||(s===he||s===ce)&&o===fe)_=le,m-=(u&&x===w&&w.visualViewport?w.visualViewport.height:x[k])-n.height,m*=l?1:-1;if(s===he||(s===ae||s===le)&&o===fe)b=ce,p-=(u&&x===w&&w.visualViewport?w.visualViewport.width:x[D])-n.width,p*=l?1:-1}var C,S=Object.assign({position:a},c&&Ke),T=!0===h?function(t,e){var i=t.x,n=t.y,s=e.devicePixelRatio||1;return{x:Oe(i*s)/s||0,y:Oe(n*s)/s||0}}({x:p,y:m},ke(i)):{x:p,y:m};return p=T.x,m=T.y,l?Object.assign({},S,((C={})[_]=y?"0":"",C[b]=v?"0":"",C.transform=(w.devicePixelRatio||1)<=1?"translate("+p+"px, "+m+"px)":"translate3d("+p+"px, "+m+"px, 0)",C)):Object.assign({},S,((e={})[_]=y?m+"px":"",e[b]=v?p+"px":"",e.transform="",e))}const Qe={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:Me(e.placement),variation:Xe(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,Ze(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,Ze(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var Je={passive:!0};const ti={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=t.options,s=n.scroll,o=void 0===s||s,r=n.resize,a=void 0===r||r,l=ke(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach(function(t){t.addEventListener("scroll",i.update,Je)}),a&&l.addEventListener("resize",i.update,Je),function(){o&&c.forEach(function(t){t.removeEventListener("scroll",i.update,Je)}),a&&l.removeEventListener("resize",i.update,Je)}},data:{}};var ei={left:"right",right:"left",bottom:"top",top:"bottom"};function ii(t){return t.replace(/left|right|bottom|top/g,function(t){return ei[t]})}var ni={start:"end",end:"start"};function si(t){return t.replace(/start|end/g,function(t){return ni[t]})}function oi(t){var e=ke(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function ri(t){return Pe(We(t)).left+oi(t).scrollLeft}function ai(t){var e=Ye(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function li(t,e){var i;void 0===e&&(e=[]);var n=function t(e){return["html","body","#document"].indexOf(xe(e))>=0?e.ownerDocument.body:Ce(e)&&ai(e)?e:t(He(e))}(t),s=n===(null==(i=t.ownerDocument)?void 0:i.body),o=ke(n),r=s?[o].concat(o.visualViewport||[],ai(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(li(He(r)))}function ci(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function hi(t,e,i){return e===ge?ci(function(t,e){var i=ke(t),n=We(t),s=i.visualViewport,o=n.clientWidth,r=n.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=Le();(c||!c&&"fixed"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+ri(t),y:l}}(t,i)):De(e)?function(t,e){var i=Pe(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(e,i):ci(function(t){var e,i=We(t),n=oi(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=Ee(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=Ee(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+ri(t),l=-n.scrollTop;return"rtl"===Ye(s||i).direction&&(a+=Ee(i.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}(We(t)))}function ui(t,e,i,n){var s="clippingParents"===e?function(t){var e=li(He(t)),i=["absolute","fixed"].indexOf(Ye(t).position)>=0&&Ce(t)?Fe(t):t;return De(i)?e.filter(function(t){return De(t)&&$e(t,i)&&"body"!==xe(t)}):[]}(t):[].concat(e),o=[].concat(s,[i]),r=o[0],a=o.reduce(function(e,i){var s=hi(t,i,n);return e.top=Ee(s.top,e.top),e.right=Ae(s.right,e.right),e.bottom=Ae(s.bottom,e.bottom),e.left=Ee(s.left,e.left),e},hi(t,r,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function di(t){var e,i=t.reference,n=t.element,s=t.placement,o=s?Me(s):null,r=s?Xe(s):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(o){case ae:e={x:a,y:i.y-n.height};break;case le:e={x:a,y:i.y+i.height};break;case ce:e={x:i.x+i.width,y:l};break;case he:e={x:i.x-n.width,y:l};break;default:e={x:i.x,y:i.y}}var c=o?Ue(o):null;if(null!=c){var h="y"===c?"height":"width";switch(r){case pe:e[c]=e[c]-(i[h]/2-n[h]/2);break;case fe:e[c]=e[c]+(i[h]/2-n[h]/2)}}return e}function pi(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=void 0===n?t.placement:n,o=i.strategy,r=void 0===o?t.strategy:o,a=i.boundary,l=void 0===a?me:a,c=i.rootBoundary,h=void 0===c?ge:c,u=i.elementContext,d=void 0===u?ve:u,p=i.altBoundary,f=void 0!==p&&p,m=i.padding,g=void 0===m?0:m,v=ze("number"!=typeof g?g:Ve(g,de)),y=d===ve?ye:ve,b=t.rects.popper,_=t.elements[f?y:d],w=ui(De(_)?_:_.contextElement||We(t.elements.popper),l,h,r),x=Pe(t.elements.reference),k=di({reference:x,element:b,strategy:"absolute",placement:s}),D=ci(Object.assign({},b,k)),C=d===ve?D:x,S={top:w.top-C.top+v.top,bottom:C.bottom-w.bottom+v.bottom,left:w.left-C.left+v.left,right:C.right-w.right+v.right},T=t.modifiersData.offset;if(d===ve&&T){var M=T[s];Object.keys(S).forEach(function(t){var e=[ce,le].indexOf(t)>=0?1:-1,i=[ae,le].indexOf(t)>=0?"y":"x";S[t]+=M[i]*e})}return S}function fi(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,c=void 0===l?_e:l,h=Xe(n),u=h?a?be:be.filter(function(t){return Xe(t)===h}):de,d=u.filter(function(t){return c.indexOf(t)>=0});0===d.length&&(d=u);var p=d.reduce(function(e,i){return e[i]=pi(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[Me(i)],e},{});return Object.keys(p).sort(function(t,e){return p[t]-p[e]})}const mi={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0===r||r,l=i.fallbackPlacements,c=i.padding,h=i.boundary,u=i.rootBoundary,d=i.altBoundary,p=i.flipVariations,f=void 0===p||p,m=i.allowedAutoPlacements,g=e.options.placement,v=Me(g),y=l||(v!==g&&f?function(t){if(Me(t)===ue)return[];var e=ii(t);return[si(t),e,si(e)]}(g):[ii(g)]),b=[g].concat(y).reduce(function(t,i){return t.concat(Me(i)===ue?fi(e,{placement:i,boundary:h,rootBoundary:u,padding:c,flipVariations:f,allowedAutoPlacements:m}):i)},[]),_=e.rects.reference,w=e.rects.popper,x=new Map,k=!0,D=b[0],C=0;C<b.length;C++){var S=b[C],T=Me(S),M=Xe(S)===pe,E=[ae,le].indexOf(T)>=0,A=E?"width":"height",O=pi(e,{placement:S,boundary:h,rootBoundary:u,altBoundary:d,padding:c}),N=E?M?ce:he:M?le:ae;_[A]>w[A]&&(N=ii(N));var L=ii(N),P=[];if(o&&P.push(O[T]<=0),a&&P.push(O[N]<=0,O[L]<=0),P.every(function(t){return t})){D=S,k=!1;break}x.set(S,P)}if(k)for(var j=function(t){var e=b.find(function(e){var i=x.get(e);if(i)return i.slice(0,t).every(function(t){return t})});if(e)return D=e,"break"},$=f?3:1;$>0&&"break"!==j($);$--);e.placement!==D&&(e.modifiersData[n]._skip=!0,e.placement=D,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function gi(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function vi(t){return[ae,ce,le,he].some(function(e){return t[e]>=0})}const yi={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=pi(e,{elementContext:"reference"}),a=pi(e,{altBoundary:!0}),l=gi(r,n),c=gi(a,s,o),h=vi(l),u=vi(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:h,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":h,"data-popper-escaped":u})}};const bi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.offset,o=void 0===s?[0,0]:s,r=_e.reduce(function(t,i){return t[i]=function(t,e,i){var n=Me(t),s=[he,ae].indexOf(n)>=0?-1:1,o="function"==typeof i?i(Object.assign({},e,{placement:t})):i,r=o[0],a=o[1];return r=r||0,a=(a||0)*s,[he,ce].indexOf(n)>=0?{x:a,y:r}:{x:r,y:a}}(i,e.rects,o),t},{}),a=r[e.placement],l=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=r}};const _i={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=di({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}};const wi={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0!==r&&r,l=i.boundary,c=i.rootBoundary,h=i.altBoundary,u=i.padding,d=i.tether,p=void 0===d||d,f=i.tetherOffset,m=void 0===f?0:f,g=pi(e,{boundary:l,rootBoundary:c,padding:u,altBoundary:h}),v=Me(e.placement),y=Xe(e.placement),b=!y,_=Ue(v),w="x"===_?"y":"x",x=e.modifiersData.popperOffsets,k=e.rects.reference,D=e.rects.popper,C="function"==typeof m?m(Object.assign({},e.rects,{placement:e.placement})):m,S="number"==typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),T=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,M={x:0,y:0};if(x){if(o){var E,A="y"===_?ae:he,O="y"===_?le:ce,N="y"===_?"height":"width",L=x[_],P=L+g[A],j=L-g[O],$=p?-D[N]/2:0,Y=y===pe?k[N]:D[N],I=y===pe?-D[N]:-k[N],W=e.elements.arrow,H=p&&W?je(W):{width:0,height:0},R=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},F=R[A],U=R[O],q=qe(0,k[N],H[N]),z=b?k[N]/2-$-q-F-S.mainAxis:Y-q-F-S.mainAxis,V=b?-k[N]/2+$+q+U+S.mainAxis:I+q+U+S.mainAxis,B=e.elements.arrow&&Fe(e.elements.arrow),G=B?"y"===_?B.clientTop||0:B.clientLeft||0:0,X=null!=(E=null==T?void 0:T[_])?E:0,K=L+V-X,Z=qe(p?Ae(P,L+z-X-G):P,L,p?Ee(j,K):j);x[_]=Z,M[_]=Z-L}if(a){var Q,J="x"===_?ae:he,tt="x"===_?le:ce,et=x[w],it="y"===w?"height":"width",nt=et+g[J],st=et-g[tt],ot=-1!==[ae,he].indexOf(v),rt=null!=(Q=null==T?void 0:T[w])?Q:0,at=ot?nt:et-k[it]-D[it]-rt+S.altAxis,lt=ot?et+k[it]+D[it]-rt-S.altAxis:st,ct=p&&ot?function(t,e,i){var n=qe(t,e,i);return n>i?i:n}(at,et,lt):qe(p?at:nt,et,p?lt:st);x[w]=ct,M[w]=ct-et}e.modifiersData[n]=M}},requiresIfExists:["offset"]};function xi(t,e,i){void 0===i&&(i=!1);var n,s,o=Ce(e),r=Ce(e)&&function(t){var e=t.getBoundingClientRect(),i=Oe(e.width)/t.offsetWidth||1,n=Oe(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(e),a=We(e),l=Pe(t,r,i),c={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(o||!o&&!i)&&(("body"!==xe(e)||ai(a))&&(c=(n=e)!==ke(n)&&Ce(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:oi(n)),Ce(e)?((h=Pe(e,!0)).x+=e.clientLeft,h.y+=e.clientTop):a&&(h.x=ri(a))),{x:l.left+c.scrollLeft-h.x,y:l.top+c.scrollTop-h.y,width:l.width,height:l.height}}function ki(t){var e=new Map,i=new Set,n=[];return t.forEach(function(t){e.set(t.name,t)}),t.forEach(function(t){i.has(t.name)||function t(s){i.add(s.name),[].concat(s.requires||[],s.requiresIfExists||[]).forEach(function(n){if(!i.has(n)){var s=e.get(n);s&&t(s)}}),n.push(s)}(t)}),n}var Di={placement:"bottom",modifiers:[],strategy:"absolute"};function Ci(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some(function(t){return!(t&&"function"==typeof t.getBoundingClientRect)})}function Si(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,s=e.defaultOptions,o=void 0===s?Di:s;return function(t,e,i){void 0===i&&(i=o);var s,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Di,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,h={state:a,setOptions:function(i){var s="function"==typeof i?i(a.options):i;u(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:De(t)?li(t):t.contextElement?li(t.contextElement):[],popper:li(e)};var r,c,d=function(t){var e=ki(t);return we.reduce(function(t,i){return t.concat(e.filter(function(t){return t.phase===i}))},[])}((r=[].concat(n,a.options.modifiers),c=r.reduce(function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t},{}),Object.keys(c).map(function(t){return c[t]})));return a.orderedModifiers=d.filter(function(t){return t.enabled}),a.orderedModifiers.forEach(function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,s=t.effect;if("function"==typeof s){var o=s({state:a,name:e,instance:h,options:n});l.push(o||function(){})}}),h.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,i=t.popper;if(Ci(e,i)){a.rects={reference:xi(e,Fe(i),"fixed"===a.options.strategy),popper:je(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(t){return a.modifiersData[t.name]=Object.assign({},t.data)});for(var n=0;n<a.orderedModifiers.length;n++)if(!0!==a.reset){var s=a.orderedModifiers[n],o=s.fn,r=s.options,l=void 0===r?{}:r,u=s.name;"function"==typeof o&&(a=o({state:a,options:l,name:u,instance:h})||a)}else a.reset=!1,n=-1}}},update:(s=function(){return new Promise(function(t){h.forceUpdate(),t(a)})},function(){return r||(r=new Promise(function(t){Promise.resolve().then(function(){r=void 0,t(s())})})),r}),destroy:function(){u(),c=!0}};if(!Ci(t,e))return h;function u(){l.forEach(function(t){return t()}),l=[]}return h.setOptions(i).then(function(t){!c&&i.onFirstUpdate&&i.onFirstUpdate(t)}),h}}var Ti=Si(),Mi=Si({defaultModifiers:[ti,_i,Qe,Te]}),Ei=Si({defaultModifiers:[ti,_i,Qe,Te,bi,mi,wi,Ge,yi]});const Ai=Object.freeze(Object.defineProperty({__proto__:null,afterMain:"afterMain",afterRead:"afterRead",afterWrite:"afterWrite",applyStyles:Te,arrow:Ge,auto:ue,basePlacements:de,beforeMain:"beforeMain",beforeRead:"beforeRead",beforeWrite:"beforeWrite",bottom:le,clippingParents:me,computeStyles:Qe,createPopper:Ei,createPopperBase:Ti,createPopperLite:Mi,detectOverflow:pi,end:fe,eventListeners:ti,flip:mi,hide:yi,left:he,main:"main",modifierPhases:we,offset:bi,placements:_e,popper:ve,popperGenerator:Si,popperOffsets:_i,preventOverflow:wi,read:"read",reference:ye,right:ce,start:pe,top:ae,variationPlacements:be,viewport:ge,write:"write"},Symbol.toStringTag,{value:"Module"})),Oi="dropdown",Ni="Escape",Li="Tab",Pi="ArrowUp",ji="ArrowDown",$i=2,Yi="hide.bs.dropdown",Ii="hidden.bs.dropdown",Wi="show.bs.dropdown",Hi="shown.bs.dropdown",Ri="show",Fi="dropup",Ui="dropend",qi="dropstart",zi="dropup-center",Vi="dropdown-center",Bi='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Gi=`${Bi}.${Ri}`,Xi=".dropdown-menu",Ki=".navbar",Zi=".navbar-nav",Qi=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",Ji=m()?"top-end":"top-start",tn=m()?"top-start":"top-end",en=m()?"bottom-end":"bottom-start",nn=m()?"bottom-start":"bottom-end",sn=m()?"left-start":"right-start",on=m()?"right-start":"left-start",rn="top",an="bottom",ln={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},cn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class hn extends F{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=q.next(this._element,Xi)[0]||q.prev(this._element,Xi)[0]||q.findOne(Xi,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return ln}static get DefaultType(){return cn}static get NAME(){return Oi}toggle(){return this._isShown()?this.hide():this.show()}show(){if(c(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!j.trigger(this._element,Wi,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(Zi))for(const t of[].concat(...document.body.children))j.on(t,"mouseover",u);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Ri),this._element.classList.add(Ri),j.trigger(this._element,Hi,t)}}hide(){if(c(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!j.trigger(this._element,Yi,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))j.off(t,"mouseover",u);this._popper&&this._popper.destroy(),this._menu.classList.remove(Ri),this._element.classList.remove(Ri),this._element.setAttribute("aria-expanded","false"),W.removeDataAttribute(this._menu,"popper"),j.trigger(this._element,Ii,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!r(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${Oi.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===Ai)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;"parent"===this._config.reference?t=this._parent:r(this._config.reference)?t=a(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=Ei(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Ri)}_getPlacement(){const t=this._parent;if(t.classList.contains(Ui))return sn;if(t.classList.contains(qi))return on;if(t.classList.contains(zi))return rn;if(t.classList.contains(Vi))return an;const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains(Fi)?e?tn:Ji:e?nn:en}_detectNavbar(){return null!==this._element.closest(Ki)}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(W.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...v(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const i=q.find(Qi,this._menu).filter(t=>l(t));i.length&&b(i,e,t===ji,!i.includes(e)).focus()}static jQueryInterface(t){return this.each(function(){const e=hn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}static clearMenus(t){if(t.button===$i||"keyup"===t.type&&t.key!==Li)return;const e=q.find(Gi);for(const i of e){const e=hn.getInstance(i);if(!e||!1===e._config.autoClose)continue;const n=t.composedPath(),s=n.includes(e._menu);if(n.includes(e._element)||"inside"===e._config.autoClose&&!s||"outside"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&t.key===Li||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i=t.key===Ni,n=[Pi,ji].includes(t.key);if(!n&&!i)return;if(e&&!i)return;t.preventDefault();const s=this.matches(Bi)?this:q.prev(this,Bi)[0]||q.next(this,Bi)[0]||q.findOne(Bi,t.delegateTarget.parentNode),o=hn.getOrCreateInstance(s);if(n)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}j.on(document,"keydown.bs.dropdown.data-api",Bi,hn.dataApiKeydownHandler),j.on(document,"keydown.bs.dropdown.data-api",Xi,hn.dataApiKeydownHandler),j.on(document,"click.bs.dropdown.data-api",hn.clearMenus),j.on(document,"keyup.bs.dropdown.data-api",hn.clearMenus),j.on(document,"click.bs.dropdown.data-api",Bi,function(t){t.preventDefault(),hn.getOrCreateInstance(this).toggle()}),g(hn);const un="backdrop",dn="fade",pn="show",fn=`mousedown.bs.${un}`,mn={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},gn={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class vn extends H{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return mn}static get DefaultType(){return gn}static get NAME(){return un}show(t){if(!this._config.isVisible)return void v(t);this._append();const e=this._getElement();this._config.isAnimated&&d(e),e.classList.add(pn),this._emulateAnimation(()=>{v(t)})}hide(t){this._config.isVisible?(this._getElement().classList.remove(pn),this._emulateAnimation(()=>{this.dispose(),v(t)})):v(t)}dispose(){this._isAppended&&(j.off(this._element,fn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(dn),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=a(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),j.on(t,fn,()=>{v(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){y(t,this._getElement(),this._config.isAnimated)}}const yn="focustrap",bn=".bs.focustrap",_n=`focusin${bn}`,wn=`keydown.tab${bn}`,xn="Tab",kn="forward",Dn="backward",Cn={autofocus:!0,trapElement:null},Sn={autofocus:"boolean",trapElement:"element"};class Tn extends H{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Cn}static get DefaultType(){return Sn}static get NAME(){return yn}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),j.off(document,bn),j.on(document,_n,t=>this._handleFocusin(t)),j.on(document,wn,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,j.off(document,bn))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=q.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===Dn?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){t.key===xn&&(this._lastTabNavDirection=t.shiftKey?Dn:kn)}}const Mn=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",En=".sticky-top",An="padding-right",On="margin-right";class Nn{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,An,e=>e+t),this._setElementAttributes(Mn,An,e=>e+t),this._setElementAttributes(En,On,e=>e-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,An),this._resetElementAttributes(Mn,An),this._resetElementAttributes(En,On)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth();this._applyManipulationCallback(t,t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+n)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(s))}px`)})}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&W.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,t=>{const i=W.getDataAttribute(t,e);null!==i?(W.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)})}_applyManipulationCallback(t,e){if(r(t))e(t);else for(const i of q.find(t,this._element))e(i)}}const Ln="modal",Pn=".bs.modal",jn="Escape",$n=`hide${Pn}`,Yn=`hidePrevented${Pn}`,In=`hidden${Pn}`,Wn=`show${Pn}`,Hn=`shown${Pn}`,Rn=`resize${Pn}`,Fn=`click.dismiss${Pn}`,Un=`mousedown.dismiss${Pn}`,qn=`keydown.dismiss${Pn}`,zn=`click${Pn}.data-api`,Vn="modal-open",Bn="fade",Gn="show",Xn="modal-static",Kn=".modal-dialog",Zn=".modal-body",Qn={backdrop:!0,focus:!0,keyboard:!0},Jn={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class ts extends F{constructor(t,e){super(t,e),this._dialog=q.findOne(Kn,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Nn,this._addEventListeners()}static get Default(){return Qn}static get DefaultType(){return Jn}static get NAME(){return Ln}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||this._isTransitioning)return;j.trigger(this._element,Wn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Vn),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){if(!this._isShown||this._isTransitioning)return;j.trigger(this._element,$n).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Gn),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){j.off(window,Pn),j.off(this._dialog,Pn),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new vn({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Tn({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=q.findOne(Zn,this._dialog);e&&(e.scrollTop=0),d(this._element),this._element.classList.add(Gn);this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,j.trigger(this._element,Hn,{relatedTarget:t})},this._dialog,this._isAnimated())}_addEventListeners(){j.on(this._element,qn,t=>{t.key===jn&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),j.on(window,Rn,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),j.on(this._element,Un,t=>{j.one(this._element,Fn,e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Vn),this._resetAdjustments(),this._scrollBar.reset(),j.trigger(this._element,In)})}_isAnimated(){return this._element.classList.contains(Bn)}_triggerBackdropTransition(){if(j.trigger(this._element,Yn).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(Xn)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Xn),this._queueCallback(()=>{this._element.classList.remove(Xn),this._queueCallback(()=>{this._element.style.overflowY=e},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=m()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!i&&t){const t=m()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each(function(){const i=ts.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t](e)}})}}j.on(document,zn,'[data-bs-toggle="modal"]',function(t){const e=q.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),j.one(e,Wn,t=>{t.defaultPrevented||j.one(e,In,()=>{l(this)&&this.focus()})});const i=q.findOne(".modal.show");i&&ts.getInstance(i).hide(),ts.getOrCreateInstance(e).toggle(this)}),z(ts),g(ts);const es="offcanvas",is="Escape",ns="show",ss="showing",os="hiding",rs="offcanvas-backdrop",as="show.bs.offcanvas",ls="shown.bs.offcanvas",cs="hide.bs.offcanvas",hs="hidePrevented.bs.offcanvas",us="hidden.bs.offcanvas",ds="keydown.dismiss.bs.offcanvas",ps={backdrop:!0,keyboard:!0,scroll:!1},fs={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class ms extends F{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return ps}static get DefaultType(){return fs}static get NAME(){return es}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown)return;if(j.trigger(this._element,as,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Nn).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(ss);this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(ns),this._element.classList.remove(ss),j.trigger(this._element,ls,{relatedTarget:t})},this._element,!0)}hide(){if(!this._isShown)return;if(j.trigger(this._element,cs).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(os),this._backdrop.hide();this._queueCallback(()=>{this._element.classList.remove(ns,os),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new Nn).reset(),j.trigger(this._element,us)},this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new vn({className:rs,isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():j.trigger(this._element,hs)}:null})}_initializeFocusTrap(){return new Tn({trapElement:this._element})}_addEventListeners(){j.on(this._element,ds,t=>{t.key===is&&(this._config.keyboard?this.hide():j.trigger(this._element,hs))})}static jQueryInterface(t){return this.each(function(){const e=ms.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}})}}j.on(document,"click.bs.offcanvas.data-api",'[data-bs-toggle="offcanvas"]',function(t){const e=q.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),c(this))return;j.one(e,us,()=>{l(this)&&this.focus()});const i=q.findOne(".offcanvas.show");i&&i!==e&&ms.getInstance(i).hide(),ms.getOrCreateInstance(e).toggle(this)}),j.on(window,"load.bs.offcanvas.data-api",()=>{for(const t of q.find(".offcanvas.show"))ms.getOrCreateInstance(t).show()}),j.on(window,"resize.bs.offcanvas",()=>{for(const t of q.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&ms.getOrCreateInstance(t).hide()}),z(ms),g(ms);const gs={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},vs=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),ys=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,bs=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!vs.has(i)||Boolean(ys.test(t.nodeValue)):e.filter(t=>t instanceof RegExp).some(t=>t.test(i))};const _s="TemplateFactory",ws={allowList:gs,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},xs={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},ks={entry:"(string|element|function|null)",selector:"(string|element)"};class Ds extends H{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return ws}static get DefaultType(){return xs}static get NAME(){return _s}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},ks)}_setContent(t,e,i){const n=q.findOne(i,t);n&&((e=this._resolvePossibleFunction(e))?r(e)?this._putElementInTemplate(a(e),n):this._config.html?n.innerHTML=this._maybeSanitize(e):n.textContent=e:n.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);const n=(new window.DOMParser).parseFromString(t,"text/html"),s=[].concat(...n.body.querySelectorAll("*"));for(const t of s){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const n=[].concat(...t.attributes),s=[].concat(e["*"]||[],e[i]||[]);for(const e of n)bs(e,s)||t.removeAttribute(e.nodeName)}return n.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return v(t,[this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const Cs="tooltip",Ss=new Set(["sanitize","allowList","sanitizeFn"]),Ts="fade",Ms="show",Es=".tooltip-inner",As=".modal",Os="hide.bs.modal",Ns="hover",Ls="focus",Ps="click",js="manual",$s="hide",Ys="hidden",Is="show",Ws="shown",Hs="inserted",Rs="click",Fs="focusin",Us="focusout",qs="mouseenter",zs="mouseleave",Vs={AUTO:"auto",TOP:"top",RIGHT:m()?"left":"right",BOTTOM:"bottom",LEFT:m()?"right":"left"},Bs={allowList:gs,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Gs={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Xs extends F{constructor(t,e){if(void 0===Ai)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Bs}static get DefaultType(){return Gs}static get NAME(){return Cs}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),j.off(this._element.closest(As),Os,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=j.trigger(this._element,this.constructor.eventName(Is)),e=(h(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),j.trigger(this._element,this.constructor.eventName(Hs))),this._popper=this._createPopper(i),i.classList.add(Ms),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))j.on(t,"mouseover",u);this._queueCallback(()=>{j.trigger(this._element,this.constructor.eventName(Ws)),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(j.trigger(this._element,this.constructor.eventName($s)).defaultPrevented)return;if(this._getTipElement().classList.remove(Ms),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))j.off(t,"mouseover",u);this._activeTrigger[Ps]=!1,this._activeTrigger[Ls]=!1,this._activeTrigger[Ns]=!1,this._isHovered=null;this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),j.trigger(this._element,this.constructor.eventName(Ys)))},this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(Ts,Ms),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=s(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(Ts),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Ds({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Es]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Ts)}_isShown(){return this.tip&&this.tip.classList.contains(Ms)}_createPopper(t){const e=v(this._config.placement,[this,t,this._element]),i=Vs[e.toUpperCase()];return Ei(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return v(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...v(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)j.on(this._element,this.constructor.eventName(Rs),this._config.selector,t=>{this._initializeOnDelegatedTarget(t).toggle()});else if(e!==js){const t=e===Ns?this.constructor.eventName(qs):this.constructor.eventName(Fs),i=e===Ns?this.constructor.eventName(zs):this.constructor.eventName(Us);j.on(this._element,t,this._config.selector,t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?Ls:Ns]=!0,e._enter()}),j.on(this._element,i,this._config.selector,t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?Ls:Ns]=e._element.contains(t.relatedTarget),e._leave()})}this._hideModalHandler=(()=>{this._element&&this.hide()}),j.on(this._element.closest(As),Os,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=W.getDataAttributes(this._element);for(const t of Object.keys(e))Ss.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:a(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const e=Xs.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}g(Xs);const Ks="popover",Zs=".popover-header",Qs=".popover-body",Js={...Xs.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},to={...Xs.DefaultType,content:"(null|string|element|function)"};class eo extends Xs{static get Default(){return Js}static get DefaultType(){return to}static get NAME(){return Ks}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Zs]:this._getTitle(),[Qs]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const e=eo.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}g(eo);const io="scrollspy",no="activate.bs.scrollspy",so="click.bs.scrollspy",oo="dropdown-item",ro="active",ao="[href]",lo=".nav, .list-group",co=".nav-link, .nav-item > .nav-link, .list-group-item",ho=".dropdown",uo=".dropdown-toggle",po={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},fo={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class mo extends F{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return po}static get DefaultType(){return fo}static get NAME(){return io}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=a(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map(t=>Number.parseFloat(t))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(j.off(this._config.target,so),j.on(this._config.target,so,ao,t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:"smooth"});i.scrollTop=n}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(t=>this._observerCallback(t),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(i(o),!n)return}else s||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=q.find(ao,this._config.target);for(const e of t){if(!e.hash||c(e))continue;const t=q.findOne(decodeURI(e.hash),this._element);l(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(ro),this._activateParents(t),j.trigger(this._element,no,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(oo))q.findOne(uo,t.closest(ho)).classList.add(ro);else for(const e of q.parents(t,lo))for(const t of q.prev(e,co))t.classList.add(ro)}_clearActiveClass(t){t.classList.remove(ro);const e=q.find(`${ao}.${ro}`,t);for(const t of e)t.classList.remove(ro)}static jQueryInterface(t){return this.each(function(){const e=mo.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}})}}j.on(window,"load.bs.scrollspy.data-api",()=>{for(const t of q.find('[data-bs-spy="scroll"]'))mo.getOrCreateInstance(t)}),g(mo);const go="tab",vo="hide.bs.tab",yo="hidden.bs.tab",bo="show.bs.tab",_o="shown.bs.tab",wo="keydown.bs.tab",xo="ArrowLeft",ko="ArrowRight",Do="ArrowUp",Co="ArrowDown",So="Home",To="End",Mo="active",Eo="fade",Ao="show",Oo="dropdown",No=".dropdown-toggle",Lo=".dropdown-menu",Po=`:not(${No})`,jo='.list-group, .nav, [role="tablist"]',$o=".nav-item, .list-group-item",Yo='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Io=`${`.nav-link${Po}, .list-group-item${Po}, [role="tab"]${Po}`}, ${Yo}`,Wo=`.${Mo}[data-bs-toggle="tab"], .${Mo}[data-bs-toggle="pill"], .${Mo}[data-bs-toggle="list"]`;class Ho extends F{constructor(t){super(t),this._parent=this._element.closest(jo),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),j.on(this._element,wo,t=>this._keydown(t)))}static get NAME(){return go}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?j.trigger(e,vo,{relatedTarget:t}):null;j.trigger(t,bo,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(Mo),this._activate(q.getElementFromSelector(t));this._queueCallback(()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),j.trigger(t,_o,{relatedTarget:e})):t.classList.add(Ao)},t,t.classList.contains(Eo))}_deactivate(t,e){if(!t)return;t.classList.remove(Mo),t.blur(),this._deactivate(q.getElementFromSelector(t));this._queueCallback(()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),j.trigger(t,yo,{relatedTarget:e})):t.classList.remove(Ao)},t,t.classList.contains(Eo))}_keydown(t){if(![xo,ko,Do,Co,So,To].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter(t=>!c(t));let i;if([So,To].includes(t.key))i=e[t.key===So?0:e.length-1];else{const n=[ko,Co].includes(t.key);i=b(e,t.target,n,!0)}i&&(i.focus({preventScroll:!0}),Ho.getOrCreateInstance(i).show())}_getChildren(){return q.find(Io,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=q.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains(Oo))return;const n=(t,n)=>{const s=q.findOne(t,i);s&&s.classList.toggle(n,e)};n(No,Mo),n(Lo,Ao),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(Mo)}_getInnerElement(t){return t.matches(Io)?t:q.findOne(Io,t)}_getOuterElement(t){return t.closest($o)||t}static jQueryInterface(t){return this.each(function(){const e=Ho.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}})}}j.on(document,"click.bs.tab",Yo,function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),c(this)||Ho.getOrCreateInstance(this).show()}),j.on(window,"load.bs.tab",()=>{for(const t of q.find(Wo))Ho.getOrCreateInstance(t)}),g(Ho);const Ro="toast",Fo="mouseover.bs.toast",Uo="mouseout.bs.toast",qo="focusin.bs.toast",zo="focusout.bs.toast",Vo="hide.bs.toast",Bo="hidden.bs.toast",Go="show.bs.toast",Xo="shown.bs.toast",Ko="fade",Zo="hide",Qo="show",Jo="showing",tr={animation:"boolean",autohide:"boolean",delay:"number"},er={animation:!0,autohide:!0,delay:5e3};class ir extends F{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return er}static get DefaultType(){return tr}static get NAME(){return Ro}show(){if(j.trigger(this._element,Go).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(Ko);this._element.classList.remove(Zo),d(this._element),this._element.classList.add(Qo,Jo),this._queueCallback(()=>{this._element.classList.remove(Jo),j.trigger(this._element,Xo),this._maybeScheduleHide()},this._element,this._config.animation)}hide(){if(!this.isShown())return;if(j.trigger(this._element,Vo).defaultPrevented)return;this._element.classList.add(Jo),this._queueCallback(()=>{this._element.classList.add(Zo),this._element.classList.remove(Jo,Qo),j.trigger(this._element,Bo)},this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Qo),super.dispose()}isShown(){return this._element.classList.contains(Qo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){j.on(this._element,Fo,t=>this._onInteraction(t,!0)),j.on(this._element,Uo,t=>this._onInteraction(t,!1)),j.on(this._element,qo,t=>this._onInteraction(t,!0)),j.on(this._element,zo,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const e=ir.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}})}}return z(ir),g(ir),{Alert:Z,Button:tt,Carousel:Ft,Collapse:re,Dropdown:hn,Modal:ts,Offcanvas:ms,Popover:eo,ScrollSpy:mo,Tab:Ho,Toast:ir,Tooltip:Xs}}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.moment=e()}(this,function(){"use strict";var t,e;function i(){return t.apply(null,arguments)}function n(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function s(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function o(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function r(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;var e;for(e in t)if(o(t,e))return!1;return!0}function a(t){return void 0===t}function l(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function c(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function h(t,e){var i,n=[],s=t.length;for(i=0;i<s;++i)n.push(e(t[i],i));return n}function u(t,e){for(var i in e)o(e,i)&&(t[i]=e[i]);return o(e,"toString")&&(t.toString=e.toString),o(e,"valueOf")&&(t.valueOf=e.valueOf),t}function d(t,e,i,n){return Ee(t,e,i,n,!0).utc()}function p(t){return null==t._pf&&(t._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),t._pf}function f(t){var i=null,n=!1,s=t._d&&!isNaN(t._d.getTime());return s&&(i=p(t),n=e.call(i.parsedDateParts,function(t){return null!=t}),s=i.overflow<0&&!i.empty&&!i.invalidEra&&!i.invalidMonth&&!i.invalidWeekday&&!i.weekdayMismatch&&!i.nullInput&&!i.invalidFormat&&!i.userInvalidated&&(!i.meridiem||i.meridiem&&n),t._strict&&(s=s&&0===i.charsLeftOver&&0===i.unusedTokens.length&&void 0===i.bigHour)),null!=Object.isFrozen&&Object.isFrozen(t)?s:(t._isValid=s,t._isValid)}function m(t){var e=d(NaN);return null!=t?u(p(e),t):p(e).userInvalidated=!0,e}e=Array.prototype.some?Array.prototype.some:function(t){var e,i=Object(this),n=i.length>>>0;for(e=0;e<n;e++)if(e in i&&t.call(this,i[e],e,i))return!0;return!1};var g=i.momentProperties=[],v=!1;function y(t,e){var i,n,s,o=g.length;if(a(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),a(e._i)||(t._i=e._i),a(e._f)||(t._f=e._f),a(e._l)||(t._l=e._l),a(e._strict)||(t._strict=e._strict),a(e._tzm)||(t._tzm=e._tzm),a(e._isUTC)||(t._isUTC=e._isUTC),a(e._offset)||(t._offset=e._offset),a(e._pf)||(t._pf=p(e)),a(e._locale)||(t._locale=e._locale),o>0)for(i=0;i<o;i++)a(s=e[n=g[i]])||(t[n]=s);return t}function b(t){y(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===v&&(v=!0,i.updateOffset(this),v=!1)}function _(t){return t instanceof b||null!=t&&null!=t._isAMomentObject}function w(t){!1===i.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function x(t,e){var n=!0;return u(function(){if(null!=i.deprecationHandler&&i.deprecationHandler(null,t),n){var s,r,a,l=[],c=arguments.length;for(r=0;r<c;r++){if(s="","object"==typeof arguments[r]){for(a in s+="\n["+r+"] ",arguments[0])o(arguments[0],a)&&(s+=a+": "+arguments[0][a]+", ");s=s.slice(0,-2)}else s=arguments[r];l.push(s)}w(t+"\nArguments: "+Array.prototype.slice.call(l).join("")+"\n"+(new Error).stack),n=!1}return e.apply(this,arguments)},e)}var k,D={};function C(t,e){null!=i.deprecationHandler&&i.deprecationHandler(t,e),D[t]||(w(e),D[t]=!0)}function S(t){return"undefined"!=typeof Function&&t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function T(t,e){var i,n=u({},t);for(i in e)o(e,i)&&(s(t[i])&&s(e[i])?(n[i]={},u(n[i],t[i]),u(n[i],e[i])):null!=e[i]?n[i]=e[i]:delete n[i]);for(i in t)o(t,i)&&!o(e,i)&&s(t[i])&&(n[i]=u({},n[i]));return n}function M(t){null!=t&&this.set(t)}i.suppressDeprecationWarnings=!1,i.deprecationHandler=null,k=Object.keys?Object.keys:function(t){var e,i=[];for(e in t)o(t,e)&&i.push(e);return i};function E(t,e,i){var n=""+Math.abs(t),s=e-n.length;return(t>=0?i?"+":"":"-")+Math.pow(10,Math.max(0,s)).toString().substr(1)+n}var A=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,O=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,N={},L={};function P(t,e,i,n){var s=n;"string"==typeof n&&(s=function(){return this[n]()}),t&&(L[t]=s),e&&(L[e[0]]=function(){return E(s.apply(this,arguments),e[1],e[2])}),i&&(L[i]=function(){return this.localeData().ordinal(s.apply(this,arguments),t)})}function j(t,e){return t.isValid()?(e=$(e,t.localeData()),N[e]=N[e]||function(t){var e,i,n,s=t.match(A);for(e=0,i=s.length;e<i;e++)L[s[e]]?s[e]=L[s[e]]:s[e]=(n=s[e]).match(/\[[\s\S]/)?n.replace(/^\[|\]$/g,""):n.replace(/\\/g,"");return function(e){var n,o="";for(n=0;n<i;n++)o+=S(s[n])?s[n].call(e,t):s[n];return o}}(e),N[e](t)):t.localeData().invalidDate()}function $(t,e){var i=5;function n(t){return e.longDateFormat(t)||t}for(O.lastIndex=0;i>=0&&O.test(t);)t=t.replace(O,n),O.lastIndex=0,i-=1;return t}var Y={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function I(t){return"string"==typeof t?Y[t]||Y[t.toLowerCase()]:void 0}function W(t){var e,i,n={};for(i in t)o(t,i)&&(e=I(i))&&(n[e]=t[i]);return n}var H={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};var R,F=/\d/,U=/\d\d/,q=/\d{3}/,z=/\d{4}/,V=/[+-]?\d{6}/,B=/\d\d?/,G=/\d\d\d\d?/,X=/\d\d\d\d\d\d?/,K=/\d{1,3}/,Z=/\d{1,4}/,Q=/[+-]?\d{1,6}/,J=/\d+/,tt=/[+-]?\d+/,et=/Z|[+-]\d\d:?\d\d/gi,it=/Z|[+-]\d\d(?::?\d\d)?/gi,nt=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,st=/^[1-9]\d?/,ot=/^([1-9]\d|\d)/;function rt(t,e,i){R[t]=S(e)?e:function(t,n){return t&&i?i:e}}function at(t,e){return o(R,t)?R[t](e._strict,e._locale):new RegExp(lt(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,i,n,s){return e||i||n||s})))}function lt(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function ct(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function ht(t){var e=+t,i=0;return 0!==e&&isFinite(e)&&(i=ct(e)),i}R={};var ut={};function dt(t,e){var i,n,s=e;for("string"==typeof t&&(t=[t]),l(e)&&(s=function(t,i){i[e]=ht(t)}),n=t.length,i=0;i<n;i++)ut[t[i]]=s}function pt(t,e){dt(t,function(t,i,n,s){n._w=n._w||{},e(t,n._w,n,s)})}function ft(t,e,i){null!=e&&o(ut,t)&&ut[t](e,i._a,i,t)}function mt(t){return t%4==0&&t%100!=0||t%400==0}var gt=0,vt=1,yt=2,bt=3,_t=4,wt=5,xt=6,kt=7,Dt=8;function Ct(t){return mt(t)?366:365}P("Y",0,0,function(){var t=this.year();return t<=9999?E(t,4):"+"+t}),P(0,["YY",2],0,function(){return this.year()%100}),P(0,["YYYY",4],0,"year"),P(0,["YYYYY",5],0,"year"),P(0,["YYYYYY",6,!0],0,"year"),rt("Y",tt),rt("YY",B,U),rt("YYYY",Z,z),rt("YYYYY",Q,V),rt("YYYYYY",Q,V),dt(["YYYYY","YYYYYY"],gt),dt("YYYY",function(t,e){e[gt]=2===t.length?i.parseTwoDigitYear(t):ht(t)}),dt("YY",function(t,e){e[gt]=i.parseTwoDigitYear(t)}),dt("Y",function(t,e){e[gt]=parseInt(t,10)}),i.parseTwoDigitYear=function(t){return ht(t)+(ht(t)>68?1900:2e3)};var St,Tt=Mt("FullYear",!0);function Mt(t,e){return function(n){return null!=n?(At(this,t,n),i.updateOffset(this,e),this):Et(this,t)}}function Et(t,e){if(!t.isValid())return NaN;var i=t._d,n=t._isUTC;switch(e){case"Milliseconds":return n?i.getUTCMilliseconds():i.getMilliseconds();case"Seconds":return n?i.getUTCSeconds():i.getSeconds();case"Minutes":return n?i.getUTCMinutes():i.getMinutes();case"Hours":return n?i.getUTCHours():i.getHours();case"Date":return n?i.getUTCDate():i.getDate();case"Day":return n?i.getUTCDay():i.getDay();case"Month":return n?i.getUTCMonth():i.getMonth();case"FullYear":return n?i.getUTCFullYear():i.getFullYear();default:return NaN}}function At(t,e,i){var n,s,o,r,a;if(t.isValid()&&!isNaN(i)){switch(n=t._d,s=t._isUTC,e){case"Milliseconds":return void(s?n.setUTCMilliseconds(i):n.setMilliseconds(i));case"Seconds":return void(s?n.setUTCSeconds(i):n.setSeconds(i));case"Minutes":return void(s?n.setUTCMinutes(i):n.setMinutes(i));case"Hours":return void(s?n.setUTCHours(i):n.setHours(i));case"Date":return void(s?n.setUTCDate(i):n.setDate(i));case"FullYear":break;default:return}o=i,r=t.month(),a=29!==(a=t.date())||1!==r||mt(o)?a:28,s?n.setUTCFullYear(o,r,a):n.setFullYear(o,r,a)}}function Ot(t,e){if(isNaN(t)||isNaN(e))return NaN;var i,n=(e%(i=12)+i)%i;return t+=(e-n)/12,1===n?mt(t)?29:28:31-n%7%2}St=Array.prototype.indexOf?Array.prototype.indexOf:function(t){var e;for(e=0;e<this.length;++e)if(this[e]===t)return e;return-1},P("M",["MM",2],"Mo",function(){return this.month()+1}),P("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),P("MMMM",0,0,function(t){return this.localeData().months(this,t)}),rt("M",B,st),rt("MM",B,U),rt("MMM",function(t,e){return e.monthsShortRegex(t)}),rt("MMMM",function(t,e){return e.monthsRegex(t)}),dt(["M","MM"],function(t,e){e[vt]=ht(t)-1}),dt(["MMM","MMMM"],function(t,e,i,n){var s=i._locale.monthsParse(t,n,i._strict);null!=s?e[vt]=s:p(i).invalidMonth=t});var Nt="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Lt="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Pt=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,jt=nt,$t=nt;function Yt(t,e){if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=ht(e);else if(!l(e=t.localeData().monthsParse(e)))return t;var i=e,n=t.date();return n=n<29?n:Math.min(n,Ot(t.year(),i)),t._isUTC?t._d.setUTCMonth(i,n):t._d.setMonth(i,n),t}function It(t){return null!=t?(Yt(this,t),i.updateOffset(this,!0),this):Et(this,"Month")}function Wt(){function t(t,e){return e.length-t.length}var e,i,n,s,o=[],r=[],a=[];for(e=0;e<12;e++)i=d([2e3,e]),n=lt(this.monthsShort(i,"")),s=lt(this.months(i,"")),o.push(n),r.push(s),a.push(s),a.push(n);o.sort(t),r.sort(t),a.sort(t),this._monthsRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+o.join("|")+")","i")}function Ht(t){var e,i;return t<100&&t>=0?((i=Array.prototype.slice.call(arguments))[0]=t+400,e=new Date(Date.UTC.apply(null,i)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function Rt(t,e,i){var n=7+e-i;return-((7+Ht(t,0,n).getUTCDay()-e)%7)+n-1}function Ft(t,e,i,n,s){var o,r,a=1+7*(e-1)+(7+i-n)%7+Rt(t,n,s);return a<=0?r=Ct(o=t-1)+a:a>Ct(t)?(o=t+1,r=a-Ct(t)):(o=t,r=a),{year:o,dayOfYear:r}}function Ut(t,e,i){var n,s,o=Rt(t.year(),e,i),r=Math.floor((t.dayOfYear()-o-1)/7)+1;return r<1?n=r+qt(s=t.year()-1,e,i):r>qt(t.year(),e,i)?(n=r-qt(t.year(),e,i),s=t.year()+1):(s=t.year(),n=r),{week:n,year:s}}function qt(t,e,i){var n=Rt(t,e,i),s=Rt(t+1,e,i);return(Ct(t)-n+s)/7}P("w",["ww",2],"wo","week"),P("W",["WW",2],"Wo","isoWeek"),rt("w",B,st),rt("ww",B,U),rt("W",B,st),rt("WW",B,U),pt(["w","ww","W","WW"],function(t,e,i,n){e[n.substr(0,1)]=ht(t)});function zt(t,e){return t.slice(e,7).concat(t.slice(0,e))}P("d",0,"do","day"),P("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),P("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),P("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),P("e",0,0,"weekday"),P("E",0,0,"isoWeekday"),rt("d",B),rt("e",B),rt("E",B),rt("dd",function(t,e){return e.weekdaysMinRegex(t)}),rt("ddd",function(t,e){return e.weekdaysShortRegex(t)}),rt("dddd",function(t,e){return e.weekdaysRegex(t)}),pt(["dd","ddd","dddd"],function(t,e,i,n){var s=i._locale.weekdaysParse(t,n,i._strict);null!=s?e.d=s:p(i).invalidWeekday=t}),pt(["d","e","E"],function(t,e,i,n){e[n]=ht(t)});var Vt="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Bt="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Gt="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Xt=nt,Kt=nt,Zt=nt;function Qt(){function t(t,e){return e.length-t.length}var e,i,n,s,o,r=[],a=[],l=[],c=[];for(e=0;e<7;e++)i=d([2e3,1]).day(e),n=lt(this.weekdaysMin(i,"")),s=lt(this.weekdaysShort(i,"")),o=lt(this.weekdays(i,"")),r.push(n),a.push(s),l.push(o),c.push(n),c.push(s),c.push(o);r.sort(t),a.sort(t),l.sort(t),c.sort(t),this._weekdaysRegex=new RegExp("^("+c.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+r.join("|")+")","i")}function Jt(){return this.hours()%12||12}function te(t,e){P(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function ee(t,e){return e._meridiemParse}P("H",["HH",2],0,"hour"),P("h",["hh",2],0,Jt),P("k",["kk",2],0,function(){return this.hours()||24}),P("hmm",0,0,function(){return""+Jt.apply(this)+E(this.minutes(),2)}),P("hmmss",0,0,function(){return""+Jt.apply(this)+E(this.minutes(),2)+E(this.seconds(),2)}),P("Hmm",0,0,function(){return""+this.hours()+E(this.minutes(),2)}),P("Hmmss",0,0,function(){return""+this.hours()+E(this.minutes(),2)+E(this.seconds(),2)}),te("a",!0),te("A",!1),rt("a",ee),rt("A",ee),rt("H",B,ot),rt("h",B,st),rt("k",B,st),rt("HH",B,U),rt("hh",B,U),rt("kk",B,U),rt("hmm",G),rt("hmmss",X),rt("Hmm",G),rt("Hmmss",X),dt(["H","HH"],bt),dt(["k","kk"],function(t,e,i){var n=ht(t);e[bt]=24===n?0:n}),dt(["a","A"],function(t,e,i){i._isPm=i._locale.isPM(t),i._meridiem=t}),dt(["h","hh"],function(t,e,i){e[bt]=ht(t),p(i).bigHour=!0}),dt("hmm",function(t,e,i){var n=t.length-2;e[bt]=ht(t.substr(0,n)),e[_t]=ht(t.substr(n)),p(i).bigHour=!0}),dt("hmmss",function(t,e,i){var n=t.length-4,s=t.length-2;e[bt]=ht(t.substr(0,n)),e[_t]=ht(t.substr(n,2)),e[wt]=ht(t.substr(s)),p(i).bigHour=!0}),dt("Hmm",function(t,e,i){var n=t.length-2;e[bt]=ht(t.substr(0,n)),e[_t]=ht(t.substr(n))}),dt("Hmmss",function(t,e,i){var n=t.length-4,s=t.length-2;e[bt]=ht(t.substr(0,n)),e[_t]=ht(t.substr(n,2)),e[wt]=ht(t.substr(s))});var ie=Mt("Hours",!0);var ne,se={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Nt,monthsShort:Lt,week:{dow:0,doy:6},weekdays:Vt,weekdaysMin:Gt,weekdaysShort:Bt,meridiemParse:/[ap]\.?m?\.?/i},oe={},re={};function ae(t,e){var i,n=Math.min(t.length,e.length);for(i=0;i<n;i+=1)if(t[i]!==e[i])return i;return n}function le(t){return t?t.toLowerCase().replace("_","-"):t}function ce(t){var e=null;if(void 0===oe[t]&&"undefined"!=typeof module&&module&&module.exports&&function(t){return!(!t||!t.match("^[^/\\\\]*$"))}(t))try{e=ne._abbr,require("./locale/"+t),he(e)}catch(e){oe[t]=null}return oe[t]}function he(t,e){var i;return t&&((i=a(e)?de(t):ue(t,e))?ne=i:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),ne._abbr}function ue(t,e){if(null!==e){var i,n=se;if(e.abbr=t,null!=oe[t])C("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),n=oe[t]._config;else if(null!=e.parentLocale)if(null!=oe[e.parentLocale])n=oe[e.parentLocale]._config;else{if(null==(i=ce(e.parentLocale)))return re[e.parentLocale]||(re[e.parentLocale]=[]),re[e.parentLocale].push({name:t,config:e}),null;n=i._config}return oe[t]=new M(T(n,e)),re[t]&&re[t].forEach(function(t){ue(t.name,t.config)}),he(t),oe[t]}return delete oe[t],null}function de(t){var e;if(t&&t._locale&&t._locale._abbr&&(t=t._locale._abbr),!t)return ne;if(!n(t)){if(e=ce(t))return e;t=[t]}return function(t){for(var e,i,n,s,o=0;o<t.length;){for(e=(s=le(t[o]).split("-")).length,i=(i=le(t[o+1]))?i.split("-"):null;e>0;){if(n=ce(s.slice(0,e).join("-")))return n;if(i&&i.length>=e&&ae(s,i)>=e-1)break;e--}o++}return ne}(t)}function pe(t){var e,i=t._a;return i&&-2===p(t).overflow&&(e=i[vt]<0||i[vt]>11?vt:i[yt]<1||i[yt]>Ot(i[gt],i[vt])?yt:i[bt]<0||i[bt]>24||24===i[bt]&&(0!==i[_t]||0!==i[wt]||0!==i[xt])?bt:i[_t]<0||i[_t]>59?_t:i[wt]<0||i[wt]>59?wt:i[xt]<0||i[xt]>999?xt:-1,p(t)._overflowDayOfYear&&(e<gt||e>yt)&&(e=yt),p(t)._overflowWeeks&&-1===e&&(e=kt),p(t)._overflowWeekday&&-1===e&&(e=Dt),p(t).overflow=e),t}var fe=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,me=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ge=/Z|[+-]\d\d(?::?\d\d)?/,ve=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ye=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],be=/^\/?Date\((-?\d+)/i,_e=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,we={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function xe(t){var e,i,n,s,o,r,a=t._i,l=fe.exec(a)||me.exec(a),c=ve.length,h=ye.length;if(l){for(p(t).iso=!0,e=0,i=c;e<i;e++)if(ve[e][1].exec(l[1])){s=ve[e][0],n=!1!==ve[e][2];break}if(null==s)return void(t._isValid=!1);if(l[3]){for(e=0,i=h;e<i;e++)if(ye[e][1].exec(l[3])){o=(l[2]||" ")+ye[e][0];break}if(null==o)return void(t._isValid=!1)}if(!n&&null!=o)return void(t._isValid=!1);if(l[4]){if(!ge.exec(l[4]))return void(t._isValid=!1);r="Z"}t._f=s+(o||"")+(r||""),Te(t)}else t._isValid=!1}function ke(t){var e=parseInt(t,10);return e<=49?2e3+e:e<=999?1900+e:e}function De(t){var e,i,n,s,o,r,a,l,c=_e.exec(t._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(c){if(i=c[4],n=c[3],s=c[2],o=c[5],r=c[6],a=c[7],l=[ke(i),Lt.indexOf(n),parseInt(s,10),parseInt(o,10),parseInt(r,10)],a&&l.push(parseInt(a,10)),e=l,!function(t,e,i){return!t||Bt.indexOf(t)===new Date(e[0],e[1],e[2]).getDay()||(p(i).weekdayMismatch=!0,i._isValid=!1,!1)}(c[1],e,t))return;t._a=e,t._tzm=function(t,e,i){if(t)return we[t];if(e)return 0;var n=parseInt(i,10),s=n%100;return(n-s)/100*60+s}(c[8],c[9],c[10]),t._d=Ht.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),p(t).rfc2822=!0}else t._isValid=!1}function Ce(t,e,i){return null!=t?t:null!=e?e:i}function Se(t){var e,n,s,o,r,a=[];if(!t._d){for(s=function(t){var e=new Date(i.now());return t._useUTC?[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()]:[e.getFullYear(),e.getMonth(),e.getDate()]}(t),t._w&&null==t._a[yt]&&null==t._a[vt]&&function(t){var e,i,n,s,o,r,a,l,c;null!=(e=t._w).GG||null!=e.W||null!=e.E?(o=1,r=4,i=Ce(e.GG,t._a[gt],Ut(Ae(),1,4).year),n=Ce(e.W,1),((s=Ce(e.E,1))<1||s>7)&&(l=!0)):(o=t._locale._week.dow,r=t._locale._week.doy,c=Ut(Ae(),o,r),i=Ce(e.gg,t._a[gt],c.year),n=Ce(e.w,c.week),null!=e.d?((s=e.d)<0||s>6)&&(l=!0):null!=e.e?(s=e.e+o,(e.e<0||e.e>6)&&(l=!0)):s=o);n<1||n>qt(i,o,r)?p(t)._overflowWeeks=!0:null!=l?p(t)._overflowWeekday=!0:(a=Ft(i,n,s,o,r),t._a[gt]=a.year,t._dayOfYear=a.dayOfYear)}(t),null!=t._dayOfYear&&(r=Ce(t._a[gt],s[gt]),(t._dayOfYear>Ct(r)||0===t._dayOfYear)&&(p(t)._overflowDayOfYear=!0),n=Ht(r,0,t._dayOfYear),t._a[vt]=n.getUTCMonth(),t._a[yt]=n.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=a[e]=s[e];for(;e<7;e++)t._a[e]=a[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[bt]&&0===t._a[_t]&&0===t._a[wt]&&0===t._a[xt]&&(t._nextDay=!0,t._a[bt]=0),t._d=(t._useUTC?Ht:function(t,e,i,n,s,o,r){var a;return t<100&&t>=0?(a=new Date(t+400,e,i,n,s,o,r),isFinite(a.getFullYear())&&a.setFullYear(t)):a=new Date(t,e,i,n,s,o,r),a}).apply(null,a),o=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[bt]=24),t._w&&void 0!==t._w.d&&t._w.d!==o&&(p(t).weekdayMismatch=!0)}}function Te(t){if(t._f!==i.ISO_8601)if(t._f!==i.RFC_2822){t._a=[],p(t).empty=!0;var e,n,s,o,r,a,l,c=""+t._i,h=c.length,u=0;for(l=(s=$(t._f,t._locale).match(A)||[]).length,e=0;e<l;e++)o=s[e],(n=(c.match(at(o,t))||[])[0])&&((r=c.substr(0,c.indexOf(n))).length>0&&p(t).unusedInput.push(r),c=c.slice(c.indexOf(n)+n.length),u+=n.length),L[o]?(n?p(t).empty=!1:p(t).unusedTokens.push(o),ft(o,n,t)):t._strict&&!n&&p(t).unusedTokens.push(o);p(t).charsLeftOver=h-u,c.length>0&&p(t).unusedInput.push(c),t._a[bt]<=12&&!0===p(t).bigHour&&t._a[bt]>0&&(p(t).bigHour=void 0),p(t).parsedDateParts=t._a.slice(0),p(t).meridiem=t._meridiem,t._a[bt]=function(t,e,i){var n;if(null==i)return e;return null!=t.meridiemHour?t.meridiemHour(e,i):null!=t.isPM?((n=t.isPM(i))&&e<12&&(e+=12),n||12!==e||(e=0),e):e}(t._locale,t._a[bt],t._meridiem),null!==(a=p(t).era)&&(t._a[gt]=t._locale.erasConvertYear(a,t._a[gt])),Se(t),pe(t)}else De(t);else xe(t)}function Me(t){var e=t._i,o=t._f;return t._locale=t._locale||de(t._l),null===e||void 0===o&&""===e?m({nullInput:!0}):("string"==typeof e&&(t._i=e=t._locale.preparse(e)),_(e)?new b(pe(e)):(c(e)?t._d=e:n(o)?function(t){var e,i,n,s,o,r,a=!1,l=t._f.length;if(0===l)return p(t).invalidFormat=!0,void(t._d=new Date(NaN));for(s=0;s<l;s++)o=0,r=!1,e=y({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[s],Te(e),f(e)&&(r=!0),o+=p(e).charsLeftOver,o+=10*p(e).unusedTokens.length,p(e).score=o,a?o<n&&(n=o,i=e):(null==n||o<n||r)&&(n=o,i=e,r&&(a=!0));u(t,i||e)}(t):o?Te(t):function(t){var e=t._i;a(e)?t._d=new Date(i.now()):c(e)?t._d=new Date(e.valueOf()):"string"==typeof e?function(t){var e=be.exec(t._i);null===e?(xe(t),!1===t._isValid&&(delete t._isValid,De(t),!1===t._isValid&&(delete t._isValid,t._strict?t._isValid=!1:i.createFromInputFallback(t)))):t._d=new Date(+e[1])}(t):n(e)?(t._a=h(e.slice(0),function(t){return parseInt(t,10)}),Se(t)):s(e)?function(t){if(!t._d){var e=W(t._i),i=void 0===e.day?e.date:e.day;t._a=h([e.year,e.month,i,e.hour,e.minute,e.second,e.millisecond],function(t){return t&&parseInt(t,10)}),Se(t)}}(t):l(e)?t._d=new Date(e):i.createFromInputFallback(t)}(t),f(t)||(t._d=null),t))}function Ee(t,e,i,o,a){var l,c={};return!0!==e&&!1!==e||(o=e,e=void 0),!0!==i&&!1!==i||(o=i,i=void 0),(s(t)&&r(t)||n(t)&&0===t.length)&&(t=void 0),c._isAMomentObject=!0,c._useUTC=c._isUTC=a,c._l=i,c._i=t,c._f=e,c._strict=o,(l=new b(pe(Me(c))))._nextDay&&(l.add(1,"d"),l._nextDay=void 0),l}function Ae(t,e,i,n){return Ee(t,e,i,n,!1)}i.createFromInputFallback=x("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),i.ISO_8601=function(){},i.RFC_2822=function(){};var Oe=x("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Ae.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:m()}),Ne=x("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Ae.apply(null,arguments);return this.isValid()&&t.isValid()?t>this?this:t:m()});function Le(t,e){var i,s;if(1===e.length&&n(e[0])&&(e=e[0]),!e.length)return Ae();for(i=e[0],s=1;s<e.length;++s)e[s].isValid()&&!e[s][t](i)||(i=e[s]);return i}var Pe=["year","quarter","month","week","day","hour","minute","second","millisecond"];function je(t){var e=W(t),i=e.year||0,n=e.quarter||0,s=e.month||0,r=e.week||e.isoWeek||0,a=e.day||0,l=e.hour||0,c=e.minute||0,h=e.second||0,u=e.millisecond||0;this._isValid=function(t){var e,i,n=!1,s=Pe.length;for(e in t)if(o(t,e)&&(-1===St.call(Pe,e)||null!=t[e]&&isNaN(t[e])))return!1;for(i=0;i<s;++i)if(t[Pe[i]]){if(n)return!1;parseFloat(t[Pe[i]])!==ht(t[Pe[i]])&&(n=!0)}return!0}(e),this._milliseconds=+u+1e3*h+6e4*c+1e3*l*60*60,this._days=+a+7*r,this._months=+s+3*n+12*i,this._data={},this._locale=de(),this._bubble()}function $e(t){return t instanceof je}function Ye(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Ie(t,e){P(t,0,0,function(){var t=this.utcOffset(),i="+";return t<0&&(t=-t,i="-"),i+E(~~(t/60),2)+e+E(~~t%60,2)})}Ie("Z",":"),Ie("ZZ",""),rt("Z",it),rt("ZZ",it),dt(["Z","ZZ"],function(t,e,i){i._useUTC=!0,i._tzm=He(it,t)});var We=/([\+\-]|\d\d)/gi;function He(t,e){var i,n,s=(e||"").match(t);return null===s?null:0===(n=60*(i=((s[s.length-1]||[])+"").match(We)||["-",0,0])[1]+ht(i[2]))?0:"+"===i[0]?n:-n}function Re(t,e){var n,s;return e._isUTC?(n=e.clone(),s=(_(t)||c(t)?t.valueOf():Ae(t).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+s),i.updateOffset(n,!1),n):Ae(t).local()}function Fe(t){return-Math.round(t._d.getTimezoneOffset())}function Ue(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}i.updateOffset=function(){};var qe=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,ze=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Ve(t,e){var i,n,s,r=t,a=null;return $e(t)?r={ms:t._milliseconds,d:t._days,M:t._months}:l(t)||!isNaN(+t)?(r={},e?r[e]=+t:r.milliseconds=+t):(a=qe.exec(t))?(i="-"===a[1]?-1:1,r={y:0,d:ht(a[yt])*i,h:ht(a[bt])*i,m:ht(a[_t])*i,s:ht(a[wt])*i,ms:ht(Ye(1e3*a[xt]))*i}):(a=ze.exec(t))?(i="-"===a[1]?-1:1,r={y:Be(a[2],i),M:Be(a[3],i),w:Be(a[4],i),d:Be(a[5],i),h:Be(a[6],i),m:Be(a[7],i),s:Be(a[8],i)}):null==r?r={}:"object"==typeof r&&("from"in r||"to"in r)&&(s=function(t,e){var i;if(!t.isValid()||!e.isValid())return{milliseconds:0,months:0};e=Re(e,t),t.isBefore(e)?i=Ge(t,e):((i=Ge(e,t)).milliseconds=-i.milliseconds,i.months=-i.months);return i}(Ae(r.from),Ae(r.to)),(r={}).ms=s.milliseconds,r.M=s.months),n=new je(r),$e(t)&&o(t,"_locale")&&(n._locale=t._locale),$e(t)&&o(t,"_isValid")&&(n._isValid=t._isValid),n}function Be(t,e){var i=t&&parseFloat(t.replace(",","."));return(isNaN(i)?0:i)*e}function Ge(t,e){var i={};return i.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(i.months,"M").isAfter(e)&&--i.months,i.milliseconds=+e-+t.clone().add(i.months,"M"),i}function Xe(t,e){return function(i,n){var s;return null===n||isNaN(+n)||(C(e,"moment()."+e+"(period, number) is deprecated. Please use moment()."+e+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=i,i=n,n=s),Ke(this,Ve(i,n),t),this}}function Ke(t,e,n,s){var o=e._milliseconds,r=Ye(e._days),a=Ye(e._months);t.isValid()&&(s=null==s||s,a&&Yt(t,Et(t,"Month")+a*n),r&&At(t,"Date",Et(t,"Date")+r*n),o&&t._d.setTime(t._d.valueOf()+o*n),s&&i.updateOffset(t,r||a))}Ve.fn=je.prototype,Ve.invalid=function(){return Ve(NaN)};var Ze=Xe(1,"add"),Qe=Xe(-1,"subtract");function Je(t){return"string"==typeof t||t instanceof String}function ti(t){return _(t)||c(t)||Je(t)||l(t)||function(t){var e=n(t),i=!1;e&&(i=0===t.filter(function(e){return!l(e)&&Je(t)}).length);return e&&i}(t)||function(t){var e,i,n=s(t)&&!r(t),a=!1,l=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],c=l.length;for(e=0;e<c;e+=1)i=l[e],a=a||o(t,i);return n&&a}(t)||null==t}function ei(t,e){if(t.date()<e.date())return-ei(e,t);var i=12*(e.year()-t.year())+(e.month()-t.month()),n=t.clone().add(i,"months");return-(i+(e-n<0?(e-n)/(n-t.clone().add(i-1,"months")):(e-n)/(t.clone().add(i+1,"months")-n)))||0}function ii(t){var e;return void 0===t?this._locale._abbr:(null!=(e=de(t))&&(this._locale=e),this)}i.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",i.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var ni=x("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)});function si(){return this._locale}var oi=1e3,ri=60*oi,ai=60*ri,li=3506328*ai;function ci(t,e){return(t%e+e)%e}function hi(t,e,i){return t<100&&t>=0?new Date(t+400,e,i)-li:new Date(t,e,i).valueOf()}function ui(t,e,i){return t<100&&t>=0?Date.UTC(t+400,e,i)-li:Date.UTC(t,e,i)}function di(t,e){return e.erasAbbrRegex(t)}function pi(){var t,e,i,n,s,o=[],r=[],a=[],l=[],c=this.eras();for(t=0,e=c.length;t<e;++t)i=lt(c[t].name),n=lt(c[t].abbr),s=lt(c[t].narrow),r.push(i),o.push(n),a.push(s),l.push(i),l.push(n),l.push(s);this._erasRegex=new RegExp("^("+l.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+r.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+o.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+a.join("|")+")","i")}function fi(t,e){P(0,[t,t.length],0,e)}function mi(t,e,i,n,s){var o;return null==t?Ut(this,n,s).year:(e>(o=qt(t,n,s))&&(e=o),function(t,e,i,n,s){var o=Ft(t,e,i,n,s),r=Ht(o.year,0,o.dayOfYear);return this.year(r.getUTCFullYear()),this.month(r.getUTCMonth()),this.date(r.getUTCDate()),this}.call(this,t,e,i,n,s))}P("N",0,0,"eraAbbr"),P("NN",0,0,"eraAbbr"),P("NNN",0,0,"eraAbbr"),P("NNNN",0,0,"eraName"),P("NNNNN",0,0,"eraNarrow"),P("y",["y",1],"yo","eraYear"),P("y",["yy",2],0,"eraYear"),P("y",["yyy",3],0,"eraYear"),P("y",["yyyy",4],0,"eraYear"),rt("N",di),rt("NN",di),rt("NNN",di),rt("NNNN",function(t,e){return e.erasNameRegex(t)}),rt("NNNNN",function(t,e){return e.erasNarrowRegex(t)}),dt(["N","NN","NNN","NNNN","NNNNN"],function(t,e,i,n){var s=i._locale.erasParse(t,n,i._strict);s?p(i).era=s:p(i).invalidEra=t}),rt("y",J),rt("yy",J),rt("yyy",J),rt("yyyy",J),rt("yo",function(t,e){return e._eraYearOrdinalRegex||J}),dt(["y","yy","yyy","yyyy"],gt),dt(["yo"],function(t,e,i,n){var s;i._locale._eraYearOrdinalRegex&&(s=t.match(i._locale._eraYearOrdinalRegex)),i._locale.eraYearOrdinalParse?e[gt]=i._locale.eraYearOrdinalParse(t,s):e[gt]=parseInt(t,10)}),P(0,["gg",2],0,function(){return this.weekYear()%100}),P(0,["GG",2],0,function(){return this.isoWeekYear()%100}),fi("gggg","weekYear"),fi("ggggg","weekYear"),fi("GGGG","isoWeekYear"),fi("GGGGG","isoWeekYear"),rt("G",tt),rt("g",tt),rt("GG",B,U),rt("gg",B,U),rt("GGGG",Z,z),rt("gggg",Z,z),rt("GGGGG",Q,V),rt("ggggg",Q,V),pt(["gggg","ggggg","GGGG","GGGGG"],function(t,e,i,n){e[n.substr(0,2)]=ht(t)}),pt(["gg","GG"],function(t,e,n,s){e[s]=i.parseTwoDigitYear(t)}),P("Q",0,"Qo","quarter"),rt("Q",F),dt("Q",function(t,e){e[vt]=3*(ht(t)-1)}),P("D",["DD",2],"Do","date"),rt("D",B,st),rt("DD",B,U),rt("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),dt(["D","DD"],yt),dt("Do",function(t,e){e[yt]=ht(t.match(B)[0])});var gi=Mt("Date",!0);P("DDD",["DDDD",3],"DDDo","dayOfYear"),rt("DDD",K),rt("DDDD",q),dt(["DDD","DDDD"],function(t,e,i){i._dayOfYear=ht(t)}),P("m",["mm",2],0,"minute"),rt("m",B,ot),rt("mm",B,U),dt(["m","mm"],_t);var vi=Mt("Minutes",!1);P("s",["ss",2],0,"second"),rt("s",B,ot),rt("ss",B,U),dt(["s","ss"],wt);var yi,bi,_i=Mt("Seconds",!1);for(P("S",0,0,function(){return~~(this.millisecond()/100)}),P(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),P(0,["SSS",3],0,"millisecond"),P(0,["SSSS",4],0,function(){return 10*this.millisecond()}),P(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),P(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),P(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),P(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),P(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),rt("S",K,F),rt("SS",K,U),rt("SSS",K,q),yi="SSSS";yi.length<=9;yi+="S")rt(yi,J);function wi(t,e){e[xt]=ht(1e3*("0."+t))}for(yi="S";yi.length<=9;yi+="S")dt(yi,wi);bi=Mt("Milliseconds",!1),P("z",0,0,"zoneAbbr"),P("zz",0,0,"zoneName");var xi=b.prototype;function ki(t){return t}xi.add=Ze,xi.calendar=function(t,e){1===arguments.length&&(arguments[0]?ti(arguments[0])?(t=arguments[0],e=void 0):function(t){var e,i=s(t)&&!r(t),n=!1,a=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(e=0;e<a.length;e+=1)n=n||o(t,a[e]);return i&&n}(arguments[0])&&(e=arguments[0],t=void 0):(t=void 0,e=void 0));var n=t||Ae(),a=Re(n,this).startOf("day"),l=i.calendarFormat(this,a)||"sameElse",c=e&&(S(e[l])?e[l].call(this,n):e[l]);return this.format(c||this.localeData().calendar(l,this,Ae(n)))},xi.clone=function(){return new b(this)},xi.diff=function(t,e,i){var n,s,o;if(!this.isValid())return NaN;if(!(n=Re(t,this)).isValid())return NaN;switch(s=6e4*(n.utcOffset()-this.utcOffset()),e=I(e)){case"year":o=ei(this,n)/12;break;case"month":o=ei(this,n);break;case"quarter":o=ei(this,n)/3;break;case"second":o=(this-n)/1e3;break;case"minute":o=(this-n)/6e4;break;case"hour":o=(this-n)/36e5;break;case"day":o=(this-n-s)/864e5;break;case"week":o=(this-n-s)/6048e5;break;default:o=this-n}return i?o:ct(o)},xi.endOf=function(t){var e,n;if(void 0===(t=I(t))||"millisecond"===t||!this.isValid())return this;switch(n=this._isUTC?ui:hi,t){case"year":e=n(this.year()+1,0,1)-1;break;case"quarter":e=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":e=n(this.year(),this.month()+1,1)-1;break;case"week":e=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":e=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":e=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":e=this._d.valueOf(),e+=ai-ci(e+(this._isUTC?0:this.utcOffset()*ri),ai)-1;break;case"minute":e=this._d.valueOf(),e+=ri-ci(e,ri)-1;break;case"second":e=this._d.valueOf(),e+=oi-ci(e,oi)-1}return this._d.setTime(e),i.updateOffset(this,!0),this},xi.format=function(t){t||(t=this.isUtc()?i.defaultFormatUtc:i.defaultFormat);var e=j(this,t);return this.localeData().postformat(e)},xi.from=function(t,e){return this.isValid()&&(_(t)&&t.isValid()||Ae(t).isValid())?Ve({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},xi.fromNow=function(t){return this.from(Ae(),t)},xi.to=function(t,e){return this.isValid()&&(_(t)&&t.isValid()||Ae(t).isValid())?Ve({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},xi.toNow=function(t){return this.to(Ae(),t)},xi.get=function(t){return S(this[t=I(t)])?this[t]():this},xi.invalidAt=function(){return p(this).overflow},xi.isAfter=function(t,e){var i=_(t)?t:Ae(t);return!(!this.isValid()||!i.isValid())&&("millisecond"===(e=I(e)||"millisecond")?this.valueOf()>i.valueOf():i.valueOf()<this.clone().startOf(e).valueOf())},xi.isBefore=function(t,e){var i=_(t)?t:Ae(t);return!(!this.isValid()||!i.isValid())&&("millisecond"===(e=I(e)||"millisecond")?this.valueOf()<i.valueOf():this.clone().endOf(e).valueOf()<i.valueOf())},xi.isBetween=function(t,e,i,n){var s=_(t)?t:Ae(t),o=_(e)?e:Ae(e);return!!(this.isValid()&&s.isValid()&&o.isValid())&&("("===(n=n||"()")[0]?this.isAfter(s,i):!this.isBefore(s,i))&&(")"===n[1]?this.isBefore(o,i):!this.isAfter(o,i))},xi.isSame=function(t,e){var i,n=_(t)?t:Ae(t);return!(!this.isValid()||!n.isValid())&&("millisecond"===(e=I(e)||"millisecond")?this.valueOf()===n.valueOf():(i=n.valueOf(),this.clone().startOf(e).valueOf()<=i&&i<=this.clone().endOf(e).valueOf()))},xi.isSameOrAfter=function(t,e){return this.isSame(t,e)||this.isAfter(t,e)},xi.isSameOrBefore=function(t,e){return this.isSame(t,e)||this.isBefore(t,e)},xi.isValid=function(){return f(this)},xi.lang=ni,xi.locale=ii,xi.localeData=si,xi.max=Ne,xi.min=Oe,xi.parsingFlags=function(){return u({},p(this))},xi.set=function(t,e){if("object"==typeof t){var i,n=function(t){var e,i=[];for(e in t)o(t,e)&&i.push({unit:e,priority:H[e]});return i.sort(function(t,e){return t.priority-e.priority}),i}(t=W(t)),s=n.length;for(i=0;i<s;i++)this[n[i].unit](t[n[i].unit])}else if(S(this[t=I(t)]))return this[t](e);return this},xi.startOf=function(t){var e,n;if(void 0===(t=I(t))||"millisecond"===t||!this.isValid())return this;switch(n=this._isUTC?ui:hi,t){case"year":e=n(this.year(),0,1);break;case"quarter":e=n(this.year(),this.month()-this.month()%3,1);break;case"month":e=n(this.year(),this.month(),1);break;case"week":e=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":e=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":e=n(this.year(),this.month(),this.date());break;case"hour":e=this._d.valueOf(),e-=ci(e+(this._isUTC?0:this.utcOffset()*ri),ai);break;case"minute":e=this._d.valueOf(),e-=ci(e,ri);break;case"second":e=this._d.valueOf(),e-=ci(e,oi)}return this._d.setTime(e),i.updateOffset(this,!0),this},xi.subtract=Qe,xi.toArray=function(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]},xi.toObject=function(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}},xi.toDate=function(){return new Date(this.valueOf())},xi.toISOString=function(t){if(!this.isValid())return null;var e=!0!==t,i=e?this.clone().utc():this;return i.year()<0||i.year()>9999?j(i,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):S(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",j(i,"Z")):j(i,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},xi.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t,e,i,n="moment",s="";return this.isLocal()||(n=0===this.utcOffset()?"moment.utc":"moment.parseZone",s="Z"),t="["+n+'("]',e=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",i=s+'[")]',this.format(t+e+"-MM-DD[T]HH:mm:ss.SSS"+i)},"undefined"!=typeof Symbol&&null!=Symbol.for&&(xi[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),xi.toJSON=function(){return this.isValid()?this.toISOString():null},xi.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},xi.unix=function(){return Math.floor(this.valueOf()/1e3)},xi.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},xi.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},xi.eraName=function(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].name;if(n[t].until<=i&&i<=n[t].since)return n[t].name}return""},xi.eraNarrow=function(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].narrow;if(n[t].until<=i&&i<=n[t].since)return n[t].narrow}return""},xi.eraAbbr=function(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].abbr;if(n[t].until<=i&&i<=n[t].since)return n[t].abbr}return""},xi.eraYear=function(){var t,e,n,s,o=this.localeData().eras();for(t=0,e=o.length;t<e;++t)if(n=o[t].since<=o[t].until?1:-1,s=this.clone().startOf("day").valueOf(),o[t].since<=s&&s<=o[t].until||o[t].until<=s&&s<=o[t].since)return(this.year()-i(o[t].since).year())*n+o[t].offset;return this.year()},xi.year=Tt,xi.isLeapYear=function(){return mt(this.year())},xi.weekYear=function(t){return mi.call(this,t,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},xi.isoWeekYear=function(t){return mi.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)},xi.quarter=xi.quarters=function(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)},xi.month=It,xi.daysInMonth=function(){return Ot(this.year(),this.month())},xi.week=xi.weeks=function(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")},xi.isoWeek=xi.isoWeeks=function(t){var e=Ut(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")},xi.weeksInYear=function(){var t=this.localeData()._week;return qt(this.year(),t.dow,t.doy)},xi.weeksInWeekYear=function(){var t=this.localeData()._week;return qt(this.weekYear(),t.dow,t.doy)},xi.isoWeeksInYear=function(){return qt(this.year(),1,4)},xi.isoWeeksInISOWeekYear=function(){return qt(this.isoWeekYear(),1,4)},xi.date=gi,xi.day=xi.days=function(t){if(!this.isValid())return null!=t?this:NaN;var e=Et(this,"Day");return null!=t?(t=function(t,e){return"string"!=typeof t?t:isNaN(t)?"number"==typeof(t=e.weekdaysParse(t))?t:null:parseInt(t,10)}(t,this.localeData()),this.add(t-e,"d")):e},xi.weekday=function(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")},xi.isoWeekday=function(t){if(!this.isValid())return null!=t?this:NaN;if(null!=t){var e=function(t,e){return"string"==typeof t?e.weekdaysParse(t)%7||7:isNaN(t)?null:t}(t,this.localeData());return this.day(this.day()%7?e:e-7)}return this.day()||7},xi.dayOfYear=function(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")},xi.hour=xi.hours=ie,xi.minute=xi.minutes=vi,xi.second=xi.seconds=_i,xi.millisecond=xi.milliseconds=bi,xi.utcOffset=function(t,e,n){var s,o=this._offset||0;if(!this.isValid())return null!=t?this:NaN;if(null!=t){if("string"==typeof t){if(null===(t=He(it,t)))return this}else Math.abs(t)<16&&!n&&(t*=60);return!this._isUTC&&e&&(s=Fe(this)),this._offset=t,this._isUTC=!0,null!=s&&this.add(s,"m"),o!==t&&(!e||this._changeInProgress?Ke(this,Ve(t-o,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,i.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?o:Fe(this)},xi.utc=function(t){return this.utcOffset(0,t)},xi.local=function(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(Fe(this),"m")),this},xi.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var t=He(et,this._i);null!=t?this.utcOffset(t):this.utcOffset(0,!0)}return this},xi.hasAlignedHourOffset=function(t){return!!this.isValid()&&(t=t?Ae(t).utcOffset():0,(this.utcOffset()-t)%60==0)},xi.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},xi.isLocal=function(){return!!this.isValid()&&!this._isUTC},xi.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},xi.isUtc=Ue,xi.isUTC=Ue,xi.zoneAbbr=function(){return this._isUTC?"UTC":""},xi.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},xi.dates=x("dates accessor is deprecated. Use date instead.",gi),xi.months=x("months accessor is deprecated. Use month instead",It),xi.years=x("years accessor is deprecated. Use year instead",Tt),xi.zone=x("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(t,e){return null!=t?("string"!=typeof t&&(t=-t),this.utcOffset(t,e),this):-this.utcOffset()}),xi.isDSTShifted=x("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){if(!a(this._isDSTShifted))return this._isDSTShifted;var t,e={};return y(e,this),(e=Me(e))._a?(t=e._isUTC?d(e._a):Ae(e._a),this._isDSTShifted=this.isValid()&&function(t,e,i){var n,s=Math.min(t.length,e.length),o=Math.abs(t.length-e.length),r=0;for(n=0;n<s;n++)(i&&t[n]!==e[n]||!i&&ht(t[n])!==ht(e[n]))&&r++;return r+o}(e._a,t.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted});var Di=M.prototype;function Ci(t,e,i,n){var s=de(),o=d().set(n,e);return s[i](o,t)}function Si(t,e,i){if(l(t)&&(e=t,t=void 0),t=t||"",null!=e)return Ci(t,e,i,"month");var n,s=[];for(n=0;n<12;n++)s[n]=Ci(t,n,i,"month");return s}function Ti(t,e,i,n){"boolean"==typeof t?(l(e)&&(i=e,e=void 0),e=e||""):(i=e=t,t=!1,l(e)&&(i=e,e=void 0),e=e||"");var s,o=de(),r=t?o._week.dow:0,a=[];if(null!=i)return Ci(e,(i+r)%7,n,"day");for(s=0;s<7;s++)a[s]=Ci(e,(s+r)%7,n,"day");return a}Di.calendar=function(t,e,i){var n=this._calendar[t]||this._calendar.sameElse;return S(n)?n.call(e,i):n},Di.longDateFormat=function(t){var e=this._longDateFormat[t],i=this._longDateFormat[t.toUpperCase()];return e||!i?e:(this._longDateFormat[t]=i.match(A).map(function(t){return"MMMM"===t||"MM"===t||"DD"===t||"dddd"===t?t.slice(1):t}).join(""),this._longDateFormat[t])},Di.invalidDate=function(){return this._invalidDate},Di.ordinal=function(t){return this._ordinal.replace("%d",t)},Di.preparse=ki,Di.postformat=ki,Di.relativeTime=function(t,e,i,n){var s=this._relativeTime[i];return S(s)?s(t,e,i,n):s.replace(/%d/i,t)},Di.pastFuture=function(t,e){var i=this._relativeTime[t>0?"future":"past"];return S(i)?i(e):i.replace(/%s/i,e)},Di.set=function(t){var e,i;for(i in t)o(t,i)&&(S(e=t[i])?this[i]=e:this["_"+i]=e);this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},Di.eras=function(t,e){var n,s,o,r=this._eras||de("en")._eras;for(n=0,s=r.length;n<s;++n){switch(typeof r[n].since){case"string":o=i(r[n].since).startOf("day"),r[n].since=o.valueOf()}switch(typeof r[n].until){case"undefined":r[n].until=1/0;break;case"string":o=i(r[n].until).startOf("day").valueOf(),r[n].until=o.valueOf()}}return r},Di.erasParse=function(t,e,i){var n,s,o,r,a,l=this.eras();for(t=t.toUpperCase(),n=0,s=l.length;n<s;++n)if(o=l[n].name.toUpperCase(),r=l[n].abbr.toUpperCase(),a=l[n].narrow.toUpperCase(),i)switch(e){case"N":case"NN":case"NNN":if(r===t)return l[n];break;case"NNNN":if(o===t)return l[n];break;case"NNNNN":if(a===t)return l[n]}else if([o,r,a].indexOf(t)>=0)return l[n]},Di.erasConvertYear=function(t,e){var n=t.since<=t.until?1:-1;return void 0===e?i(t.since).year():i(t.since).year()+(e-t.offset)*n},Di.erasAbbrRegex=function(t){return o(this,"_erasAbbrRegex")||pi.call(this),t?this._erasAbbrRegex:this._erasRegex},Di.erasNameRegex=function(t){return o(this,"_erasNameRegex")||pi.call(this),t?this._erasNameRegex:this._erasRegex},Di.erasNarrowRegex=function(t){return o(this,"_erasNarrowRegex")||pi.call(this),t?this._erasNarrowRegex:this._erasRegex},Di.months=function(t,e){return t?n(this._months)?this._months[t.month()]:this._months[(this._months.isFormat||Pt).test(e)?"format":"standalone"][t.month()]:n(this._months)?this._months:this._months.standalone},Di.monthsShort=function(t,e){return t?n(this._monthsShort)?this._monthsShort[t.month()]:this._monthsShort[Pt.test(e)?"format":"standalone"][t.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},Di.monthsParse=function(t,e,i){var n,s,o;if(this._monthsParseExact)return function(t,e,i){var n,s,o,r=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],n=0;n<12;++n)o=d([2e3,n]),this._shortMonthsParse[n]=this.monthsShort(o,"").toLocaleLowerCase(),this._longMonthsParse[n]=this.months(o,"").toLocaleLowerCase();return i?"MMM"===e?-1!==(s=St.call(this._shortMonthsParse,r))?s:null:-1!==(s=St.call(this._longMonthsParse,r))?s:null:"MMM"===e?-1!==(s=St.call(this._shortMonthsParse,r))?s:-1!==(s=St.call(this._longMonthsParse,r))?s:null:-1!==(s=St.call(this._longMonthsParse,r))?s:-1!==(s=St.call(this._shortMonthsParse,r))?s:null}.call(this,t,e,i);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),n=0;n<12;n++){if(s=d([2e3,n]),i&&!this._longMonthsParse[n]&&(this._longMonthsParse[n]=new RegExp("^"+this.months(s,"").replace(".","")+"$","i"),this._shortMonthsParse[n]=new RegExp("^"+this.monthsShort(s,"").replace(".","")+"$","i")),i||this._monthsParse[n]||(o="^"+this.months(s,"")+"|^"+this.monthsShort(s,""),this._monthsParse[n]=new RegExp(o.replace(".",""),"i")),i&&"MMMM"===e&&this._longMonthsParse[n].test(t))return n;if(i&&"MMM"===e&&this._shortMonthsParse[n].test(t))return n;if(!i&&this._monthsParse[n].test(t))return n}},Di.monthsRegex=function(t){return this._monthsParseExact?(o(this,"_monthsRegex")||Wt.call(this),t?this._monthsStrictRegex:this._monthsRegex):(o(this,"_monthsRegex")||(this._monthsRegex=$t),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)},Di.monthsShortRegex=function(t){return this._monthsParseExact?(o(this,"_monthsRegex")||Wt.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(o(this,"_monthsShortRegex")||(this._monthsShortRegex=jt),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)},Di.week=function(t){return Ut(t,this._week.dow,this._week.doy).week},Di.firstDayOfYear=function(){return this._week.doy},Di.firstDayOfWeek=function(){return this._week.dow},Di.weekdays=function(t,e){var i=n(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(e)?"format":"standalone"];return!0===t?zt(i,this._week.dow):t?i[t.day()]:i},Di.weekdaysMin=function(t){return!0===t?zt(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin},Di.weekdaysShort=function(t){return!0===t?zt(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort},Di.weekdaysParse=function(t,e,i){var n,s,o;if(this._weekdaysParseExact)return function(t,e,i){var n,s,o,r=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],n=0;n<7;++n)o=d([2e3,1]).day(n),this._minWeekdaysParse[n]=this.weekdaysMin(o,"").toLocaleLowerCase(),this._shortWeekdaysParse[n]=this.weekdaysShort(o,"").toLocaleLowerCase(),this._weekdaysParse[n]=this.weekdays(o,"").toLocaleLowerCase();return i?"dddd"===e?-1!==(s=St.call(this._weekdaysParse,r))?s:null:"ddd"===e?-1!==(s=St.call(this._shortWeekdaysParse,r))?s:null:-1!==(s=St.call(this._minWeekdaysParse,r))?s:null:"dddd"===e?-1!==(s=St.call(this._weekdaysParse,r))?s:-1!==(s=St.call(this._shortWeekdaysParse,r))?s:-1!==(s=St.call(this._minWeekdaysParse,r))?s:null:"ddd"===e?-1!==(s=St.call(this._shortWeekdaysParse,r))?s:-1!==(s=St.call(this._weekdaysParse,r))?s:-1!==(s=St.call(this._minWeekdaysParse,r))?s:null:-1!==(s=St.call(this._minWeekdaysParse,r))?s:-1!==(s=St.call(this._weekdaysParse,r))?s:-1!==(s=St.call(this._shortWeekdaysParse,r))?s:null}.call(this,t,e,i);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),n=0;n<7;n++){if(s=d([2e3,1]).day(n),i&&!this._fullWeekdaysParse[n]&&(this._fullWeekdaysParse[n]=new RegExp("^"+this.weekdays(s,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[n]=new RegExp("^"+this.weekdaysShort(s,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[n]=new RegExp("^"+this.weekdaysMin(s,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[n]||(o="^"+this.weekdays(s,"")+"|^"+this.weekdaysShort(s,"")+"|^"+this.weekdaysMin(s,""),this._weekdaysParse[n]=new RegExp(o.replace(".",""),"i")),i&&"dddd"===e&&this._fullWeekdaysParse[n].test(t))return n;if(i&&"ddd"===e&&this._shortWeekdaysParse[n].test(t))return n;if(i&&"dd"===e&&this._minWeekdaysParse[n].test(t))return n;if(!i&&this._weekdaysParse[n].test(t))return n}},Di.weekdaysRegex=function(t){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Qt.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(o(this,"_weekdaysRegex")||(this._weekdaysRegex=Xt),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)},Di.weekdaysShortRegex=function(t){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Qt.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(o(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Kt),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},Di.weekdaysMinRegex=function(t){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Qt.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(o(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Zt),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},Di.isPM=function(t){return"p"===(t+"").toLowerCase().charAt(0)},Di.meridiem=function(t,e,i){return t>11?i?"pm":"PM":i?"am":"AM"},he("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===ht(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th")}}),i.lang=x("moment.lang is deprecated. Use moment.locale instead.",he),i.langData=x("moment.langData is deprecated. Use moment.localeData instead.",de);var Mi=Math.abs;function Ei(t,e,i,n){var s=Ve(e,i);return t._milliseconds+=n*s._milliseconds,t._days+=n*s._days,t._months+=n*s._months,t._bubble()}function Ai(t){return t<0?Math.floor(t):Math.ceil(t)}function Oi(t){return 4800*t/146097}function Ni(t){return 146097*t/4800}function Li(t){return function(){return this.as(t)}}var Pi=Li("ms"),ji=Li("s"),$i=Li("m"),Yi=Li("h"),Ii=Li("d"),Wi=Li("w"),Hi=Li("M"),Ri=Li("Q"),Fi=Li("y"),Ui=Pi;function qi(t){return function(){return this.isValid()?this._data[t]:NaN}}var zi=qi("milliseconds"),Vi=qi("seconds"),Bi=qi("minutes"),Gi=qi("hours"),Xi=qi("days"),Ki=qi("months"),Zi=qi("years");var Qi=Math.round,Ji={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};var tn=Math.abs;function en(t){return(t>0)-(t<0)||+t}function nn(){if(!this.isValid())return this.localeData().invalidDate();var t,e,i,n,s,o,r,a,l=tn(this._milliseconds)/1e3,c=tn(this._days),h=tn(this._months),u=this.asSeconds();return u?(t=ct(l/60),e=ct(t/60),l%=60,t%=60,i=ct(h/12),h%=12,n=l?l.toFixed(3).replace(/\.?0+$/,""):"",s=u<0?"-":"",o=en(this._months)!==en(u)?"-":"",r=en(this._days)!==en(u)?"-":"",a=en(this._milliseconds)!==en(u)?"-":"",s+"P"+(i?o+i+"Y":"")+(h?o+h+"M":"")+(c?r+c+"D":"")+(e||t||l?"T":"")+(e?a+e+"H":"")+(t?a+t+"M":"")+(l?a+n+"S":"")):"P0D"}var sn=je.prototype;return sn.isValid=function(){return this._isValid},sn.abs=function(){var t=this._data;return this._milliseconds=Mi(this._milliseconds),this._days=Mi(this._days),this._months=Mi(this._months),t.milliseconds=Mi(t.milliseconds),t.seconds=Mi(t.seconds),t.minutes=Mi(t.minutes),t.hours=Mi(t.hours),t.months=Mi(t.months),t.years=Mi(t.years),this},sn.add=function(t,e){return Ei(this,t,e,1)},sn.subtract=function(t,e){return Ei(this,t,e,-1)},sn.as=function(t){if(!this.isValid())return NaN;var e,i,n=this._milliseconds;if("month"===(t=I(t))||"quarter"===t||"year"===t)switch(e=this._days+n/864e5,i=this._months+Oi(e),t){case"month":return i;case"quarter":return i/3;case"year":return i/12}else switch(e=this._days+Math.round(Ni(this._months)),t){case"week":return e/7+n/6048e5;case"day":return e+n/864e5;case"hour":return 24*e+n/36e5;case"minute":return 1440*e+n/6e4;case"second":return 86400*e+n/1e3;case"millisecond":return Math.floor(864e5*e)+n;default:throw new Error("Unknown unit "+t)}},sn.asMilliseconds=Pi,sn.asSeconds=ji,sn.asMinutes=$i,sn.asHours=Yi,sn.asDays=Ii,sn.asWeeks=Wi,sn.asMonths=Hi,sn.asQuarters=Ri,sn.asYears=Fi,sn.valueOf=Ui,sn._bubble=function(){var t,e,i,n,s,o=this._milliseconds,r=this._days,a=this._months,l=this._data;return o>=0&&r>=0&&a>=0||o<=0&&r<=0&&a<=0||(o+=864e5*Ai(Ni(a)+r),r=0,a=0),l.milliseconds=o%1e3,t=ct(o/1e3),l.seconds=t%60,e=ct(t/60),l.minutes=e%60,i=ct(e/60),l.hours=i%24,r+=ct(i/24),a+=s=ct(Oi(r)),r-=Ai(Ni(s)),n=ct(a/12),a%=12,l.days=r,l.months=a,l.years=n,this},sn.clone=function(){return Ve(this)},sn.get=function(t){return t=I(t),this.isValid()?this[t+"s"]():NaN},sn.milliseconds=zi,sn.seconds=Vi,sn.minutes=Bi,sn.hours=Gi,sn.days=Xi,sn.weeks=function(){return ct(this.days()/7)},sn.months=Ki,sn.years=Zi,sn.humanize=function(t,e){if(!this.isValid())return this.localeData().invalidDate();var i,n,s=!1,o=Ji;return"object"==typeof t&&(e=t,t=!1),"boolean"==typeof t&&(s=t),"object"==typeof e&&(o=Object.assign({},Ji,e),null!=e.s&&null==e.ss&&(o.ss=e.s-1)),n=function(t,e,i,n){var s=Ve(t).abs(),o=Qi(s.as("s")),r=Qi(s.as("m")),a=Qi(s.as("h")),l=Qi(s.as("d")),c=Qi(s.as("M")),h=Qi(s.as("w")),u=Qi(s.as("y")),d=o<=i.ss&&["s",o]||o<i.s&&["ss",o]||r<=1&&["m"]||r<i.m&&["mm",r]||a<=1&&["h"]||a<i.h&&["hh",a]||l<=1&&["d"]||l<i.d&&["dd",l];return null!=i.w&&(d=d||h<=1&&["w"]||h<i.w&&["ww",h]),(d=d||c<=1&&["M"]||c<i.M&&["MM",c]||u<=1&&["y"]||["yy",u])[2]=e,d[3]=+t>0,d[4]=n,function(t,e,i,n,s){return s.relativeTime(e||1,!!i,t,n)}.apply(null,d)}(this,!s,o,i=this.localeData()),s&&(n=i.pastFuture(+this,n)),i.postformat(n)},sn.toISOString=nn,sn.toString=nn,sn.toJSON=nn,sn.locale=ii,sn.localeData=si,sn.toIsoString=x("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",nn),sn.lang=ni,P("X",0,0,"unix"),P("x",0,0,"valueOf"),rt("x",tt),rt("X",/[+-]?\d+(\.\d{1,3})?/),dt("X",function(t,e,i){i._d=new Date(1e3*parseFloat(t))}),dt("x",function(t,e,i){i._d=new Date(ht(t))}),i.version="2.30.1",t=Ae,i.fn=xi,i.min=function(){return Le("isBefore",[].slice.call(arguments,0))},i.max=function(){return Le("isAfter",[].slice.call(arguments,0))},i.now=function(){return Date.now?Date.now():+new Date},i.utc=d,i.unix=function(t){return Ae(1e3*t)},i.months=function(t,e){return Si(t,e,"months")},i.isDate=c,i.locale=he,i.invalid=m,i.duration=Ve,i.isMoment=_,i.weekdays=function(t,e,i){return Ti(t,e,i,"weekdays")},i.parseZone=function(){return Ae.apply(null,arguments).parseZone()},i.localeData=de,i.isDuration=$e,i.monthsShort=function(t,e){return Si(t,e,"monthsShort")},i.weekdaysMin=function(t,e,i){return Ti(t,e,i,"weekdaysMin")},i.defineLocale=ue,i.updateLocale=function(t,e){if(null!=e){var i,n,s=se;null!=oe[t]&&null!=oe[t].parentLocale?oe[t].set(T(oe[t]._config,e)):(null!=(n=ce(t))&&(s=n._config),e=T(s,e),null==n&&(e.abbr=t),(i=new M(e)).parentLocale=oe[t],oe[t]=i),he(t)}else null!=oe[t]&&(null!=oe[t].parentLocale?(oe[t]=oe[t].parentLocale,t===he()&&he(t)):null!=oe[t]&&delete oe[t]);return oe[t]},i.locales=function(){return k(oe)},i.weekdaysShort=function(t,e,i){return Ti(t,e,i,"weekdaysShort")},i.normalizeUnits=I,i.relativeTimeRounding=function(t){return void 0===t?Qi:"function"==typeof t&&(Qi=t,!0)},i.relativeTimeThreshold=function(t,e){return void 0!==Ji[t]&&(void 0===e?Ji[t]:(Ji[t]=e,"s"===t&&(Ji.ss=e-1),!0))},i.calendarFormat=function(t,e){var i=t.diff(e,"days",!0);return i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse"},i.prototype=xi,i.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},i});var SimpleBar=function(){"use strict";var t=function(e,i){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(e,i)},e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},i=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)},n="object"==typeof e&&e&&e.Object===Object&&e,s="object"==typeof self&&self&&self.Object===Object&&self,o=n||s||Function("return this")(),r=o,a=/\s/,l=/^\s+/,c=o.Symbol,h=c,u=Object.prototype,d=u.hasOwnProperty,p=u.toString,f=h?h.toStringTag:void 0,m=Object.prototype.toString,g=function(t){var e=d.call(t,f),i=t[f];try{t[f]=void 0;var n=!0}catch(t){}var s=p.call(t);return n&&(e?t[f]=i:delete t[f]),s},v=c?c.toStringTag:void 0,y=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":v&&v in Object(t)?g(t):function(t){return m.call(t)}(t)},b=function(t){return t?t.slice(0,function(t){for(var e=t.length;e--&&a.test(t.charAt(e)););return e}(t)+1).replace(l,""):t},_=i,w=/^[-+]0x[0-9a-f]+$/i,x=/^0b[01]+$/i,k=/^0o[0-7]+$/i,D=parseInt,C=i,S=function(){return r.Date.now()},T=function(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return null!=t&&"object"==typeof t}(t)&&"[object Symbol]"==y(t)}(t))return NaN;if(_(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=_(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=b(t);var i=x.test(t);return i||k.test(t)?D(t.slice(2),i?2:8):w.test(t)?NaN:+t},M=Math.max,E=Math.min,A=function(t,e,i){var n,s,o,r,a,l,c=0,h=!1,u=!1,d=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function p(e){var i=n,o=s;return n=s=void 0,c=e,r=t.apply(o,i)}function f(t){var i=t-l;return void 0===l||i>=e||i<0||u&&t-c>=o}function m(){var t=S();if(f(t))return g(t);a=setTimeout(m,function(t){var i=e-(t-l);return u?E(i,o-(t-c)):i}(t))}function g(t){return a=void 0,d&&n?p(t):(n=s=void 0,r)}function v(){var t=S(),i=f(t);if(n=arguments,s=this,l=t,i){if(void 0===a)return function(t){return c=t,a=setTimeout(m,e),h?p(t):r}(l);if(u)return clearTimeout(a),a=setTimeout(m,e),p(l)}return void 0===a&&(a=setTimeout(m,e)),r}return e=T(e)||0,C(i)&&(h=!!i.leading,o=(u="maxWait"in i)?M(T(i.maxWait)||0,e):o,d="trailing"in i?!!i.trailing:d),v.cancel=function(){void 0!==a&&clearTimeout(a),c=0,n=l=s=a=void 0},v.flush=function(){return void 0===a?r:g(S())},v},O=A,N=i,L=function(t,e,i){var n=!0,s=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return N(i)&&(n="leading"in i?!!i.leading:n,s="trailing"in i?!!i.trailing:s),O(t,e,{leading:n,maxWait:e,trailing:s})},P=function(){return(P=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var s in e=arguments[i])Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t}).apply(this,arguments)};function j(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView?t.ownerDocument.defaultView:window}function $(t){return t&&t.ownerDocument?t.ownerDocument:document}var Y=function(t){return Array.prototype.reduce.call(t,function(t,e){var i=e.name.match(/data-simplebar-(.+)/);if(i){var n=i[1].replace(/\W+(.)/g,function(t,e){return e.toUpperCase()});switch(e.value){case"true":t[n]=!0;break;case"false":t[n]=!1;break;case void 0:t[n]=!0;break;default:t[n]=e.value}}return t},{})};function I(t,e){var i;t&&(i=t.classList).add.apply(i,e.split(" "))}function W(t,e){t&&e.split(" ").forEach(function(e){t.classList.remove(e)})}function H(t){return".".concat(t.split(" ").join("."))}var R=!("undefined"==typeof window||!window.document||!window.document.createElement),F=Object.freeze({__proto__:null,addClasses:I,canUseDOM:R,classNamesToQuery:H,getElementDocument:$,getElementWindow:j,getOptions:Y,removeClasses:W}),U=null,q=null;function z(){if(null===U){if("undefined"==typeof document)return U=0;var t=document.body,e=document.createElement("div");e.classList.add("simplebar-hide-scrollbar"),t.appendChild(e);var i=e.getBoundingClientRect().right;t.removeChild(e),U=i}return U}R&&window.addEventListener("resize",function(){q!==window.devicePixelRatio&&(q=window.devicePixelRatio,U=null)});var V=j,B=$,G=Y,X=I,K=W,Z=H,Q=function(){function t(e,i){void 0===i&&(i={});var n=this;if(this.removePreventClickId=null,this.minScrollbarWidth=20,this.stopScrollDelay=175,this.isScrolling=!1,this.isMouseEntering=!1,this.isDragging=!1,this.scrollXTicking=!1,this.scrollYTicking=!1,this.wrapperEl=null,this.contentWrapperEl=null,this.contentEl=null,this.offsetEl=null,this.maskEl=null,this.placeholderEl=null,this.heightAutoObserverWrapperEl=null,this.heightAutoObserverEl=null,this.rtlHelpers=null,this.scrollbarWidth=0,this.resizeObserver=null,this.mutationObserver=null,this.elStyles=null,this.isRtl=null,this.mouseX=0,this.mouseY=0,this.onMouseMove=function(){},this.onWindowResize=function(){},this.onStopScrolling=function(){},this.onMouseEntered=function(){},this.onScroll=function(){var t=V(n.el);n.scrollXTicking||(t.requestAnimationFrame(n.scrollX),n.scrollXTicking=!0),n.scrollYTicking||(t.requestAnimationFrame(n.scrollY),n.scrollYTicking=!0),n.isScrolling||(n.isScrolling=!0,X(n.el,n.classNames.scrolling)),n.showScrollbar("x"),n.showScrollbar("y"),n.onStopScrolling()},this.scrollX=function(){n.axis.x.isOverflowing&&n.positionScrollbar("x"),n.scrollXTicking=!1},this.scrollY=function(){n.axis.y.isOverflowing&&n.positionScrollbar("y"),n.scrollYTicking=!1},this._onStopScrolling=function(){K(n.el,n.classNames.scrolling),n.options.autoHide&&(n.hideScrollbar("x"),n.hideScrollbar("y")),n.isScrolling=!1},this.onMouseEnter=function(){n.isMouseEntering||(X(n.el,n.classNames.mouseEntered),n.showScrollbar("x"),n.showScrollbar("y"),n.isMouseEntering=!0),n.onMouseEntered()},this._onMouseEntered=function(){K(n.el,n.classNames.mouseEntered),n.options.autoHide&&(n.hideScrollbar("x"),n.hideScrollbar("y")),n.isMouseEntering=!1},this._onMouseMove=function(t){n.mouseX=t.clientX,n.mouseY=t.clientY,(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&n.onMouseMoveForAxis("x"),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&n.onMouseMoveForAxis("y")},this.onMouseLeave=function(){n.onMouseMove.cancel(),(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&n.onMouseLeaveForAxis("x"),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&n.onMouseLeaveForAxis("y"),n.mouseX=-1,n.mouseY=-1},this._onWindowResize=function(){n.scrollbarWidth=n.getScrollbarWidth(),n.hideNativeScrollbar()},this.onPointerEvent=function(t){var e,i;n.axis.x.track.el&&n.axis.y.track.el&&n.axis.x.scrollbar.el&&n.axis.y.scrollbar.el&&(n.axis.x.track.rect=n.axis.x.track.el.getBoundingClientRect(),n.axis.y.track.rect=n.axis.y.track.el.getBoundingClientRect(),(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&(e=n.isWithinBounds(n.axis.x.track.rect)),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&(i=n.isWithinBounds(n.axis.y.track.rect)),(e||i)&&(t.stopPropagation(),"pointerdown"===t.type&&"touch"!==t.pointerType&&(e&&(n.axis.x.scrollbar.rect=n.axis.x.scrollbar.el.getBoundingClientRect(),n.isWithinBounds(n.axis.x.scrollbar.rect)?n.onDragStart(t,"x"):n.onTrackClick(t,"x")),i&&(n.axis.y.scrollbar.rect=n.axis.y.scrollbar.el.getBoundingClientRect(),n.isWithinBounds(n.axis.y.scrollbar.rect)?n.onDragStart(t,"y"):n.onTrackClick(t,"y")))))},this.drag=function(e){var i,s,o,r,a,l,c,h,u,d,p;if(n.draggedAxis&&n.contentWrapperEl){var f=n.axis[n.draggedAxis].track,m=null!==(s=null===(i=f.rect)||void 0===i?void 0:i[n.axis[n.draggedAxis].sizeAttr])&&void 0!==s?s:0,g=n.axis[n.draggedAxis].scrollbar,v=null!==(r=null===(o=n.contentWrapperEl)||void 0===o?void 0:o[n.axis[n.draggedAxis].scrollSizeAttr])&&void 0!==r?r:0,y=parseInt(null!==(l=null===(a=n.elStyles)||void 0===a?void 0:a[n.axis[n.draggedAxis].sizeAttr])&&void 0!==l?l:"0px",10);e.preventDefault(),e.stopPropagation();var b=("y"===n.draggedAxis?e.pageY:e.pageX)-(null!==(h=null===(c=f.rect)||void 0===c?void 0:c[n.axis[n.draggedAxis].offsetAttr])&&void 0!==h?h:0)-n.axis[n.draggedAxis].dragOffset,_=(b="x"===n.draggedAxis&&n.isRtl?(null!==(d=null===(u=f.rect)||void 0===u?void 0:u[n.axis[n.draggedAxis].sizeAttr])&&void 0!==d?d:0)-g.size-b:b)/(m-g.size)*(v-y);"x"===n.draggedAxis&&n.isRtl&&(_=(null===(p=t.getRtlHelpers())||void 0===p?void 0:p.isScrollingToNegative)?-_:_),n.contentWrapperEl[n.axis[n.draggedAxis].scrollOffsetAttr]=_}},this.onEndDrag=function(t){n.isDragging=!1;var e=B(n.el),i=V(n.el);t.preventDefault(),t.stopPropagation(),K(n.el,n.classNames.dragging),n.onStopScrolling(),e.removeEventListener("mousemove",n.drag,!0),e.removeEventListener("mouseup",n.onEndDrag,!0),n.removePreventClickId=i.setTimeout(function(){e.removeEventListener("click",n.preventClick,!0),e.removeEventListener("dblclick",n.preventClick,!0),n.removePreventClickId=null})},this.preventClick=function(t){t.preventDefault(),t.stopPropagation()},this.el=e,this.options=P(P({},t.defaultOptions),i),this.classNames=P(P({},t.defaultOptions.classNames),i.classNames),this.axis={x:{scrollOffsetAttr:"scrollLeft",sizeAttr:"width",scrollSizeAttr:"scrollWidth",offsetSizeAttr:"offsetWidth",offsetAttr:"left",overflowAttr:"overflowX",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}},y:{scrollOffsetAttr:"scrollTop",sizeAttr:"height",scrollSizeAttr:"scrollHeight",offsetSizeAttr:"offsetHeight",offsetAttr:"top",overflowAttr:"overflowY",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}}},"object"!=typeof this.el||!this.el.nodeName)throw new Error("Argument passed to SimpleBar must be an HTML element instead of ".concat(this.el));this.onMouseMove=L(this._onMouseMove,64),this.onWindowResize=A(this._onWindowResize,64,{leading:!0}),this.onStopScrolling=A(this._onStopScrolling,this.stopScrollDelay),this.onMouseEntered=A(this._onMouseEntered,this.stopScrollDelay),this.init()}return t.getRtlHelpers=function(){if(t.rtlHelpers)return t.rtlHelpers;var e=document.createElement("div");e.innerHTML='<div class="simplebar-dummy-scrollbar-size"><div></div></div>';var i=e.firstElementChild,n=null==i?void 0:i.firstElementChild;if(!n)return null;document.body.appendChild(i),i.scrollLeft=0;var s=t.getOffset(i),o=t.getOffset(n);i.scrollLeft=-999;var r=t.getOffset(n);return document.body.removeChild(i),t.rtlHelpers={isScrollOriginAtZero:s.left!==o.left,isScrollingToNegative:o.left!==r.left},t.rtlHelpers},t.prototype.getScrollbarWidth=function(){try{return this.contentWrapperEl&&"none"===getComputedStyle(this.contentWrapperEl,"::-webkit-scrollbar").display||"scrollbarWidth"in document.documentElement.style||"-ms-overflow-style"in document.documentElement.style?0:z()}catch(t){return z()}},t.getOffset=function(t){var e=t.getBoundingClientRect(),i=B(t),n=V(t);return{top:e.top+(n.pageYOffset||i.documentElement.scrollTop),left:e.left+(n.pageXOffset||i.documentElement.scrollLeft)}},t.prototype.init=function(){R&&(this.initDOM(),this.rtlHelpers=t.getRtlHelpers(),this.scrollbarWidth=this.getScrollbarWidth(),this.recalculate(),this.initListeners())},t.prototype.initDOM=function(){var t,e;this.wrapperEl=this.el.querySelector(Z(this.classNames.wrapper)),this.contentWrapperEl=this.options.scrollableNode||this.el.querySelector(Z(this.classNames.contentWrapper)),this.contentEl=this.options.contentNode||this.el.querySelector(Z(this.classNames.contentEl)),this.offsetEl=this.el.querySelector(Z(this.classNames.offset)),this.maskEl=this.el.querySelector(Z(this.classNames.mask)),this.placeholderEl=this.findChild(this.wrapperEl,Z(this.classNames.placeholder)),this.heightAutoObserverWrapperEl=this.el.querySelector(Z(this.classNames.heightAutoObserverWrapperEl)),this.heightAutoObserverEl=this.el.querySelector(Z(this.classNames.heightAutoObserverEl)),this.axis.x.track.el=this.findChild(this.el,"".concat(Z(this.classNames.track)).concat(Z(this.classNames.horizontal))),this.axis.y.track.el=this.findChild(this.el,"".concat(Z(this.classNames.track)).concat(Z(this.classNames.vertical))),this.axis.x.scrollbar.el=(null===(t=this.axis.x.track.el)||void 0===t?void 0:t.querySelector(Z(this.classNames.scrollbar)))||null,this.axis.y.scrollbar.el=(null===(e=this.axis.y.track.el)||void 0===e?void 0:e.querySelector(Z(this.classNames.scrollbar)))||null,this.options.autoHide||(X(this.axis.x.scrollbar.el,this.classNames.visible),X(this.axis.y.scrollbar.el,this.classNames.visible))},t.prototype.initListeners=function(){var t,e=this,i=V(this.el);if(this.el.addEventListener("mouseenter",this.onMouseEnter),this.el.addEventListener("pointerdown",this.onPointerEvent,!0),this.el.addEventListener("mousemove",this.onMouseMove),this.el.addEventListener("mouseleave",this.onMouseLeave),null===(t=this.contentWrapperEl)||void 0===t||t.addEventListener("scroll",this.onScroll),i.addEventListener("resize",this.onWindowResize),this.contentEl){if(window.ResizeObserver){var n=!1,s=i.ResizeObserver||ResizeObserver;this.resizeObserver=new s(function(){n&&i.requestAnimationFrame(function(){e.recalculate()})}),this.resizeObserver.observe(this.el),this.resizeObserver.observe(this.contentEl),i.requestAnimationFrame(function(){n=!0})}this.mutationObserver=new i.MutationObserver(function(){i.requestAnimationFrame(function(){e.recalculate()})}),this.mutationObserver.observe(this.contentEl,{childList:!0,subtree:!0,characterData:!0})}},t.prototype.recalculate=function(){if(this.heightAutoObserverEl&&this.contentEl&&this.contentWrapperEl&&this.wrapperEl&&this.placeholderEl){var t=V(this.el);this.elStyles=t.getComputedStyle(this.el),this.isRtl="rtl"===this.elStyles.direction;var e=this.contentEl.offsetWidth,i=this.heightAutoObserverEl.offsetHeight<=1,n=this.heightAutoObserverEl.offsetWidth<=1||e>0,s=this.contentWrapperEl.offsetWidth,o=this.elStyles.overflowX,r=this.elStyles.overflowY;this.contentEl.style.padding="".concat(this.elStyles.paddingTop," ").concat(this.elStyles.paddingRight," ").concat(this.elStyles.paddingBottom," ").concat(this.elStyles.paddingLeft),this.wrapperEl.style.margin="-".concat(this.elStyles.paddingTop," -").concat(this.elStyles.paddingRight," -").concat(this.elStyles.paddingBottom," -").concat(this.elStyles.paddingLeft);var a=this.contentEl.scrollHeight,l=this.contentEl.scrollWidth;this.contentWrapperEl.style.height=i?"auto":"100%",this.placeholderEl.style.width=n?"".concat(e||l,"px"):"auto",this.placeholderEl.style.height="".concat(a,"px");var c=this.contentWrapperEl.offsetHeight;this.axis.x.isOverflowing=0!==e&&l>e,this.axis.y.isOverflowing=a>c,this.axis.x.isOverflowing="hidden"!==o&&this.axis.x.isOverflowing,this.axis.y.isOverflowing="hidden"!==r&&this.axis.y.isOverflowing,this.axis.x.forceVisible="x"===this.options.forceVisible||!0===this.options.forceVisible,this.axis.y.forceVisible="y"===this.options.forceVisible||!0===this.options.forceVisible,this.hideNativeScrollbar();var h=this.axis.x.isOverflowing?this.scrollbarWidth:0,u=this.axis.y.isOverflowing?this.scrollbarWidth:0;this.axis.x.isOverflowing=this.axis.x.isOverflowing&&l>s-u,this.axis.y.isOverflowing=this.axis.y.isOverflowing&&a>c-h,this.axis.x.scrollbar.size=this.getScrollbarSize("x"),this.axis.y.scrollbar.size=this.getScrollbarSize("y"),this.axis.x.scrollbar.el&&(this.axis.x.scrollbar.el.style.width="".concat(this.axis.x.scrollbar.size,"px")),this.axis.y.scrollbar.el&&(this.axis.y.scrollbar.el.style.height="".concat(this.axis.y.scrollbar.size,"px")),this.positionScrollbar("x"),this.positionScrollbar("y"),this.toggleTrackVisibility("x"),this.toggleTrackVisibility("y")}},t.prototype.getScrollbarSize=function(t){var e,i;if(void 0===t&&(t="y"),!this.axis[t].isOverflowing||!this.contentEl)return 0;var n,s=this.contentEl[this.axis[t].scrollSizeAttr],o=null!==(i=null===(e=this.axis[t].track.el)||void 0===e?void 0:e[this.axis[t].offsetSizeAttr])&&void 0!==i?i:0,r=o/s;return n=Math.max(~~(r*o),this.options.scrollbarMinSize),this.options.scrollbarMaxSize&&(n=Math.min(n,this.options.scrollbarMaxSize)),n},t.prototype.positionScrollbar=function(e){var i,n,s;void 0===e&&(e="y");var o=this.axis[e].scrollbar;if(this.axis[e].isOverflowing&&this.contentWrapperEl&&o.el&&this.elStyles){var r=this.contentWrapperEl[this.axis[e].scrollSizeAttr],a=(null===(i=this.axis[e].track.el)||void 0===i?void 0:i[this.axis[e].offsetSizeAttr])||0,l=parseInt(this.elStyles[this.axis[e].sizeAttr],10),c=this.contentWrapperEl[this.axis[e].scrollOffsetAttr];c="x"===e&&this.isRtl&&(null===(n=t.getRtlHelpers())||void 0===n?void 0:n.isScrollOriginAtZero)?-c:c,"x"===e&&this.isRtl&&(c=(null===(s=t.getRtlHelpers())||void 0===s?void 0:s.isScrollingToNegative)?c:-c);var h=c/(r-l),u=~~((a-o.size)*h);u="x"===e&&this.isRtl?-u+(a-o.size):u,o.el.style.transform="x"===e?"translate3d(".concat(u,"px, 0, 0)"):"translate3d(0, ".concat(u,"px, 0)")}},t.prototype.toggleTrackVisibility=function(t){void 0===t&&(t="y");var e=this.axis[t].track.el,i=this.axis[t].scrollbar.el;e&&i&&this.contentWrapperEl&&(this.axis[t].isOverflowing||this.axis[t].forceVisible?(e.style.visibility="visible",this.contentWrapperEl.style[this.axis[t].overflowAttr]="scroll",this.el.classList.add("".concat(this.classNames.scrollable,"-").concat(t))):(e.style.visibility="hidden",this.contentWrapperEl.style[this.axis[t].overflowAttr]="hidden",this.el.classList.remove("".concat(this.classNames.scrollable,"-").concat(t))),this.axis[t].isOverflowing?i.style.display="block":i.style.display="none")},t.prototype.showScrollbar=function(t){void 0===t&&(t="y"),this.axis[t].isOverflowing&&!this.axis[t].scrollbar.isVisible&&(X(this.axis[t].scrollbar.el,this.classNames.visible),this.axis[t].scrollbar.isVisible=!0)},t.prototype.hideScrollbar=function(t){void 0===t&&(t="y"),this.isDragging||this.axis[t].isOverflowing&&this.axis[t].scrollbar.isVisible&&(K(this.axis[t].scrollbar.el,this.classNames.visible),this.axis[t].scrollbar.isVisible=!1)},t.prototype.hideNativeScrollbar=function(){this.offsetEl&&(this.offsetEl.style[this.isRtl?"left":"right"]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px",this.offsetEl.style.bottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px")},t.prototype.onMouseMoveForAxis=function(t){void 0===t&&(t="y");var e=this.axis[t];e.track.el&&e.scrollbar.el&&(e.track.rect=e.track.el.getBoundingClientRect(),e.scrollbar.rect=e.scrollbar.el.getBoundingClientRect(),this.isWithinBounds(e.track.rect)?(this.showScrollbar(t),X(e.track.el,this.classNames.hover),this.isWithinBounds(e.scrollbar.rect)?X(e.scrollbar.el,this.classNames.hover):K(e.scrollbar.el,this.classNames.hover)):(K(e.track.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(t)))},t.prototype.onMouseLeaveForAxis=function(t){void 0===t&&(t="y"),K(this.axis[t].track.el,this.classNames.hover),K(this.axis[t].scrollbar.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(t)},t.prototype.onDragStart=function(t,e){var i;void 0===e&&(e="y"),this.isDragging=!0;var n=B(this.el),s=V(this.el),o=this.axis[e].scrollbar,r="y"===e?t.pageY:t.pageX;this.axis[e].dragOffset=r-((null===(i=o.rect)||void 0===i?void 0:i[this.axis[e].offsetAttr])||0),this.draggedAxis=e,X(this.el,this.classNames.dragging),n.addEventListener("mousemove",this.drag,!0),n.addEventListener("mouseup",this.onEndDrag,!0),null===this.removePreventClickId?(n.addEventListener("click",this.preventClick,!0),n.addEventListener("dblclick",this.preventClick,!0)):(s.clearTimeout(this.removePreventClickId),this.removePreventClickId=null)},t.prototype.onTrackClick=function(t,e){var i,n,s,o,r=this;void 0===e&&(e="y");var a=this.axis[e];if(this.options.clickOnTrack&&a.scrollbar.el&&this.contentWrapperEl){t.preventDefault();var l=V(this.el);this.axis[e].scrollbar.rect=a.scrollbar.el.getBoundingClientRect();var c=null!==(n=null===(i=this.axis[e].scrollbar.rect)||void 0===i?void 0:i[this.axis[e].offsetAttr])&&void 0!==n?n:0,h=parseInt(null!==(o=null===(s=this.elStyles)||void 0===s?void 0:s[this.axis[e].sizeAttr])&&void 0!==o?o:"0px",10),u=this.contentWrapperEl[this.axis[e].scrollOffsetAttr],d=("y"===e?this.mouseY-c:this.mouseX-c)<0?-1:1,p=-1===d?u-h:u+h,f=function(){r.contentWrapperEl&&(-1===d?u>p&&(u-=40,r.contentWrapperEl[r.axis[e].scrollOffsetAttr]=u,l.requestAnimationFrame(f)):u<p&&(u+=40,r.contentWrapperEl[r.axis[e].scrollOffsetAttr]=u,l.requestAnimationFrame(f)))};f()}},t.prototype.getContentElement=function(){return this.contentEl},t.prototype.getScrollElement=function(){return this.contentWrapperEl},t.prototype.removeListeners=function(){var t=V(this.el);this.el.removeEventListener("mouseenter",this.onMouseEnter),this.el.removeEventListener("pointerdown",this.onPointerEvent,!0),this.el.removeEventListener("mousemove",this.onMouseMove),this.el.removeEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl&&this.contentWrapperEl.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onWindowResize),this.mutationObserver&&this.mutationObserver.disconnect(),this.resizeObserver&&this.resizeObserver.disconnect(),this.onMouseMove.cancel(),this.onWindowResize.cancel(),this.onStopScrolling.cancel(),this.onMouseEntered.cancel()},t.prototype.unMount=function(){this.removeListeners()},t.prototype.isWithinBounds=function(t){return this.mouseX>=t.left&&this.mouseX<=t.left+t.width&&this.mouseY>=t.top&&this.mouseY<=t.top+t.height},t.prototype.findChild=function(t,e){var i=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector;return Array.prototype.filter.call(t.children,function(t){return i.call(t,e)})[0]},t.rtlHelpers=null,t.defaultOptions={forceVisible:!1,clickOnTrack:!0,scrollbarMinSize:25,scrollbarMaxSize:0,ariaLabel:"scrollable content",tabIndex:0,classNames:{contentEl:"simplebar-content",contentWrapper:"simplebar-content-wrapper",offset:"simplebar-offset",mask:"simplebar-mask",wrapper:"simplebar-wrapper",placeholder:"simplebar-placeholder",scrollbar:"simplebar-scrollbar",track:"simplebar-track",heightAutoObserverWrapperEl:"simplebar-height-auto-observer-wrapper",heightAutoObserverEl:"simplebar-height-auto-observer",visible:"simplebar-visible",horizontal:"simplebar-horizontal",vertical:"simplebar-vertical",hover:"simplebar-hover",dragging:"simplebar-dragging",scrolling:"simplebar-scrolling",scrollable:"simplebar-scrollable",mouseEntered:"simplebar-mouse-entered"},scrollableNode:null,contentNode:null,autoHide:!0},t.getOptions=G,t.helpers=F,t}(),J=Q.helpers,tt=J.getOptions,et=J.addClasses,it=J.canUseDOM,nt=function(e){function i(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var s=e.apply(this,t)||this;return i.instances.set(t[0],s),s}return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}(i,e),i.initDOMLoadedElements=function(){document.removeEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.removeEventListener("load",this.initDOMLoadedElements),Array.prototype.forEach.call(document.querySelectorAll("[data-simplebar]"),function(t){"init"===t.getAttribute("data-simplebar")||i.instances.has(t)||new i(t,tt(t.attributes))})},i.removeObserver=function(){var t;null===(t=i.globalObserver)||void 0===t||t.disconnect()},i.prototype.initDOM=function(){var t,e,i,n=this;if(!Array.prototype.filter.call(this.el.children,function(t){return t.classList.contains(n.classNames.wrapper)}).length){for(this.wrapperEl=document.createElement("div"),this.contentWrapperEl=document.createElement("div"),this.offsetEl=document.createElement("div"),this.maskEl=document.createElement("div"),this.contentEl=document.createElement("div"),this.placeholderEl=document.createElement("div"),this.heightAutoObserverWrapperEl=document.createElement("div"),this.heightAutoObserverEl=document.createElement("div"),et(this.wrapperEl,this.classNames.wrapper),et(this.contentWrapperEl,this.classNames.contentWrapper),et(this.offsetEl,this.classNames.offset),et(this.maskEl,this.classNames.mask),et(this.contentEl,this.classNames.contentEl),et(this.placeholderEl,this.classNames.placeholder),et(this.heightAutoObserverWrapperEl,this.classNames.heightAutoObserverWrapperEl),et(this.heightAutoObserverEl,this.classNames.heightAutoObserverEl);this.el.firstChild;)this.contentEl.appendChild(this.el.firstChild);this.contentWrapperEl.appendChild(this.contentEl),this.offsetEl.appendChild(this.contentWrapperEl),this.maskEl.appendChild(this.offsetEl),this.heightAutoObserverWrapperEl.appendChild(this.heightAutoObserverEl),this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl),this.wrapperEl.appendChild(this.maskEl),this.wrapperEl.appendChild(this.placeholderEl),this.el.appendChild(this.wrapperEl),null===(t=this.contentWrapperEl)||void 0===t||t.setAttribute("tabindex",this.options.tabIndex.toString()),null===(e=this.contentWrapperEl)||void 0===e||e.setAttribute("role","region"),null===(i=this.contentWrapperEl)||void 0===i||i.setAttribute("aria-label",this.options.ariaLabel)}if(!this.axis.x.track.el||!this.axis.y.track.el){var s=document.createElement("div"),o=document.createElement("div");et(s,this.classNames.track),et(o,this.classNames.scrollbar),s.appendChild(o),this.axis.x.track.el=s.cloneNode(!0),et(this.axis.x.track.el,this.classNames.horizontal),this.axis.y.track.el=s.cloneNode(!0),et(this.axis.y.track.el,this.classNames.vertical),this.el.appendChild(this.axis.x.track.el),this.el.appendChild(this.axis.y.track.el)}Q.prototype.initDOM.call(this),this.el.setAttribute("data-simplebar","init")},i.prototype.unMount=function(){Q.prototype.unMount.call(this),i.instances.delete(this.el)},i.initHtmlApi=function(){this.initDOMLoadedElements=this.initDOMLoadedElements.bind(this),"undefined"!=typeof MutationObserver&&(this.globalObserver=new MutationObserver(i.handleMutations),this.globalObserver.observe(document,{childList:!0,subtree:!0})),"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?window.setTimeout(this.initDOMLoadedElements):(document.addEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.addEventListener("load",this.initDOMLoadedElements))},i.handleMutations=function(t){t.forEach(function(t){t.addedNodes.forEach(function(t){1===t.nodeType&&(t.hasAttribute("data-simplebar")?!i.instances.has(t)&&document.documentElement.contains(t)&&new i(t,tt(t.attributes)):t.querySelectorAll("[data-simplebar]").forEach(function(t){"init"!==t.getAttribute("data-simplebar")&&!i.instances.has(t)&&document.documentElement.contains(t)&&new i(t,tt(t.attributes))}))}),t.removedNodes.forEach(function(t){var e;1===t.nodeType&&("init"===t.getAttribute("data-simplebar")?!document.documentElement.contains(t)&&(null===(e=i.instances.get(t))||void 0===e||e.unMount()):Array.prototype.forEach.call(t.querySelectorAll('[data-simplebar="init"]'),function(t){var e;!document.documentElement.contains(t)&&(null===(e=i.instances.get(t))||void 0===e||e.unMount())}))})})},i.instances=new WeakMap,i}(Q);return it&&nt.initHtmlApi(),nt}();!function(t,e){if("function"==typeof define&&define.amd)define(["moment","jquery"],function(t,i){return i.fn||(i.fn={}),"function"!=typeof t&&t.hasOwnProperty("default")&&(t=t.default),e(t,i)});else if("object"==typeof module&&module.exports){var i="undefined"!=typeof window?window.jQuery:void 0;i||(i=require("jquery")).fn||(i.fn={});var n="undefined"!=typeof window&&void 0!==window.moment?window.moment:require("moment");module.exports=e(n,i)}else t.daterangepicker=e(t.moment,t.jQuery)}(this,function(t,e){var i=function(i,n,s){if(this.parentEl="body",this.element=e(i),this.startDate=t().startOf("day"),this.endDate=t().endOf("day"),this.minDate=!1,this.maxDate=!1,this.maxSpan=!1,this.autoApply=!1,this.singleDatePicker=!1,this.showDropdowns=!1,this.minYear=t().subtract(100,"year").format("YYYY"),this.maxYear=t().add(100,"year").format("YYYY"),this.showWeekNumbers=!1,this.showISOWeekNumbers=!1,this.showCustomRangeLabel=!0,this.timePicker=!1,this.timePicker24Hour=!1,this.timePickerIncrement=1,this.timePickerSeconds=!1,this.linkedCalendars=!0,this.autoUpdateInput=!0,this.alwaysShowCalendars=!1,this.ranges={},this.opens="right",this.element.hasClass("pull-right")&&(this.opens="left"),this.drops="down",this.element.hasClass("dropup")&&(this.drops="up"),this.buttonClasses="btn btn-sm",this.applyButtonClasses="btn-primary",this.cancelButtonClasses="btn-default",this.locale={direction:"ltr",format:t.localeData().longDateFormat("L"),separator:" - ",applyLabel:"Apply",cancelLabel:"Cancel",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:t.weekdaysMin(),monthNames:t.monthsShort(),firstDay:t.localeData().firstDayOfWeek()},this.callback=function(){},this.isShowing=!1,this.leftCalendar={},this.rightCalendar={},"object"==typeof n&&null!==n||(n={}),"string"==typeof(n=e.extend(this.element.data(),n)).template||n.template instanceof e||(n.template='<div class="daterangepicker"><div class="ranges"></div><div class="drp-calendar left"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-calendar right"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-buttons"><span class="drp-selected"></span><button class="cancelBtn" type="button"></button><button class="applyBtn" disabled="disabled" type="button"></button> </div></div>'),this.parentEl=n.parentEl&&e(n.parentEl).length?e(n.parentEl):e(this.parentEl),this.container=e(n.template).appendTo(this.parentEl),"object"==typeof n.locale&&("string"==typeof n.locale.direction&&(this.locale.direction=n.locale.direction),"string"==typeof n.locale.format&&(this.locale.format=n.locale.format),"string"==typeof n.locale.separator&&(this.locale.separator=n.locale.separator),"object"==typeof n.locale.daysOfWeek&&(this.locale.daysOfWeek=n.locale.daysOfWeek.slice()),"object"==typeof n.locale.monthNames&&(this.locale.monthNames=n.locale.monthNames.slice()),"number"==typeof n.locale.firstDay&&(this.locale.firstDay=n.locale.firstDay),"string"==typeof n.locale.applyLabel&&(this.locale.applyLabel=n.locale.applyLabel),"string"==typeof n.locale.cancelLabel&&(this.locale.cancelLabel=n.locale.cancelLabel),"string"==typeof n.locale.weekLabel&&(this.locale.weekLabel=n.locale.weekLabel),"string"==typeof n.locale.customRangeLabel)){(p=document.createElement("textarea")).innerHTML=n.locale.customRangeLabel;var o=p.value;this.locale.customRangeLabel=o}if(this.container.addClass(this.locale.direction),"string"==typeof n.startDate&&(this.startDate=t(n.startDate,this.locale.format)),"string"==typeof n.endDate&&(this.endDate=t(n.endDate,this.locale.format)),"string"==typeof n.minDate&&(this.minDate=t(n.minDate,this.locale.format)),"string"==typeof n.maxDate&&(this.maxDate=t(n.maxDate,this.locale.format)),"object"==typeof n.startDate&&(this.startDate=t(n.startDate)),"object"==typeof n.endDate&&(this.endDate=t(n.endDate)),"object"==typeof n.minDate&&(this.minDate=t(n.minDate)),"object"==typeof n.maxDate&&(this.maxDate=t(n.maxDate)),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),"string"==typeof n.applyButtonClasses&&(this.applyButtonClasses=n.applyButtonClasses),"string"==typeof n.applyClass&&(this.applyButtonClasses=n.applyClass),"string"==typeof n.cancelButtonClasses&&(this.cancelButtonClasses=n.cancelButtonClasses),"string"==typeof n.cancelClass&&(this.cancelButtonClasses=n.cancelClass),"object"==typeof n.maxSpan&&(this.maxSpan=n.maxSpan),"object"==typeof n.dateLimit&&(this.maxSpan=n.dateLimit),"string"==typeof n.opens&&(this.opens=n.opens),"string"==typeof n.drops&&(this.drops=n.drops),"boolean"==typeof n.showWeekNumbers&&(this.showWeekNumbers=n.showWeekNumbers),"boolean"==typeof n.showISOWeekNumbers&&(this.showISOWeekNumbers=n.showISOWeekNumbers),"string"==typeof n.buttonClasses&&(this.buttonClasses=n.buttonClasses),"object"==typeof n.buttonClasses&&(this.buttonClasses=n.buttonClasses.join(" ")),"boolean"==typeof n.showDropdowns&&(this.showDropdowns=n.showDropdowns),"number"==typeof n.minYear&&(this.minYear=n.minYear),"number"==typeof n.maxYear&&(this.maxYear=n.maxYear),"boolean"==typeof n.showCustomRangeLabel&&(this.showCustomRangeLabel=n.showCustomRangeLabel),"boolean"==typeof n.singleDatePicker&&(this.singleDatePicker=n.singleDatePicker,this.singleDatePicker&&(this.endDate=this.startDate.clone())),"boolean"==typeof n.timePicker&&(this.timePicker=n.timePicker),"boolean"==typeof n.timePickerSeconds&&(this.timePickerSeconds=n.timePickerSeconds),"number"==typeof n.timePickerIncrement&&(this.timePickerIncrement=n.timePickerIncrement),"boolean"==typeof n.timePicker24Hour&&(this.timePicker24Hour=n.timePicker24Hour),"boolean"==typeof n.autoApply&&(this.autoApply=n.autoApply),"boolean"==typeof n.autoUpdateInput&&(this.autoUpdateInput=n.autoUpdateInput),"boolean"==typeof n.linkedCalendars&&(this.linkedCalendars=n.linkedCalendars),"function"==typeof n.isInvalidDate&&(this.isInvalidDate=n.isInvalidDate),"function"==typeof n.isCustomDate&&(this.isCustomDate=n.isCustomDate),"boolean"==typeof n.alwaysShowCalendars&&(this.alwaysShowCalendars=n.alwaysShowCalendars),0!=this.locale.firstDay)for(var r=this.locale.firstDay;r>0;)this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift()),r--;var a,l,c;if(void 0===n.startDate&&void 0===n.endDate&&e(this.element).is(":text")){var h=e(this.element).val(),u=h.split(this.locale.separator);a=l=null,2==u.length?(a=t(u[0],this.locale.format),l=t(u[1],this.locale.format)):this.singleDatePicker&&""!==h&&(a=t(h,this.locale.format),l=t(h,this.locale.format)),null!==a&&null!==l&&(this.setStartDate(a),this.setEndDate(l))}if("object"==typeof n.ranges){for(c in n.ranges){a="string"==typeof n.ranges[c][0]?t(n.ranges[c][0],this.locale.format):t(n.ranges[c][0]),l="string"==typeof n.ranges[c][1]?t(n.ranges[c][1],this.locale.format):t(n.ranges[c][1]),this.minDate&&a.isBefore(this.minDate)&&(a=this.minDate.clone());var d=this.maxDate;if(this.maxSpan&&d&&a.clone().add(this.maxSpan).isAfter(d)&&(d=a.clone().add(this.maxSpan)),d&&l.isAfter(d)&&(l=d.clone()),!(this.minDate&&l.isBefore(this.minDate,this.timepicker?"minute":"day")||d&&a.isAfter(d,this.timepicker?"minute":"day"))){var p;(p=document.createElement("textarea")).innerHTML=c;o=p.value;this.ranges[o]=[a,l]}}var f="<ul>";for(c in this.ranges)f+='<li data-range-key="'+c+'">'+c+"</li>";this.showCustomRangeLabel&&(f+='<li data-range-key="'+this.locale.customRangeLabel+'">'+this.locale.customRangeLabel+"</li>"),f+="</ul>",this.container.find(".ranges").prepend(f)}"function"==typeof s&&(this.callback=s),this.timePicker||(this.startDate=this.startDate.startOf("day"),this.endDate=this.endDate.endOf("day"),this.container.find(".calendar-time").hide()),this.timePicker&&this.autoApply&&(this.autoApply=!1),this.autoApply&&this.container.addClass("auto-apply"),"object"==typeof n.ranges&&this.container.addClass("show-ranges"),this.singleDatePicker&&(this.container.addClass("single"),this.container.find(".drp-calendar.left").addClass("single"),this.container.find(".drp-calendar.left").show(),this.container.find(".drp-calendar.right").hide(),!this.timePicker&&this.autoApply&&this.container.addClass("auto-apply")),(void 0===n.ranges&&!this.singleDatePicker||this.alwaysShowCalendars)&&this.container.addClass("show-calendar"),this.container.addClass("opens"+this.opens),this.container.find(".applyBtn, .cancelBtn").addClass(this.buttonClasses),this.applyButtonClasses.length&&this.container.find(".applyBtn").addClass(this.applyButtonClasses),this.cancelButtonClasses.length&&this.container.find(".cancelBtn").addClass(this.cancelButtonClasses),this.container.find(".applyBtn").html(this.locale.applyLabel),this.container.find(".cancelBtn").html(this.locale.cancelLabel),this.container.find(".drp-calendar").on("click.daterangepicker",".prev",e.proxy(this.clickPrev,this)).on("click.daterangepicker",".next",e.proxy(this.clickNext,this)).on("mousedown.daterangepicker","td.available",e.proxy(this.clickDate,this)).on("mouseenter.daterangepicker","td.available",e.proxy(this.hoverDate,this)).on("change.daterangepicker","select.yearselect",e.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.monthselect",e.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.hourselect,select.minuteselect,select.secondselect,select.ampmselect",e.proxy(this.timeChanged,this)),this.container.find(".ranges").on("click.daterangepicker","li",e.proxy(this.clickRange,this)),this.container.find(".drp-buttons").on("click.daterangepicker","button.applyBtn",e.proxy(this.clickApply,this)).on("click.daterangepicker","button.cancelBtn",e.proxy(this.clickCancel,this)),this.element.is("input")||this.element.is("button")?this.element.on({"click.daterangepicker":e.proxy(this.show,this),"focus.daterangepicker":e.proxy(this.show,this),"keyup.daterangepicker":e.proxy(this.elementChanged,this),"keydown.daterangepicker":e.proxy(this.keydown,this)}):(this.element.on("click.daterangepicker",e.proxy(this.toggle,this)),this.element.on("keydown.daterangepicker",e.proxy(this.toggle,this))),this.updateElement()};return i.prototype={constructor:i,setStartDate:function(e){"string"==typeof e&&(this.startDate=t(e,this.locale.format)),"object"==typeof e&&(this.startDate=t(e)),this.timePicker||(this.startDate=this.startDate.startOf("day")),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.maxDate&&this.startDate.isAfter(this.maxDate)&&(this.startDate=this.maxDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.floor(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.isShowing||this.updateElement(),this.updateMonthsInView()},setEndDate:function(e){"string"==typeof e&&(this.endDate=t(e,this.locale.format)),"object"==typeof e&&(this.endDate=t(e)),this.timePicker||(this.endDate=this.endDate.endOf("day")),this.timePicker&&this.timePickerIncrement&&this.endDate.minute(Math.round(this.endDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.endDate.isBefore(this.startDate)&&(this.endDate=this.startDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),this.maxSpan&&this.startDate.clone().add(this.maxSpan).isBefore(this.endDate)&&(this.endDate=this.startDate.clone().add(this.maxSpan)),this.previousRightTime=this.endDate.clone(),this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.isShowing||this.updateElement(),this.updateMonthsInView()},isInvalidDate:function(){return!1},isCustomDate:function(){return!1},updateView:function(){this.timePicker&&(this.renderTimePicker("left"),this.renderTimePicker("right"),this.endDate?this.container.find(".right .calendar-time select").prop("disabled",!1).removeClass("disabled"):this.container.find(".right .calendar-time select").prop("disabled",!0).addClass("disabled")),this.endDate&&this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.updateMonthsInView(),this.updateCalendars(),this.updateFormInputs()},updateMonthsInView:function(){if(this.endDate){if(!this.singleDatePicker&&this.leftCalendar.month&&this.rightCalendar.month&&(this.startDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.startDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM"))&&(this.endDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.endDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM")))return;this.leftCalendar.month=this.startDate.clone().date(2),this.linkedCalendars||this.endDate.month()==this.startDate.month()&&this.endDate.year()==this.startDate.year()?this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"):this.rightCalendar.month=this.endDate.clone().date(2)}else this.leftCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&this.rightCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&(this.leftCalendar.month=this.startDate.clone().date(2),this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"));this.maxDate&&this.linkedCalendars&&!this.singleDatePicker&&this.rightCalendar.month>this.maxDate&&(this.rightCalendar.month=this.maxDate.clone().date(2),this.leftCalendar.month=this.maxDate.clone().date(2).subtract(1,"month"))},updateCalendars:function(){if(this.timePicker){var t,e,i,n;if(this.endDate){if(t=parseInt(this.container.find(".left .hourselect").val(),10),e=parseInt(this.container.find(".left .minuteselect").val(),10),isNaN(e)&&(e=parseInt(this.container.find(".left .minuteselect option:last").val(),10)),i=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,!this.timePicker24Hour)"PM"===(n=this.container.find(".left .ampmselect").val())&&t<12&&(t+=12),"AM"===n&&12===t&&(t=0)}else if(t=parseInt(this.container.find(".right .hourselect").val(),10),e=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(e)&&(e=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),i=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,!this.timePicker24Hour)"PM"===(n=this.container.find(".right .ampmselect").val())&&t<12&&(t+=12),"AM"===n&&12===t&&(t=0);this.leftCalendar.month.hour(t).minute(e).second(i),this.rightCalendar.month.hour(t).minute(e).second(i)}this.renderCalendar("left"),this.renderCalendar("right"),this.container.find(".ranges li").removeClass("active"),null!=this.endDate&&this.calculateChosenLabel()},renderCalendar:function(i){var n,s=(n="left"==i?this.leftCalendar:this.rightCalendar).month.month(),o=n.month.year(),r=n.month.hour(),a=n.month.minute(),l=n.month.second(),c=t([o,s]).daysInMonth(),h=t([o,s,1]),u=t([o,s,c]),d=t(h).subtract(1,"month").month(),p=t(h).subtract(1,"month").year(),f=t([p,d]).daysInMonth(),m=h.day();(n=[]).firstDay=h,n.lastDay=u;for(var g=0;g<6;g++)n[g]=[];var v=f-m+this.locale.firstDay+1;v>f&&(v-=7),m==this.locale.firstDay&&(v=f-6);for(var y=t([p,d,v,12,a,l]),b=(g=0,0),_=0;g<42;g++,b++,y=t(y).add(24,"hour"))g>0&&b%7==0&&(b=0,_++),n[_][b]=y.clone().hour(r).minute(a).second(l),y.hour(12),this.minDate&&n[_][b].format("YYYY-MM-DD")==this.minDate.format("YYYY-MM-DD")&&n[_][b].isBefore(this.minDate)&&"left"==i&&(n[_][b]=this.minDate.clone()),this.maxDate&&n[_][b].format("YYYY-MM-DD")==this.maxDate.format("YYYY-MM-DD")&&n[_][b].isAfter(this.maxDate)&&"right"==i&&(n[_][b]=this.maxDate.clone());"left"==i?this.leftCalendar.calendar=n:this.rightCalendar.calendar=n;var w="left"==i?this.minDate:this.startDate,x=this.maxDate,k=("left"==i?this.startDate:this.endDate,this.locale.direction,'<table class="table-condensed">');k+="<thead>",k+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(k+="<th></th>"),w&&!w.isBefore(n.firstDay)||this.linkedCalendars&&"left"!=i?k+="<th></th>":k+='<th class="prev available"><span></span></th>';var D=this.locale.monthNames[n[1][1].month()]+n[1][1].format(" YYYY");if(this.showDropdowns){for(var C=n[1][1].month(),S=n[1][1].year(),T=x&&x.year()||this.maxYear,M=w&&w.year()||this.minYear,E=S==M,A=S==T,O='<select class="monthselect">',N=0;N<12;N++)(!E||w&&N>=w.month())&&(!A||x&&N<=x.month())?O+="<option value='"+N+"'"+(N===C?" selected='selected'":"")+">"+this.locale.monthNames[N]+"</option>":O+="<option value='"+N+"'"+(N===C?" selected='selected'":"")+" disabled='disabled'>"+this.locale.monthNames[N]+"</option>";O+="</select>";for(var L='<select class="yearselect">',P=M;P<=T;P++)L+='<option value="'+P+'"'+(P===S?' selected="selected"':"")+">"+P+"</option>";D=O+(L+="</select>")}if(k+='<th colspan="5" class="month">'+D+"</th>",x&&!x.isAfter(n.lastDay)||this.linkedCalendars&&"right"!=i&&!this.singleDatePicker?k+="<th></th>":k+='<th class="next available"><span></span></th>',k+="</tr>",k+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(k+='<th class="week">'+this.locale.weekLabel+"</th>"),e.each(this.locale.daysOfWeek,function(t,e){k+="<th>"+e+"</th>"}),k+="</tr>",k+="</thead>",k+="<tbody>",null==this.endDate&&this.maxSpan){var j=this.startDate.clone().add(this.maxSpan).endOf("day");x&&!j.isBefore(x)||(x=j)}for(_=0;_<6;_++){k+="<tr>",this.showWeekNumbers?k+='<td class="week">'+n[_][0].week()+"</td>":this.showISOWeekNumbers&&(k+='<td class="week">'+n[_][0].isoWeek()+"</td>");for(b=0;b<7;b++){var $=[];n[_][b].isSame(new Date,"day")&&$.push("today"),n[_][b].isoWeekday()>5&&$.push("weekend"),n[_][b].month()!=n[1][1].month()&&$.push("off","ends"),this.minDate&&n[_][b].isBefore(this.minDate,"day")&&$.push("off","disabled"),x&&n[_][b].isAfter(x,"day")&&$.push("off","disabled"),this.isInvalidDate(n[_][b])&&$.push("off","disabled"),n[_][b].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")&&$.push("active","start-date"),null!=this.endDate&&n[_][b].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")&&$.push("active","end-date"),null!=this.endDate&&n[_][b]>this.startDate&&n[_][b]<this.endDate&&$.push("in-range");var Y=this.isCustomDate(n[_][b]);!1!==Y&&("string"==typeof Y?$.push(Y):Array.prototype.push.apply($,Y));var I="",W=!1;for(g=0;g<$.length;g++)I+=$[g]+" ","disabled"==$[g]&&(W=!0);W||(I+="available"),k+='<td class="'+I.replace(/^\s+|\s+$/g,"")+'" data-title="r'+_+"c"+b+'">'+n[_][b].date()+"</td>"}k+="</tr>"}k+="</tbody>",k+="</table>",this.container.find(".drp-calendar."+i+" .calendar-table").html(k)},renderTimePicker:function(t){if("right"!=t||this.endDate){var e,i,n,s=this.maxDate;if(!this.maxSpan||this.maxDate&&!this.startDate.clone().add(this.maxSpan).isBefore(this.maxDate)||(s=this.startDate.clone().add(this.maxSpan)),"left"==t)i=this.startDate.clone(),n=this.minDate;else if("right"==t){i=this.endDate.clone(),n=this.startDate;var o=this.container.find(".drp-calendar.right .calendar-time");if(""!=o.html()&&(i.hour(isNaN(i.hour())?o.find(".hourselect option:selected").val():i.hour()),i.minute(isNaN(i.minute())?o.find(".minuteselect option:selected").val():i.minute()),i.second(isNaN(i.second())?o.find(".secondselect option:selected").val():i.second()),!this.timePicker24Hour)){var r=o.find(".ampmselect option:selected").val();"PM"===r&&i.hour()<12&&i.hour(i.hour()+12),"AM"===r&&12===i.hour()&&i.hour(0)}i.isBefore(this.startDate)&&(i=this.startDate.clone()),s&&i.isAfter(s)&&(i=s.clone())}e='<select class="hourselect">';for(var a=this.timePicker24Hour?0:1,l=this.timePicker24Hour?23:12,c=a;c<=l;c++){var h=c;this.timePicker24Hour||(h=i.hour()>=12?12==c?12:c+12:12==c?0:c);var u=i.clone().hour(h),d=!1;n&&u.minute(59).isBefore(n)&&(d=!0),s&&u.minute(0).isAfter(s)&&(d=!0),h!=i.hour()||d?e+=d?'<option value="'+c+'" disabled="disabled" class="disabled">'+c+"</option>":'<option value="'+c+'">'+c+"</option>":e+='<option value="'+c+'" selected="selected">'+c+"</option>"}e+="</select> ",e+=': <select class="minuteselect">';for(c=0;c<60;c+=this.timePickerIncrement){var p=c<10?"0"+c:c;u=i.clone().minute(c),d=!1;n&&u.second(59).isBefore(n)&&(d=!0),s&&u.second(0).isAfter(s)&&(d=!0),i.minute()!=c||d?e+=d?'<option value="'+c+'" disabled="disabled" class="disabled">'+p+"</option>":'<option value="'+c+'">'+p+"</option>":e+='<option value="'+c+'" selected="selected">'+p+"</option>"}if(e+="</select> ",this.timePickerSeconds){e+=': <select class="secondselect">';for(c=0;c<60;c++){p=c<10?"0"+c:c,u=i.clone().second(c),d=!1;n&&u.isBefore(n)&&(d=!0),s&&u.isAfter(s)&&(d=!0),i.second()!=c||d?e+=d?'<option value="'+c+'" disabled="disabled" class="disabled">'+p+"</option>":'<option value="'+c+'">'+p+"</option>":e+='<option value="'+c+'" selected="selected">'+p+"</option>"}e+="</select> "}if(!this.timePicker24Hour){e+='<select class="ampmselect">';var f="",m="";n&&i.clone().hour(12).minute(0).second(0).isBefore(n)&&(f=' disabled="disabled" class="disabled"'),s&&i.clone().hour(0).minute(0).second(0).isAfter(s)&&(m=' disabled="disabled" class="disabled"'),i.hour()>=12?e+='<option value="AM"'+f+'>AM</option><option value="PM" selected="selected"'+m+">PM</option>":e+='<option value="AM" selected="selected"'+f+'>AM</option><option value="PM"'+m+">PM</option>",e+="</select>"}this.container.find(".drp-calendar."+t+" .calendar-time").html(e)}},updateFormInputs:function(){this.singleDatePicker||this.endDate&&(this.startDate.isBefore(this.endDate)||this.startDate.isSame(this.endDate))?this.container.find("button.applyBtn").prop("disabled",!1):this.container.find("button.applyBtn").prop("disabled",!0)},move:function(){var t,i={top:0,left:0},n=this.drops,s=e(window).width();switch(this.parentEl.is("body")||(i={top:this.parentEl.offset().top-this.parentEl.scrollTop(),left:this.parentEl.offset().left-this.parentEl.scrollLeft()},s=this.parentEl[0].clientWidth+this.parentEl.offset().left),n){case"auto":(t=this.element.offset().top+this.element.outerHeight()-i.top)+this.container.outerHeight()>=this.parentEl[0].scrollHeight&&(t=this.element.offset().top-this.container.outerHeight()-i.top,n="up");break;case"up":t=this.element.offset().top-this.container.outerHeight()-i.top;break;default:t=this.element.offset().top+this.element.outerHeight()-i.top}this.container.css({top:0,left:0,right:"auto"});var o=this.container.outerWidth();if(this.container.toggleClass("drop-up","up"==n),"left"==this.opens){var r=s-this.element.offset().left-this.element.outerWidth();o+r>e(window).width()?this.container.css({top:t,right:"auto",left:9}):this.container.css({top:t,right:r,left:"auto"})}else if("center"==this.opens){(a=this.element.offset().left-i.left+this.element.outerWidth()/2-o/2)<0?this.container.css({top:t,right:"auto",left:9}):a+o>e(window).width()?this.container.css({top:t,left:"auto",right:0}):this.container.css({top:t,left:a,right:"auto"})}else{var a;(a=this.element.offset().left-i.left)+o>e(window).width()?this.container.css({top:t,left:"auto",right:0}):this.container.css({top:t,left:a,right:"auto"})}},show:function(t){this.isShowing||(this._outsideClickProxy=e.proxy(function(t){this.outsideClick(t)},this),e(document).on("mousedown.daterangepicker",this._outsideClickProxy).on("touchend.daterangepicker",this._outsideClickProxy).on("click.daterangepicker","[data-toggle=dropdown]",this._outsideClickProxy).on("focusin.daterangepicker",this._outsideClickProxy),e(window).on("resize.daterangepicker",e.proxy(function(t){this.move(t)},this)),this.oldStartDate=this.startDate.clone(),this.oldEndDate=this.endDate.clone(),this.previousRightTime=this.endDate.clone(),this.updateView(),this.container.show(),this.move(),this.element.trigger("show.daterangepicker",this),this.isShowing=!0)},hide:function(t){this.isShowing&&(this.endDate||(this.startDate=this.oldStartDate.clone(),this.endDate=this.oldEndDate.clone()),this.startDate.isSame(this.oldStartDate)&&this.endDate.isSame(this.oldEndDate)||this.callback(this.startDate.clone(),this.endDate.clone(),this.chosenLabel),this.updateElement(),e(document).off(".daterangepicker"),e(window).off(".daterangepicker"),this.container.hide(),this.element.trigger("hide.daterangepicker",this),this.isShowing=!1)},toggle:function(t){this.isShowing?this.hide():this.show()},outsideClick:function(t){var i=e(t.target);"focusin"==t.type||i.closest(this.element).length||i.closest(this.container).length||i.closest(".calendar-table").length||(this.hide(),this.element.trigger("outsideClick.daterangepicker",this))},showCalendars:function(){this.container.addClass("show-calendar"),this.move(),this.element.trigger("showCalendar.daterangepicker",this)},hideCalendars:function(){this.container.removeClass("show-calendar"),this.element.trigger("hideCalendar.daterangepicker",this)},clickRange:function(t){var e=t.target.getAttribute("data-range-key");if(this.chosenLabel=e,e==this.locale.customRangeLabel)this.showCalendars();else{var i=this.ranges[e];this.startDate=i[0],this.endDate=i[1],this.timePicker||(this.startDate.startOf("day"),this.endDate.endOf("day")),this.alwaysShowCalendars||this.hideCalendars(),this.clickApply()}},clickPrev:function(t){e(t.target).parents(".drp-calendar").hasClass("left")?(this.leftCalendar.month.subtract(1,"month"),this.linkedCalendars&&this.rightCalendar.month.subtract(1,"month")):this.rightCalendar.month.subtract(1,"month"),this.updateCalendars()},clickNext:function(t){e(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.month.add(1,"month"):(this.rightCalendar.month.add(1,"month"),this.linkedCalendars&&this.leftCalendar.month.add(1,"month")),this.updateCalendars()},hoverDate:function(t){if(e(t.target).hasClass("available")){var i=e(t.target).attr("data-title"),n=i.substr(1,1),s=i.substr(3,1),o=e(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.calendar[n][s]:this.rightCalendar.calendar[n][s],r=this.leftCalendar,a=this.rightCalendar,l=this.startDate;this.endDate||this.container.find(".drp-calendar tbody td").each(function(t,i){if(!e(i).hasClass("week")){var n=e(i).attr("data-title"),s=n.substr(1,1),c=n.substr(3,1),h=e(i).parents(".drp-calendar").hasClass("left")?r.calendar[s][c]:a.calendar[s][c];h.isAfter(l)&&h.isBefore(o)||h.isSame(o,"day")?e(i).addClass("in-range"):e(i).removeClass("in-range")}})}},clickDate:function(t){if(e(t.target).hasClass("available")){var i=e(t.target).attr("data-title"),n=i.substr(1,1),s=i.substr(3,1),o=e(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.calendar[n][s]:this.rightCalendar.calendar[n][s];if(this.endDate||o.isBefore(this.startDate,"day")){if(this.timePicker){var r=parseInt(this.container.find(".left .hourselect").val(),10);if(!this.timePicker24Hour)"PM"===(c=this.container.find(".left .ampmselect").val())&&r<12&&(r+=12),"AM"===c&&12===r&&(r=0);var a=parseInt(this.container.find(".left .minuteselect").val(),10);isNaN(a)&&(a=parseInt(this.container.find(".left .minuteselect option:last").val(),10));var l=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0;o=o.clone().hour(r).minute(a).second(l)}this.endDate=null,this.setStartDate(o.clone())}else if(!this.endDate&&o.isBefore(this.startDate))this.setEndDate(this.startDate.clone());else{if(this.timePicker){var c;r=parseInt(this.container.find(".right .hourselect").val(),10);if(!this.timePicker24Hour)"PM"===(c=this.container.find(".right .ampmselect").val())&&r<12&&(r+=12),"AM"===c&&12===r&&(r=0);a=parseInt(this.container.find(".right .minuteselect").val(),10);isNaN(a)&&(a=parseInt(this.container.find(".right .minuteselect option:last").val(),10));l=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0;o=o.clone().hour(r).minute(a).second(l)}this.setEndDate(o.clone()),this.autoApply&&(this.calculateChosenLabel(),this.clickApply())}this.singleDatePicker&&(this.setEndDate(this.startDate),!this.timePicker&&this.autoApply&&this.clickApply()),this.updateView(),t.stopPropagation()}},calculateChosenLabel:function(){var t=!0,e=0;for(var i in this.ranges){if(this.timePicker){var n=this.timePickerSeconds?"YYYY-MM-DD HH:mm:ss":"YYYY-MM-DD HH:mm";if(this.startDate.format(n)==this.ranges[i][0].format(n)&&this.endDate.format(n)==this.ranges[i][1].format(n)){t=!1,this.chosenLabel=this.container.find(".ranges li:eq("+e+")").addClass("active").attr("data-range-key");break}}else if(this.startDate.format("YYYY-MM-DD")==this.ranges[i][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[i][1].format("YYYY-MM-DD")){t=!1,this.chosenLabel=this.container.find(".ranges li:eq("+e+")").addClass("active").attr("data-range-key");break}e++}t&&(this.showCustomRangeLabel?this.chosenLabel=this.container.find(".ranges li:last").addClass("active").attr("data-range-key"):this.chosenLabel=null,this.showCalendars())},clickApply:function(t){this.hide(),this.element.trigger("apply.daterangepicker",this)},clickCancel:function(t){this.startDate=this.oldStartDate,this.endDate=this.oldEndDate,this.hide(),this.element.trigger("cancel.daterangepicker",this)},monthOrYearChanged:function(t){var i=e(t.target).closest(".drp-calendar").hasClass("left"),n=i?"left":"right",s=this.container.find(".drp-calendar."+n),o=parseInt(s.find(".monthselect").val(),10),r=s.find(".yearselect").val();i||(r<this.startDate.year()||r==this.startDate.year()&&o<this.startDate.month())&&(o=this.startDate.month(),r=this.startDate.year()),this.minDate&&(r<this.minDate.year()||r==this.minDate.year()&&o<this.minDate.month())&&(o=this.minDate.month(),r=this.minDate.year()),this.maxDate&&(r>this.maxDate.year()||r==this.maxDate.year()&&o>this.maxDate.month())&&(o=this.maxDate.month(),r=this.maxDate.year()),i?(this.leftCalendar.month.month(o).year(r),this.linkedCalendars&&(this.rightCalendar.month=this.leftCalendar.month.clone().add(1,"month"))):(this.rightCalendar.month.month(o).year(r),this.linkedCalendars&&(this.leftCalendar.month=this.rightCalendar.month.clone().subtract(1,"month"))),this.updateCalendars()},timeChanged:function(t){var i=e(t.target).closest(".drp-calendar"),n=i.hasClass("left"),s=parseInt(i.find(".hourselect").val(),10),o=parseInt(i.find(".minuteselect").val(),10);isNaN(o)&&(o=parseInt(i.find(".minuteselect option:last").val(),10));var r=this.timePickerSeconds?parseInt(i.find(".secondselect").val(),10):0;if(!this.timePicker24Hour){var a=i.find(".ampmselect").val();"PM"===a&&s<12&&(s+=12),"AM"===a&&12===s&&(s=0)}if(n){var l=this.startDate.clone();l.hour(s),l.minute(o),l.second(r),this.setStartDate(l),this.singleDatePicker?this.endDate=this.startDate.clone():this.endDate&&this.endDate.format("YYYY-MM-DD")==l.format("YYYY-MM-DD")&&this.endDate.isBefore(l)&&this.setEndDate(l.clone())}else if(this.endDate){var c=this.endDate.clone();c.hour(s),c.minute(o),c.second(r),this.setEndDate(c)}this.updateCalendars(),this.updateFormInputs(),this.renderTimePicker("left"),this.renderTimePicker("right")},elementChanged:function(){if(this.element.is("input")&&this.element.val().length){var e=this.element.val().split(this.locale.separator),i=null,n=null;2===e.length&&(i=t(e[0],this.locale.format),n=t(e[1],this.locale.format)),(this.singleDatePicker||null===i||null===n)&&(n=i=t(this.element.val(),this.locale.format)),i.isValid()&&n.isValid()&&(this.setStartDate(i),this.setEndDate(n),this.updateView())}},keydown:function(t){9!==t.keyCode&&13!==t.keyCode||this.hide(),27===t.keyCode&&(t.preventDefault(),t.stopPropagation(),this.hide())},updateElement:function(){if(this.element.is("input")&&this.autoUpdateInput){var t=this.startDate.format(this.locale.format);this.singleDatePicker||(t+=this.locale.separator+this.endDate.format(this.locale.format)),t!==this.element.val()&&this.element.val(t).trigger("change")}},remove:function(){this.container.remove(),this.element.off(".daterangepicker"),this.element.removeData()}},e.fn.daterangepicker=function(t,n){var s=e.extend(!0,{},e.fn.daterangepicker.defaultOptions,t);return this.each(function(){var t=e(this);t.data("daterangepicker")&&t.data("daterangepicker").remove(),t.data("daterangepicker",new i(t,s,n))}),this},i}),"function"!=typeof Object.create&&(Object.create=function(t){function e(){}return e.prototype=t,new e}),function(t,e,i,n){"use strict";var s={_positionClasses:["bottom-left","bottom-right","top-right","top-left","bottom-center","top-center","mid-center"],_defaultIcons:["success","error","info","warning"],init:function(e,i){this.prepareOptions(e,t.toast.options),this.process()},prepareOptions:function(e,i){var n={};"string"==typeof e||e instanceof Array?n.text=e:n=e,this.options=t.extend({},i,n)},process:function(){this.setup(),this.addToDom(),this.position(),this.bindToast(),this.animate()},setup:function(){var e="";if(this._toastEl=this._toastEl||t("<div></div>",{class:"jq-toast-single"}),e+='<span class="jq-toast-loader"></span>',this.options.allowToastClose&&(e+='<span class="close-jq-toast-single">&times;</span>'),this.options.text instanceof Array){this.options.heading&&(e+='<h2 class="jq-toast-heading">'+this.options.heading+"</h2>"),e+='<ul class="jq-toast-ul">';for(var i=0;i<this.options.text.length;i++)e+='<li class="jq-toast-li" id="jq-toast-item-'+i+'">'+this.options.text[i]+"</li>";e+="</ul>"}else this.options.heading&&(e+='<h2 class="jq-toast-heading">'+this.options.heading+"</h2>"),e+=this.options.text;this._toastEl.html(e),!1!==this.options.bgColor&&this._toastEl.css("background-color",this.options.bgColor),!1!==this.options.textColor&&this._toastEl.css("color",this.options.textColor),this.options.textAlign&&this._toastEl.css("text-align",this.options.textAlign),!1!==this.options.icon&&(this._toastEl.addClass("jq-has-icon"),-1!==t.inArray(this.options.icon,this._defaultIcons)&&this._toastEl.addClass("jq-icon-"+this.options.icon)),!1!==this.options.class&&this._toastEl.addClass(this.options.class)},position:function(){"string"==typeof this.options.position&&-1!==t.inArray(this.options.position,this._positionClasses)?"bottom-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,bottom:20}):"top-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,top:20}):"mid-center"===this.options.position?this._container.css({left:t(e).outerWidth()/2-this._container.outerWidth()/2,top:t(e).outerHeight()/2-this._container.outerHeight()/2}):this._container.addClass(this.options.position):"object"==typeof this.options.position?this._container.css({top:this.options.position.top?this.options.position.top:"auto",bottom:this.options.position.bottom?this.options.position.bottom:"auto",left:this.options.position.left?this.options.position.left:"auto",right:this.options.position.right?this.options.position.right:"auto"}):this._container.addClass("bottom-left")},bindToast:function(){var t=this;this._toastEl.on("afterShown",function(){t.processLoader()}),this._toastEl.find(".close-jq-toast-single").on("click",function(e){e.preventDefault(),"fade"===t.options.showHideTransition?(t._toastEl.trigger("beforeHide"),t._toastEl.fadeOut(function(){t._toastEl.trigger("afterHidden")})):"slide"===t.options.showHideTransition?(t._toastEl.trigger("beforeHide"),t._toastEl.slideUp(function(){t._toastEl.trigger("afterHidden")})):(t._toastEl.trigger("beforeHide"),t._toastEl.hide(function(){t._toastEl.trigger("afterHidden")}))}),"function"==typeof this.options.beforeShow&&this._toastEl.on("beforeShow",function(){t.options.beforeShow()}),"function"==typeof this.options.afterShown&&this._toastEl.on("afterShown",function(){t.options.afterShown()}),"function"==typeof this.options.beforeHide&&this._toastEl.on("beforeHide",function(){t.options.beforeHide()}),"function"==typeof this.options.afterHidden&&this._toastEl.on("afterHidden",function(){t.options.afterHidden()})},addToDom:function(){var e=t(".jq-toast-wrap");if(0===e.length?(e=t("<div></div>",{class:"jq-toast-wrap"}),t("body").append(e)):(!this.options.stack||isNaN(parseInt(this.options.stack,10)))&&e.empty(),e.find(".jq-toast-single:hidden").remove(),e.append(this._toastEl),this.options.stack&&!isNaN(parseInt(this.options.stack),10)){var i=e.find(".jq-toast-single").length-this.options.stack;i>0&&t(".jq-toast-wrap").find(".jq-toast-single").slice(0,i).remove()}this._container=e},canAutoHide:function(){return!1!==this.options.hideAfter&&!isNaN(parseInt(this.options.hideAfter,10))},processLoader:function(){if(!this.canAutoHide()||!1===this.options.loader)return!1;var t=this._toastEl.find(".jq-toast-loader"),e=(this.options.hideAfter-400)/1e3+"s",i=this.options.loaderBg,n=t.attr("style")||"";n=n.substring(0,n.indexOf("-webkit-transition")),n+="-webkit-transition: width "+e+" ease-in;                       -o-transition: width "+e+" ease-in;                       transition: width "+e+" ease-in;                       background-color: "+i+";",t.attr("style",n).addClass("jq-toast-loaded")},animate:function(){var t=this;if(this._toastEl.hide(),this._toastEl.trigger("beforeShow"),"fade"===this.options.showHideTransition.toLowerCase()?this._toastEl.fadeIn(function(){t._toastEl.trigger("afterShown")}):"slide"===this.options.showHideTransition.toLowerCase()?this._toastEl.slideDown(function(){t._toastEl.trigger("afterShown")}):this._toastEl.show(function(){t._toastEl.trigger("afterShown")}),this.canAutoHide()){t=this;e.setTimeout(function(){"fade"===t.options.showHideTransition.toLowerCase()?(t._toastEl.trigger("beforeHide"),t._toastEl.fadeOut(function(){t._toastEl.trigger("afterHidden")})):"slide"===t.options.showHideTransition.toLowerCase()?(t._toastEl.trigger("beforeHide"),t._toastEl.slideUp(function(){t._toastEl.trigger("afterHidden")})):(t._toastEl.trigger("beforeHide"),t._toastEl.hide(function(){t._toastEl.trigger("afterHidden")}))},this.options.hideAfter)}},reset:function(e){"all"===e?t(".jq-toast-wrap").remove():this._toastEl.remove()},update:function(t){this.prepareOptions(t,this.options),this.setup(),this.bindToast()}};t.toast=function(t){var e=Object.create(s);return e.init(t,this),{reset:function(t){e.reset(t)},update:function(t){e.update(t)}}},t.toast.options={text:"",heading:"",showHideTransition:"fade",allowToastClose:!0,hideAfter:3e3,loader:!0,loaderBg:"#9EC600",stack:5,position:"bottom-left",bgColor:!1,textColor:!1,textAlign:"left",icon:!1,beforeShow:function(){},afterShown:function(){},beforeHide:function(){},afterHidden:function(){}}}(jQuery,window,document),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=function(e,i){return void 0===i&&(i="undefined"!=typeof window?require("jquery"):require("jquery")(e)),t(i),i}:t(jQuery)}(function(t){var e,i,n,s,o,r,a,l,c,h,u,d,p,f,m;function g(t,e){return d.call(t,e)}function v(t,e){var i,n,s,o,r,a,l,c,u,d,p=e&&e.split("/"),m=h.map,g=m&&m["*"]||{};if(t){for(e=(t=t.split("/")).length-1,h.nodeIdCompat&&f.test(t[e])&&(t[e]=t[e].replace(f,"")),"."===t[0].charAt(0)&&p&&(t=p.slice(0,p.length-1).concat(t)),c=0;c<t.length;c++)"."===(d=t[c])?(t.splice(c,1),--c):".."===d&&(0===c||1===c&&".."===t[2]||".."===t[c-1]||0<c&&(t.splice(c-1,2),c-=2));t=t.join("/")}if((p||g)&&m){for(c=(i=t.split("/")).length;0<c;--c){if(n=i.slice(0,c).join("/"),p)for(u=p.length;0<u;--u)if(s=(s=m[p.slice(0,u).join("/")])&&s[n]){o=s,r=c;break}if(o)break;!a&&g&&g[n]&&(a=g[n],l=c)}!o&&a&&(o=a,r=l),o&&(i.splice(0,r,o),t=i.join("/"))}return t}function y(t,e){return function(){var i=p.call(arguments,0);return"string"!=typeof i[0]&&1===i.length&&i.push(null),o.apply(n,i.concat([t,e]))}}function b(t){var e;if(g(c,t)&&(e=c[t],delete c[t],u[t]=!0,s.apply(n,e)),!g(l,t)&&!g(u,t))throw new Error("No "+t);return l[t]}function _(t){var e,i=t?t.indexOf("!"):-1;return-1<i&&(e=t.substring(0,i),t=t.substring(i+1,t.length)),[e,t]}function w(t){return t?_(t):[]}var x=(m=((x=t&&t.fn&&t.fn.select2&&t.fn.select2.amd?t.fn.select2.amd:x)&&x.requirejs||(x?i=x:x={},l={},c={},h={},u={},d=Object.prototype.hasOwnProperty,p=[].slice,f=/\.js$/,r=function(t,e){var i,n,s=_(t),o=s[0];e=e[1];return t=s[1],o&&(i=b(o=v(o,e))),o?t=i&&i.normalize?i.normalize(t,(n=e,function(t){return v(t,n)})):v(t,e):(o=(s=_(t=v(t,e)))[0],t=s[1],o&&(i=b(o))),{f:o?o+"!"+t:t,n:t,pr:o,p:i}},a={require:function(t){return y(t)},exports:function(t){var e=l[t];return void 0!==e?e:l[t]={}},module:function(t){return{id:t,uri:"",exports:l[t],config:(e=t,function(){return h&&h.config&&h.config[e]||{}})};var e}},s=function(t,e,i,s){var o,h,d,p,f,m=[],v=typeof i,_=w(s=s||t);if("undefined"==v||"function"==v){for(e=!e.length&&i.length?["require","exports","module"]:e,p=0;p<e.length;p+=1)if("require"===(h=(d=r(e[p],_)).f))m[p]=a.require(t);else if("exports"===h)m[p]=a.exports(t),f=!0;else if("module"===h)o=m[p]=a.module(t);else if(g(l,h)||g(c,h)||g(u,h))m[p]=b(h);else{if(!d.p)throw new Error(t+" missing "+h);d.p.load(d.n,y(s,!0),function(t){return function(e){l[t]=e}}(h),{}),m[p]=l[h]}v=i?i.apply(l[t],m):void 0,t&&(o&&o.exports!==n&&o.exports!==l[t]?l[t]=o.exports:v===n&&f||(l[t]=v))}else t&&(l[t]=i)},e=i=o=function(t,e,i,l,c){if("string"==typeof t)return a[t]?a[t](e):b(r(t,w(e)).f);if(!t.splice){if((h=t).deps&&o(h.deps,h.callback),!e)return;e.splice?(t=e,e=i,i=null):t=n}return e=e||function(){},"function"==typeof i&&(i=l,l=c),l?s(n,t,e,i):setTimeout(function(){s(n,t,e,i)},4),o},o.config=function(t){return o(t)},e._defined=l,(m=function(t,e,i){if("string"!=typeof t)throw new Error("See almond README: incorrect module build, no module name");e.splice||(i=e,e=[]),g(l,t)||g(c,t)||(c[t]=[t,e,i])}).amd={jQuery:!0},x.requirejs=e,x.require=i,x.define=m),x.define("almond",function(){}),x.define("jquery",[],function(){var e=t||$;return null==e&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),e}),x.define("select2/utils",["jquery"],function(t){var e={};function i(t){var e,i=t.prototype,n=[];for(e in i)"function"==typeof i[e]&&"constructor"!==e&&n.push(e);return n}function n(){this.listeners={}}e.Extend=function(t,e){var i,n={}.hasOwnProperty;function s(){this.constructor=t}for(i in e)n.call(e,i)&&(t[i]=e[i]);return s.prototype=e.prototype,t.prototype=new s,t.__super__=e.prototype,t},e.Decorate=function(t,e){var n=i(e),s=i(t);function o(){var i=Array.prototype.unshift,n=e.prototype.constructor.length,s=t.prototype.constructor;0<n&&(i.call(arguments,t.prototype.constructor),s=e.prototype.constructor),s.apply(this,arguments)}e.displayName=t.displayName,o.prototype=new function(){this.constructor=o};for(var r=0;r<s.length;r++){var a=s[r];o.prototype[a]=t.prototype[a]}for(var l=0;l<n.length;l++){var c=n[l];o.prototype[c]=function(t){var i=function(){};t in o.prototype&&(i=o.prototype[t]);var n=e.prototype[t];return function(){return Array.prototype.unshift.call(arguments,i),n.apply(this,arguments)}}(c)}return o},n.prototype.on=function(t,e){this.listeners=this.listeners||{},t in this.listeners?this.listeners[t].push(e):this.listeners[t]=[e]},n.prototype.trigger=function(t){var e=Array.prototype.slice,i=e.call(arguments,1);this.listeners=this.listeners||{},0===(i=null==i?[]:i).length&&i.push({}),(i[0]._type=t)in this.listeners&&this.invoke(this.listeners[t],e.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},n.prototype.invoke=function(t,e){for(var i=0,n=t.length;i<n;i++)t[i].apply(this,e)},e.Observable=n,e.generateChars=function(t){for(var e="",i=0;i<t;i++)e+=Math.floor(36*Math.random()).toString(36);return e},e.bind=function(t,e){return function(){t.apply(e,arguments)}},e._convertData=function(t){for(var e in t){var i=e.split("-"),n=t;if(1!==i.length){for(var s=0;s<i.length;s++){var o=i[s];(o=o.substring(0,1).toLowerCase()+o.substring(1))in n||(n[o]={}),s==i.length-1&&(n[o]=t[e]),n=n[o]}delete t[e]}}return t},e.hasScroll=function(e,i){var n=t(i),s=i.style.overflowX,o=i.style.overflowY;return(s!==o||"hidden"!==o&&"visible"!==o)&&("scroll"===s||"scroll"===o||n.innerHeight()<i.scrollHeight||n.innerWidth()<i.scrollWidth)},e.escapeMarkup=function(t){var e={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof t?t:String(t).replace(/[&<>"'\/\\]/g,function(t){return e[t]})},e.__cache={};var s=0;return e.GetUniqueElementId=function(t){var i=t.getAttribute("data-select2-id");return null!=i||(i=t.id?"select2-data-"+t.id:"select2-data-"+(++s).toString()+"-"+e.generateChars(4),t.setAttribute("data-select2-id",i)),i},e.StoreData=function(t,i,n){t=e.GetUniqueElementId(t),e.__cache[t]||(e.__cache[t]={}),e.__cache[t][i]=n},e.GetData=function(i,n){var s=e.GetUniqueElementId(i);return n?e.__cache[s]&&null!=e.__cache[s][n]?e.__cache[s][n]:t(i).data(n):e.__cache[s]},e.RemoveData=function(t){var i=e.GetUniqueElementId(t);null!=e.__cache[i]&&delete e.__cache[i],t.removeAttribute("data-select2-id")},e.copyNonInternalCssClasses=function(t,e){var i=(i=t.getAttribute("class").trim().split(/\s+/)).filter(function(t){return 0===t.indexOf("select2-")});e=(e=e.getAttribute("class").trim().split(/\s+/)).filter(function(t){return 0!==t.indexOf("select2-")}),e=i.concat(e);t.setAttribute("class",e.join(" "))},e}),x.define("select2/results",["jquery","./utils"],function(t,e){function i(t,e,n){this.$element=t,this.data=n,this.options=e,i.__super__.constructor.call(this)}return e.Extend(i,e.Observable),i.prototype.render=function(){var e=t('<ul class="select2-results__options" role="listbox"></ul>');return this.options.get("multiple")&&e.attr("aria-multiselectable","true"),this.$results=e},i.prototype.clear=function(){this.$results.empty()},i.prototype.displayMessage=function(e){var i=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var n=t('<li role="alert" aria-live="assertive" class="select2-results__option"></li>'),s=this.options.get("translations").get(e.message);n.append(i(s(e.args))),n[0].className+=" select2-results__message",this.$results.append(n)},i.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},i.prototype.append=function(t){this.hideLoading();var e=[];if(null!=t.results&&0!==t.results.length){t.results=this.sort(t.results);for(var i=0;i<t.results.length;i++){var n=t.results[i];n=this.option(n);e.push(n)}this.$results.append(e)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},i.prototype.position=function(t,e){e.find(".select2-results").append(t)},i.prototype.sort=function(t){return this.options.get("sorter")(t)},i.prototype.highlightFirstItem=function(){var t=this.$results.find(".select2-results__option--selectable"),e=t.filter(".select2-results__option--selected");(0<e.length?e:t).first().trigger("mouseenter"),this.ensureHighlightVisible()},i.prototype.setClasses=function(){var i=this;this.data.current(function(n){var s=n.map(function(t){return t.id.toString()});i.$results.find(".select2-results__option--selectable").each(function(){var i=t(this),n=e.GetData(this,"data"),o=""+n.id;null!=n.element&&n.element.selected||null==n.element&&-1<s.indexOf(o)?(this.classList.add("select2-results__option--selected"),i.attr("aria-selected","true")):(this.classList.remove("select2-results__option--selected"),i.attr("aria-selected","false"))})})},i.prototype.showLoading=function(t){this.hideLoading(),t={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(t)},(t=this.option(t)).className+=" loading-results",this.$results.prepend(t)},i.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},i.prototype.option=function(i){var n=document.createElement("li");n.classList.add("select2-results__option"),n.classList.add("select2-results__option--selectable");var s,o={role:"option"},r=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;for(s in(null!=i.element&&r.call(i.element,":disabled")||null==i.element&&i.disabled)&&(o["aria-disabled"]="true",n.classList.remove("select2-results__option--selectable"),n.classList.add("select2-results__option--disabled")),null==i.id&&n.classList.remove("select2-results__option--selectable"),null!=i._resultId&&(n.id=i._resultId),i.title&&(n.title=i.title),i.children&&(o.role="group",o["aria-label"]=i.text,n.classList.remove("select2-results__option--selectable"),n.classList.add("select2-results__option--group")),o){var a=o[s];n.setAttribute(s,a)}if(i.children){var l=t(n),c=document.createElement("strong");c.className="select2-results__group",this.template(i,c);for(var h=[],u=0;u<i.children.length;u++){var d=i.children[u];d=this.option(d);h.push(d)}(r=t("<ul></ul>",{class:"select2-results__options select2-results__options--nested",role:"none"})).append(h),l.append(c),l.append(r)}else this.template(i,n);return e.StoreData(n,"data",i),n},i.prototype.bind=function(i,n){var s=this,o=i.id+"-results";this.$results.attr("id",o),i.on("results:all",function(t){s.clear(),s.append(t.data),i.isOpen()&&(s.setClasses(),s.highlightFirstItem())}),i.on("results:append",function(t){s.append(t.data),i.isOpen()&&s.setClasses()}),i.on("query",function(t){s.hideMessages(),s.showLoading(t)}),i.on("select",function(){i.isOpen()&&(s.setClasses(),s.options.get("scrollAfterSelect")&&s.highlightFirstItem())}),i.on("unselect",function(){i.isOpen()&&(s.setClasses(),s.options.get("scrollAfterSelect")&&s.highlightFirstItem())}),i.on("open",function(){s.$results.attr("aria-expanded","true"),s.$results.attr("aria-hidden","false"),s.setClasses(),s.ensureHighlightVisible()}),i.on("close",function(){s.$results.attr("aria-expanded","false"),s.$results.attr("aria-hidden","true"),s.$results.removeAttr("aria-activedescendant")}),i.on("results:toggle",function(){var t=s.getHighlightedResults();0!==t.length&&t.trigger("mouseup")}),i.on("results:select",function(){var t,i=s.getHighlightedResults();0!==i.length&&(t=e.GetData(i[0],"data"),i.hasClass("select2-results__option--selected")?s.trigger("close",{}):s.trigger("select",{data:t}))}),i.on("results:previous",function(){var t,e=s.getHighlightedResults(),i=s.$results.find(".select2-results__option--selectable"),n=i.index(e);n<=0||(t=n-1,0===e.length&&(t=0),(n=i.eq(t)).trigger("mouseenter"),e=s.$results.offset().top,i=n.offset().top,n=s.$results.scrollTop()+(i-e),0===t?s.$results.scrollTop(0):i-e<0&&s.$results.scrollTop(n))}),i.on("results:next",function(){var t,e=s.getHighlightedResults(),i=s.$results.find(".select2-results__option--selectable"),n=i.index(e)+1;n>=i.length||((t=i.eq(n)).trigger("mouseenter"),e=s.$results.offset().top+s.$results.outerHeight(!1),i=t.offset().top+t.outerHeight(!1),t=s.$results.scrollTop()+i-e,0===n?s.$results.scrollTop(0):e<i&&s.$results.scrollTop(t))}),i.on("results:focus",function(t){t.element[0].classList.add("select2-results__option--highlighted"),t.element[0].setAttribute("aria-selected","true")}),i.on("results:message",function(t){s.displayMessage(t)}),t.fn.mousewheel&&this.$results.on("mousewheel",function(t){var e=s.$results.scrollTop(),i=s.$results.get(0).scrollHeight-e+t.deltaY;e=0<t.deltaY&&e-t.deltaY<=0,i=t.deltaY<0&&i<=s.$results.height();e?(s.$results.scrollTop(0),t.preventDefault(),t.stopPropagation()):i&&(s.$results.scrollTop(s.$results.get(0).scrollHeight-s.$results.height()),t.preventDefault(),t.stopPropagation())}),this.$results.on("mouseup",".select2-results__option--selectable",function(i){var n=t(this),o=e.GetData(this,"data");n.hasClass("select2-results__option--selected")?s.options.get("multiple")?s.trigger("unselect",{originalEvent:i,data:o}):s.trigger("close",{}):s.trigger("select",{originalEvent:i,data:o})}),this.$results.on("mouseenter",".select2-results__option--selectable",function(i){var n=e.GetData(this,"data");s.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false"),s.trigger("results:focus",{data:n,element:t(this)})})},i.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},i.prototype.destroy=function(){this.$results.remove()},i.prototype.ensureHighlightVisible=function(){var t,e,i,n,s=this.getHighlightedResults();0!==s.length&&(t=this.$results.find(".select2-results__option--selectable").index(s),n=this.$results.offset().top,e=s.offset().top,i=this.$results.scrollTop()+(e-n),n=e-n,i-=2*s.outerHeight(!1),t<=2?this.$results.scrollTop(0):(n>this.$results.outerHeight()||n<0)&&this.$results.scrollTop(i))},i.prototype.template=function(e,i){var n=this.options.get("templateResult"),s=this.options.get("escapeMarkup");null==(e=n(e,i))?i.style.display="none":"string"==typeof e?i.innerHTML=s(e):t(i).append(e)},i}),x.define("select2/keys",[],function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}}),x.define("select2/selection/base",["jquery","../utils","../keys"],function(t,e,i){function n(t,e){this.$element=t,this.options=e,n.__super__.constructor.call(this)}return e.Extend(n,e.Observable),n.prototype.render=function(){var i=t('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=e.GetData(this.$element[0],"old-tabindex")?this._tabindex=e.GetData(this.$element[0],"old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),i.attr("title",this.$element.attr("title")),i.attr("tabindex",this._tabindex),i.attr("aria-disabled","false"),this.$selection=i},n.prototype.bind=function(t,e){var n=this,s=t.id+"-results";this.container=t,this.$selection.on("focus",function(t){n.trigger("focus",t)}),this.$selection.on("blur",function(t){n._handleBlur(t)}),this.$selection.on("keydown",function(t){n.trigger("keypress",t),t.which===i.SPACE&&t.preventDefault()}),t.on("results:focus",function(t){n.$selection.attr("aria-activedescendant",t.data._resultId)}),t.on("selection:update",function(t){n.update(t.data)}),t.on("open",function(){n.$selection.attr("aria-expanded","true"),n.$selection.attr("aria-owns",s),n._attachCloseHandler(t)}),t.on("close",function(){n.$selection.attr("aria-expanded","false"),n.$selection.removeAttr("aria-activedescendant"),n.$selection.removeAttr("aria-owns"),n.$selection.trigger("focus"),n._detachCloseHandler(t)}),t.on("enable",function(){n.$selection.attr("tabindex",n._tabindex),n.$selection.attr("aria-disabled","false")}),t.on("disable",function(){n.$selection.attr("tabindex","-1"),n.$selection.attr("aria-disabled","true")})},n.prototype._handleBlur=function(e){var i=this;window.setTimeout(function(){document.activeElement==i.$selection[0]||t.contains(i.$selection[0],document.activeElement)||i.trigger("blur",e)},1)},n.prototype._attachCloseHandler=function(i){t(document.body).on("mousedown.select2."+i.id,function(i){var n=t(i.target).closest(".select2");t(".select2.select2-container--open").each(function(){this!=n[0]&&e.GetData(this,"element").select2("close")})})},n.prototype._detachCloseHandler=function(e){t(document.body).off("mousedown.select2."+e.id)},n.prototype.position=function(t,e){e.find(".selection").append(t)},n.prototype.destroy=function(){this._detachCloseHandler(this.container)},n.prototype.update=function(t){throw new Error("The `update` method must be defined in child classes.")},n.prototype.isEnabled=function(){return!this.isDisabled()},n.prototype.isDisabled=function(){return this.options.get("disabled")},n}),x.define("select2/selection/single",["jquery","./base","../utils","../keys"],function(t,e,i,n){function s(){s.__super__.constructor.apply(this,arguments)}return i.Extend(s,e),s.prototype.render=function(){var t=s.__super__.render.call(this);return t[0].classList.add("select2-selection--single"),t.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),t},s.prototype.bind=function(t,e){var i=this;s.__super__.bind.apply(this,arguments);var n=t.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",n).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",n),this.$selection.attr("aria-controls",n),this.$selection.on("mousedown",function(t){1===t.which&&i.trigger("toggle",{originalEvent:t})}),this.$selection.on("focus",function(t){}),this.$selection.on("blur",function(t){}),t.on("focus",function(e){t.isOpen()||i.$selection.trigger("focus")})},s.prototype.clear=function(){var t=this.$selection.find(".select2-selection__rendered");t.empty(),t.removeAttr("title")},s.prototype.display=function(t,e){var i=this.options.get("templateSelection");return this.options.get("escapeMarkup")(i(t,e))},s.prototype.selectionContainer=function(){return t("<span></span>")},s.prototype.update=function(t){var e,i;0!==t.length?(i=t[0],e=this.$selection.find(".select2-selection__rendered"),t=this.display(i,e),e.empty().append(t),(i=i.title||i.text)?e.attr("title",i):e.removeAttr("title")):this.clear()},s}),x.define("select2/selection/multiple",["jquery","./base","../utils"],function(t,e,i){function n(t,e){n.__super__.constructor.apply(this,arguments)}return i.Extend(n,e),n.prototype.render=function(){var t=n.__super__.render.call(this);return t[0].classList.add("select2-selection--multiple"),t.html('<ul class="select2-selection__rendered"></ul>'),t},n.prototype.bind=function(e,s){var o=this;n.__super__.bind.apply(this,arguments);var r=e.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",r),this.$selection.on("click",function(t){o.trigger("toggle",{originalEvent:t})}),this.$selection.on("click",".select2-selection__choice__remove",function(e){var n;o.isDisabled()||(n=t(this).parent(),n=i.GetData(n[0],"data"),o.trigger("unselect",{originalEvent:e,data:n}))}),this.$selection.on("keydown",".select2-selection__choice__remove",function(t){o.isDisabled()||t.stopPropagation()})},n.prototype.clear=function(){var t=this.$selection.find(".select2-selection__rendered");t.empty(),t.removeAttr("title")},n.prototype.display=function(t,e){var i=this.options.get("templateSelection");return this.options.get("escapeMarkup")(i(t,e))},n.prototype.selectionContainer=function(){return t('<li class="select2-selection__choice"><button type="button" class="select2-selection__choice__remove" tabindex="-1"><span aria-hidden="true">&times;</span></button><span class="select2-selection__choice__display"></span></li>')},n.prototype.update=function(t){if(this.clear(),0!==t.length){for(var e=[],n=this.$selection.find(".select2-selection__rendered").attr("id")+"-choice-",s=0;s<t.length;s++){var o=t[s],r=this.selectionContainer(),a=this.display(o,r),l=n+i.generateChars(4)+"-";o.id?l+=o.id:l+=i.generateChars(4),r.find(".select2-selection__choice__display").append(a).attr("id",l);var c=o.title||o.text;c&&r.attr("title",c),a=this.options.get("translations").get("removeItem"),(c=r.find(".select2-selection__choice__remove")).attr("title",a()),c.attr("aria-label",a()),c.attr("aria-describedby",l),i.StoreData(r[0],"data",o),e.push(r)}this.$selection.find(".select2-selection__rendered").append(e)}},n}),x.define("select2/selection/placeholder",[],function(){function t(t,e,i){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),t.call(this,e,i)}return t.prototype.normalizePlaceholder=function(t,e){return"string"==typeof e?{id:"",text:e}:e},t.prototype.createPlaceholder=function(t,e){var i=this.selectionContainer();return i.html(this.display(e)),i[0].classList.add("select2-selection__placeholder"),i[0].classList.remove("select2-selection__choice"),e=e.title||e.text||i.text(),this.$selection.find(".select2-selection__rendered").attr("title",e),i},t.prototype.update=function(t,e){var i=1==e.length&&e[0].id!=this.placeholder.id;if(1<e.length||i)return t.call(this,e);this.clear(),e=this.createPlaceholder(this.placeholder),this.$selection.find(".select2-selection__rendered").append(e)},t}),x.define("select2/selection/allowClear",["jquery","../keys","../utils"],function(t,e,i){function n(){}return n.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",function(t){n._handleClear(t)}),e.on("keypress",function(t){n._handleKeyboardClear(t,e)})},n.prototype._handleClear=function(t,e){if(!this.isDisabled()){var n=this.$selection.find(".select2-selection__clear");if(0!==n.length){e.stopPropagation();var s=i.GetData(n[0],"data"),o=this.$element.val();this.$element.val(this.placeholder.id);var r={data:s};if(this.trigger("clear",r),r.prevented)this.$element.val(o);else{for(var a=0;a<s.length;a++)if(r={data:s[a]},this.trigger("unselect",r),r.prevented)return void this.$element.val(o);this.$element.trigger("input").trigger("change"),this.trigger("toggle",{})}}}},n.prototype._handleKeyboardClear=function(t,i,n){n.isOpen()||i.which!=e.DELETE&&i.which!=e.BACKSPACE||this._handleClear(i)},n.prototype.update=function(e,n){var s,o;e.call(this,n),this.$selection.find(".select2-selection__clear").remove(),this.$selection[0].classList.remove("select2-selection--clearable"),0<this.$selection.find(".select2-selection__placeholder").length||0===n.length||(s=this.$selection.find(".select2-selection__rendered").attr("id"),o=this.options.get("translations").get("removeAllItems"),(e=t('<button type="button" class="select2-selection__clear" tabindex="-1"><span aria-hidden="true">&times;</span></button>')).attr("title",o()),e.attr("aria-label",o()),e.attr("aria-describedby",s),i.StoreData(e[0],"data",n),this.$selection.prepend(e),this.$selection[0].classList.add("select2-selection--clearable"))},n}),x.define("select2/selection/search",["jquery","../utils","../keys"],function(t,e,i){function n(t,e,i){t.call(this,e,i)}return n.prototype.render=function(e){var i=this.options.get("translations").get("search"),n=t('<span class="select2-search select2-search--inline"><textarea class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" ></textarea></span>');return this.$searchContainer=n,this.$search=n.find("textarea"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",i()),e=e.call(this),this._transferTabIndex(),e.append(this.$searchContainer),e},n.prototype.bind=function(t,n,s){var o=this,r=n.id+"-results",a=n.id+"-container";t.call(this,n,s),o.$search.attr("aria-describedby",a),n.on("open",function(){o.$search.attr("aria-controls",r),o.$search.trigger("focus")}),n.on("close",function(){o.$search.val(""),o.resizeSearch(),o.$search.removeAttr("aria-controls"),o.$search.removeAttr("aria-activedescendant"),o.$search.trigger("focus")}),n.on("enable",function(){o.$search.prop("disabled",!1),o._transferTabIndex()}),n.on("disable",function(){o.$search.prop("disabled",!0)}),n.on("focus",function(t){o.$search.trigger("focus")}),n.on("results:focus",function(t){t.data._resultId?o.$search.attr("aria-activedescendant",t.data._resultId):o.$search.removeAttr("aria-activedescendant")}),this.$selection.on("focusin",".select2-search--inline",function(t){o.trigger("focus",t)}),this.$selection.on("focusout",".select2-search--inline",function(t){o._handleBlur(t)}),this.$selection.on("keydown",".select2-search--inline",function(t){var n;t.stopPropagation(),o.trigger("keypress",t),o._keyUpPrevented=t.isDefaultPrevented(),t.which!==i.BACKSPACE||""!==o.$search.val()||0<(n=o.$selection.find(".select2-selection__choice").last()).length&&(n=e.GetData(n[0],"data"),o.searchRemoveChoice(n),t.preventDefault())}),this.$selection.on("click",".select2-search--inline",function(t){o.$search.val()&&t.stopPropagation()});var l=(n=document.documentMode)&&n<=11;this.$selection.on("input.searchcheck",".select2-search--inline",function(t){l?o.$selection.off("input.search input.searchcheck"):o.$selection.off("keyup.search")}),this.$selection.on("keyup.search input.search",".select2-search--inline",function(t){var e;l&&"input"===t.type?o.$selection.off("input.search input.searchcheck"):(e=t.which)!=i.SHIFT&&e!=i.CTRL&&e!=i.ALT&&e!=i.TAB&&o.handleSearch(t)})},n.prototype._transferTabIndex=function(t){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},n.prototype.createPlaceholder=function(t,e){this.$search.attr("placeholder",e.text)},n.prototype.update=function(t,e){var i=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),t.call(this,e),this.resizeSearch(),i&&this.$search.trigger("focus")},n.prototype.handleSearch=function(){var t;this.resizeSearch(),this._keyUpPrevented||(t=this.$search.val(),this.trigger("query",{term:t})),this._keyUpPrevented=!1},n.prototype.searchRemoveChoice=function(t,e){this.trigger("unselect",{data:e}),this.$search.val(e.text),this.handleSearch()},n.prototype.resizeSearch=function(){this.$search.css("width","25px");var t="100%";""===this.$search.attr("placeholder")&&(t=.75*(this.$search.val().length+1)+"em"),this.$search.css("width",t)},n}),x.define("select2/selection/selectionCss",["../utils"],function(t){function e(){}return e.prototype.render=function(e){var i=e.call(this);return-1!==(e=this.options.get("selectionCssClass")||"").indexOf(":all:")&&(e=e.replace(":all:",""),t.copyNonInternalCssClasses(i[0],this.$element[0])),i.addClass(e),i},e}),x.define("select2/selection/eventRelay",["jquery"],function(t){function e(){}return e.prototype.bind=function(e,i,n){var s=this,o=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],r=["opening","closing","selecting","unselecting","clearing"];e.call(this,i,n),i.on("*",function(e,i){var n;-1!==o.indexOf(e)&&(i=i||{},n=t.Event("select2:"+e,{params:i}),s.$element.trigger(n),-1!==r.indexOf(e)&&(i.prevented=n.isDefaultPrevented()))})},e}),x.define("select2/translation",["jquery","require"],function(t,e){function i(t){this.dict=t||{}}return i.prototype.all=function(){return this.dict},i.prototype.get=function(t){return this.dict[t]},i.prototype.extend=function(e){this.dict=t.extend({},e.all(),this.dict)},i._cache={},i.loadPath=function(t){var n;return t in i._cache||(n=e(t),i._cache[t]=n),new i(i._cache[t])},i}),x.define("select2/diacritics",[],function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Œ":"OE","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","œ":"oe","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ώ":"ω","ς":"σ","’":"'"}}),x.define("select2/data/base",["../utils"],function(t){function e(t,i){e.__super__.constructor.call(this)}return t.Extend(e,t.Observable),e.prototype.current=function(t){throw new Error("The `current` method must be defined in child classes.")},e.prototype.query=function(t,e){throw new Error("The `query` method must be defined in child classes.")},e.prototype.bind=function(t,e){},e.prototype.destroy=function(){},e.prototype.generateResultId=function(e,i){return e=e.id+"-result-",e+=t.generateChars(4),null!=i.id?e+="-"+i.id.toString():e+="-"+t.generateChars(4),e},e}),x.define("select2/data/select",["./base","../utils","jquery"],function(t,e,i){function n(t,e){this.$element=t,this.options=e,n.__super__.constructor.call(this)}return e.Extend(n,t),n.prototype.current=function(t){var e=this;t(Array.prototype.map.call(this.$element[0].querySelectorAll(":checked"),function(t){return e.item(i(t))}))},n.prototype.select=function(t){var e,i=this;if(t.selected=!0,null!=t.element&&"option"===t.element.tagName.toLowerCase())return t.element.selected=!0,void this.$element.trigger("input").trigger("change");this.$element.prop("multiple")?this.current(function(e){var n=[];(t=[t]).push.apply(t,e);for(var s=0;s<t.length;s++){var o=t[s].id;-1===n.indexOf(o)&&n.push(o)}i.$element.val(n),i.$element.trigger("input").trigger("change")}):(e=t.id,this.$element.val(e),this.$element.trigger("input").trigger("change"))},n.prototype.unselect=function(t){var e=this;if(this.$element.prop("multiple")){if(t.selected=!1,null!=t.element&&"option"===t.element.tagName.toLowerCase())return t.element.selected=!1,void this.$element.trigger("input").trigger("change");this.current(function(i){for(var n=[],s=0;s<i.length;s++){var o=i[s].id;o!==t.id&&-1===n.indexOf(o)&&n.push(o)}e.$element.val(n),e.$element.trigger("input").trigger("change")})}},n.prototype.bind=function(t,e){var i=this;(this.container=t).on("select",function(t){i.select(t.data)}),t.on("unselect",function(t){i.unselect(t.data)})},n.prototype.destroy=function(){this.$element.find("*").each(function(){e.RemoveData(this)})},n.prototype.query=function(t,e){var n=[],s=this;this.$element.children().each(function(){var e;"option"!==this.tagName.toLowerCase()&&"optgroup"!==this.tagName.toLowerCase()||(e=i(this),e=s.item(e),null!==(e=s.matches(t,e))&&n.push(e))}),e({results:n})},n.prototype.addOptions=function(t){this.$element.append(t)},n.prototype.option=function(t){var n;return t.children?(n=document.createElement("optgroup")).label=t.text:void 0!==(n=document.createElement("option")).textContent?n.textContent=t.text:n.innerText=t.text,void 0!==t.id&&(n.value=t.id),t.disabled&&(n.disabled=!0),t.selected&&(n.selected=!0),t.title&&(n.title=t.title),(t=this._normalizeItem(t)).element=n,e.StoreData(n,"data",t),i(n)},n.prototype.item=function(t){var n={};if(null!=(n=e.GetData(t[0],"data")))return n;var s=t[0];if("option"===s.tagName.toLowerCase())n={id:t.val(),text:t.text(),disabled:t.prop("disabled"),selected:t.prop("selected"),title:t.prop("title")};else if("optgroup"===s.tagName.toLowerCase()){n={text:t.prop("label"),children:[],title:t.prop("title")};for(var o=t.children("option"),r=[],a=0;a<o.length;a++){var l=i(o[a]);l=this.item(l);r.push(l)}n.children=r}return(n=this._normalizeItem(n)).element=t[0],e.StoreData(t[0],"data",n),n},n.prototype._normalizeItem=function(t){return t!==Object(t)&&(t={id:t,text:t}),null!=(t=i.extend({},{text:""},t)).id&&(t.id=t.id.toString()),null!=t.text&&(t.text=t.text.toString()),null==t._resultId&&t.id&&null!=this.container&&(t._resultId=this.generateResultId(this.container,t)),i.extend({},{selected:!1,disabled:!1},t)},n.prototype.matches=function(t,e){return this.options.get("matcher")(t,e)},n}),x.define("select2/data/array",["./select","../utils","jquery"],function(t,e,i){function n(t,e){this._dataToConvert=e.get("data")||[],n.__super__.constructor.call(this,t,e)}return e.Extend(n,t),n.prototype.bind=function(t,e){n.__super__.bind.call(this,t,e),this.addOptions(this.convertToOptions(this._dataToConvert))},n.prototype.select=function(t){var e=this.$element.find("option").filter(function(e,i){return i.value==t.id.toString()});0===e.length&&(e=this.option(t),this.addOptions(e)),n.__super__.select.call(this,t)},n.prototype.convertToOptions=function(t){for(var e=this,n=this.$element.find("option"),s=n.map(function(){return e.item(i(this)).id}).get(),o=[],r=0;r<t.length;r++){var a,l,c=this._normalizeItem(t[r]);0<=s.indexOf(c.id)?(a=n.filter(function(t){return function(){return i(this).val()==t.id}}(c)),l=this.item(a),l=i.extend(!0,{},c,l),l=this.option(l),a.replaceWith(l)):(l=this.option(c),c.children&&(c=this.convertToOptions(c.children),l.append(c)),o.push(l))}return o},n}),x.define("select2/data/ajax",["./array","../utils","jquery"],function(t,e,i){function n(t,e){this.ajaxOptions=this._applyDefaults(e.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),n.__super__.constructor.call(this,t,e)}return e.Extend(n,t),n.prototype._applyDefaults=function(t){var e={data:function(t){return i.extend({},t,{q:t.term})},transport:function(t,e,n){return(t=i.ajax(t)).then(e),t.fail(n),t}};return i.extend({},e,t,!0)},n.prototype.processResults=function(t){return t},n.prototype.query=function(t,e){var n=this;null!=this._request&&("function"==typeof this._request.abort&&this._request.abort(),this._request=null);var s=i.extend({type:"GET"},this.ajaxOptions);function o(){var i=s.transport(s,function(i){i=n.processResults(i,t),n.options.get("debug")&&window.console&&console.error&&(i&&i.results&&Array.isArray(i.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),e(i)},function(){"status"in i&&(0===i.status||"0"===i.status)||n.trigger("results:message",{message:"errorLoading"})});n._request=i}"function"==typeof s.url&&(s.url=s.url.call(this.$element,t)),"function"==typeof s.data&&(s.data=s.data.call(this.$element,t)),this.ajaxOptions.delay&&null!=t.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(o,this.ajaxOptions.delay)):o()},n}),x.define("select2/data/tags",["jquery"],function(t){function e(t,e,i){var n=i.get("tags"),s=i.get("createTag");if(void 0!==s&&(this.createTag=s),void 0!==(s=i.get("insertTag"))&&(this.insertTag=s),t.call(this,e,i),Array.isArray(n))for(var o=0;o<n.length;o++){var r=n[o];r=this._normalizeItem(r),r=this.option(r);this.$element.append(r)}}return e.prototype.query=function(t,e,i){var n=this;this._removeOldTags(),null!=e.term&&null==e.page?t.call(this,e,function t(s,o){for(var r=s.results,a=0;a<r.length;a++){var l=r[a],c=null!=l.children&&!t({results:l.children},!0);if((l.text||"").toUpperCase()===(e.term||"").toUpperCase()||c)return!o&&(s.data=r,void i(s))}if(o)return!0;var h,u=n.createTag(e);null!=u&&((h=n.option(u)).attr("data-select2-tag","true"),n.addOptions([h]),n.insertTag(r,u)),s.results=r,i(s)}):t.call(this,e,i)},e.prototype.createTag=function(t,e){return null==e.term?null:""===(e=e.term.trim())?null:{id:e,text:e}},e.prototype.insertTag=function(t,e,i){e.unshift(i)},e.prototype._removeOldTags=function(e){this.$element.find("option[data-select2-tag]").each(function(){this.selected||t(this).remove()})},e}),x.define("select2/data/tokenizer",["jquery"],function(t){function e(t,e,i){var n=i.get("tokenizer");void 0!==n&&(this.tokenizer=n),t.call(this,e,i)}return e.prototype.bind=function(t,e,i){t.call(this,e,i),this.$search=e.dropdown.$search||e.selection.$search||i.find(".select2-search__field")},e.prototype.query=function(e,i,n){var s=this;i.term=i.term||"";var o=this.tokenizer(i,this.options,function(e){var i,n=s._normalizeItem(e);s.$element.find("option").filter(function(){return t(this).val()===n.id}).length||((i=s.option(n)).attr("data-select2-tag",!0),s._removeOldTags(),s.addOptions([i])),i=n,s.trigger("select",{data:i})});o.term!==i.term&&(this.$search.length&&(this.$search.val(o.term),this.$search.trigger("focus")),i.term=o.term),e.call(this,i,n)},e.prototype.tokenizer=function(e,i,n,s){for(var o=n.get("tokenSeparators")||[],r=i.term,a=0,l=this.createTag||function(t){return{id:t.term,text:t.term}};a<r.length;){var c=r[a];-1!==o.indexOf(c)?(c=r.substr(0,a),null!=(c=l(t.extend({},i,{term:c})))?(s(c),r=r.substr(a+1)||"",a=0):a++):a++}return{term:r}},e}),x.define("select2/data/minimumInputLength",[],function(){function t(t,e,i){this.minimumInputLength=i.get("minimumInputLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){e.term=e.term||"",e.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:e.term,params:e}}):t.call(this,e,i)},t}),x.define("select2/data/maximumInputLength",[],function(){function t(t,e,i){this.maximumInputLength=i.get("maximumInputLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){e.term=e.term||"",0<this.maximumInputLength&&e.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:e.term,params:e}}):t.call(this,e,i)},t}),x.define("select2/data/maximumSelectionLength",[],function(){function t(t,e,i){this.maximumSelectionLength=i.get("maximumSelectionLength"),t.call(this,e,i)}return t.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("select",function(){n._checkIfMaximumSelected()})},t.prototype.query=function(t,e,i){var n=this;this._checkIfMaximumSelected(function(){t.call(n,e,i)})},t.prototype._checkIfMaximumSelected=function(t,e){var i=this;this.current(function(t){t=null!=t?t.length:0,0<i.maximumSelectionLength&&t>=i.maximumSelectionLength?i.trigger("results:message",{message:"maximumSelected",args:{maximum:i.maximumSelectionLength}}):e&&e()})},t}),x.define("select2/dropdown",["jquery","./utils"],function(t,e){function i(t,e){this.$element=t,this.options=e,i.__super__.constructor.call(this)}return e.Extend(i,e.Observable),i.prototype.render=function(){var e=t('<span class="select2-dropdown"><span class="select2-results"></span></span>');return e.attr("dir",this.options.get("dir")),this.$dropdown=e},i.prototype.bind=function(){},i.prototype.position=function(t,e){},i.prototype.destroy=function(){this.$dropdown.remove()},i}),x.define("select2/dropdown/search",["jquery"],function(t){function e(){}return e.prototype.render=function(e){var i=e.call(this),n=this.options.get("translations").get("search");e=t('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /></span>');return this.$searchContainer=e,this.$search=e.find("input"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",n()),i.prepend(e),i},e.prototype.bind=function(e,i,n){var s=this,o=i.id+"-results";e.call(this,i,n),this.$search.on("keydown",function(t){s.trigger("keypress",t),s._keyUpPrevented=t.isDefaultPrevented()}),this.$search.on("input",function(e){t(this).off("keyup")}),this.$search.on("keyup input",function(t){s.handleSearch(t)}),i.on("open",function(){s.$search.attr("tabindex",0),s.$search.attr("aria-controls",o),s.$search.trigger("focus"),window.setTimeout(function(){s.$search.trigger("focus")},0)}),i.on("close",function(){s.$search.attr("tabindex",-1),s.$search.removeAttr("aria-controls"),s.$search.removeAttr("aria-activedescendant"),s.$search.val(""),s.$search.trigger("blur")}),i.on("focus",function(){i.isOpen()||s.$search.trigger("focus")}),i.on("results:all",function(t){null!=t.query.term&&""!==t.query.term||(s.showSearch(t)?s.$searchContainer[0].classList.remove("select2-search--hide"):s.$searchContainer[0].classList.add("select2-search--hide"))}),i.on("results:focus",function(t){t.data._resultId?s.$search.attr("aria-activedescendant",t.data._resultId):s.$search.removeAttr("aria-activedescendant")})},e.prototype.handleSearch=function(t){var e;this._keyUpPrevented||(e=this.$search.val(),this.trigger("query",{term:e})),this._keyUpPrevented=!1},e.prototype.showSearch=function(t,e){return!0},e}),x.define("select2/dropdown/hidePlaceholder",[],function(){function t(t,e,i,n){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),t.call(this,e,i,n)}return t.prototype.append=function(t,e){e.results=this.removePlaceholder(e.results),t.call(this,e)},t.prototype.normalizePlaceholder=function(t,e){return"string"==typeof e?{id:"",text:e}:e},t.prototype.removePlaceholder=function(t,e){for(var i=e.slice(0),n=e.length-1;0<=n;n--){var s=e[n];this.placeholder.id===s.id&&i.splice(n,1)}return i},t}),x.define("select2/dropdown/infiniteScroll",["jquery"],function(t){function e(t,e,i,n){this.lastParams={},t.call(this,e,i,n),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return e.prototype.append=function(t,e){this.$loadingMore.remove(),this.loading=!1,t.call(this,e),this.showLoadingMore(e)&&(this.$results.append(this.$loadingMore),this.loadMoreIfNeeded())},e.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("query",function(t){n.lastParams=t,n.loading=!0}),e.on("query:append",function(t){n.lastParams=t,n.loading=!0}),this.$results.on("scroll",this.loadMoreIfNeeded.bind(this))},e.prototype.loadMoreIfNeeded=function(){var e=t.contains(document.documentElement,this.$loadingMore[0]);!this.loading&&e&&(e=this.$results.offset().top+this.$results.outerHeight(!1),this.$loadingMore.offset().top+this.$loadingMore.outerHeight(!1)<=e+50&&this.loadMore())},e.prototype.loadMore=function(){this.loading=!0;var e=t.extend({},{page:1},this.lastParams);e.page++,this.trigger("query:append",e)},e.prototype.showLoadingMore=function(t,e){return e.pagination&&e.pagination.more},e.prototype.createLoadingMore=function(){var e=t('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),i=this.options.get("translations").get("loadingMore");return e.html(i(this.lastParams)),e},e}),x.define("select2/dropdown/attachBody",["jquery","../utils"],function(t,e){function i(e,i,n){this.$dropdownParent=t(n.get("dropdownParent")||document.body),e.call(this,i,n)}return i.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("open",function(){n._showDropdown(),n._attachPositioningHandler(e),n._bindContainerResultHandlers(e)}),e.on("close",function(){n._hideDropdown(),n._detachPositioningHandler(e)}),this.$dropdownContainer.on("mousedown",function(t){t.stopPropagation()})},i.prototype.destroy=function(t){t.call(this),this.$dropdownContainer.remove()},i.prototype.position=function(t,e,i){e.attr("class",i.attr("class")),e[0].classList.remove("select2"),e[0].classList.add("select2-container--open"),e.css({position:"absolute",top:-999999}),this.$container=i},i.prototype.render=function(e){var i=t("<span></span>");e=e.call(this);return i.append(e),this.$dropdownContainer=i},i.prototype._hideDropdown=function(t){this.$dropdownContainer.detach()},i.prototype._bindContainerResultHandlers=function(t,e){var i;this._containerResultsHandlersBound||(i=this,e.on("results:all",function(){i._positionDropdown(),i._resizeDropdown()}),e.on("results:append",function(){i._positionDropdown(),i._resizeDropdown()}),e.on("results:message",function(){i._positionDropdown(),i._resizeDropdown()}),e.on("select",function(){i._positionDropdown(),i._resizeDropdown()}),e.on("unselect",function(){i._positionDropdown(),i._resizeDropdown()}),this._containerResultsHandlersBound=!0)},i.prototype._attachPositioningHandler=function(i,n){var s=this,o="scroll.select2."+n.id,r="resize.select2."+n.id,a="orientationchange.select2."+n.id;(n=this.$container.parents().filter(e.hasScroll)).each(function(){e.StoreData(this,"select2-scroll-position",{x:t(this).scrollLeft(),y:t(this).scrollTop()})}),n.on(o,function(i){var n=e.GetData(this,"select2-scroll-position");t(this).scrollTop(n.y)}),t(window).on(o+" "+r+" "+a,function(t){s._positionDropdown(),s._resizeDropdown()})},i.prototype._detachPositioningHandler=function(i,n){var s="scroll.select2."+n.id,o="resize.select2."+n.id;n="orientationchange.select2."+n.id;this.$container.parents().filter(e.hasScroll).off(s),t(window).off(s+" "+o+" "+n)},i.prototype._positionDropdown=function(){var e=t(window),i=this.$dropdown[0].classList.contains("select2-dropdown--above"),n=this.$dropdown[0].classList.contains("select2-dropdown--below"),s=null,o=this.$container.offset();o.bottom=o.top+this.$container.outerHeight(!1);var r={height:this.$container.outerHeight(!1)};r.top=o.top,r.bottom=o.top+r.height;var a=this.$dropdown.outerHeight(!1),l=e.scrollTop(),c=e.scrollTop()+e.height(),h=l<o.top-a;e=c>o.bottom+a,l={left:o.left,top:r.bottom};"static"===(c=this.$dropdownParent).css("position")&&(c=c.offsetParent()),o={top:0,left:0},(t.contains(document.body,c[0])||c[0].isConnected)&&(o=c.offset()),l.top-=o.top,l.left-=o.left,i||n||(s="below"),e||!h||i?!h&&e&&i&&(s="below"):s="above",("above"==s||i&&"below"!==s)&&(l.top=r.top-o.top-a),null!=s&&(this.$dropdown[0].classList.remove("select2-dropdown--below"),this.$dropdown[0].classList.remove("select2-dropdown--above"),this.$dropdown[0].classList.add("select2-dropdown--"+s),this.$container[0].classList.remove("select2-container--below"),this.$container[0].classList.remove("select2-container--above"),this.$container[0].classList.add("select2-container--"+s)),this.$dropdownContainer.css(l)},i.prototype._resizeDropdown=function(){var t={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(t.minWidth=t.width,t.position="relative",t.width="auto"),this.$dropdown.css(t)},i.prototype._showDropdown=function(t){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},i}),x.define("select2/dropdown/minimumResultsForSearch",[],function(){function t(t,e,i,n){this.minimumResultsForSearch=i.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),t.call(this,e,i,n)}return t.prototype.showSearch=function(t,e){return!(function t(e){for(var i=0,n=0;n<e.length;n++){var s=e[n];s.children?i+=t(s.children):i++}return i}(e.data.results)<this.minimumResultsForSearch)&&t.call(this,e)},t}),x.define("select2/dropdown/selectOnClose",["../utils"],function(t){function e(){}return e.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("close",function(t){n._handleSelectOnClose(t)})},e.prototype._handleSelectOnClose=function(e,i){if(i&&null!=i.originalSelect2Event){var n=i.originalSelect2Event;if("select"===n._type||"unselect"===n._type)return}(n=this.getHighlightedResults()).length<1||null!=(n=t.GetData(n[0],"data")).element&&n.element.selected||null==n.element&&n.selected||this.trigger("select",{data:n})},e}),x.define("select2/dropdown/closeOnSelect",[],function(){function t(){}return t.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("select",function(t){n._selectTriggered(t)}),e.on("unselect",function(t){n._selectTriggered(t)})},t.prototype._selectTriggered=function(t,e){var i=e.originalEvent;i&&(i.ctrlKey||i.metaKey)||this.trigger("close",{originalEvent:i,originalSelect2Event:e})},t}),x.define("select2/dropdown/dropdownCss",["../utils"],function(t){function e(){}return e.prototype.render=function(e){var i=e.call(this);return-1!==(e=this.options.get("dropdownCssClass")||"").indexOf(":all:")&&(e=e.replace(":all:",""),t.copyNonInternalCssClasses(i[0],this.$element[0])),i.addClass(e),i},e}),x.define("select2/dropdown/tagsSearchHighlight",["../utils"],function(t){function e(){}return e.prototype.highlightFirstItem=function(e){if(0<(i=this.$results.find(".select2-results__option--selectable:not(.select2-results__option--selected)")).length){var i,n=i.first();if((i=t.GetData(n[0],"data").element)&&i.getAttribute&&"true"===i.getAttribute("data-select2-tag"))return void n.trigger("mouseenter")}e.call(this)},e}),x.define("select2/i18n/en",[],function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(t){var e=t.input.length-t.maximum;t="Please delete "+e+" character";return 1!=e&&(t+="s"),t},inputTooShort:function(t){return"Please enter "+(t.minimum-t.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(t){var e="You can only select "+t.maximum+" item";return 1!=t.maximum&&(e+="s"),e},noResults:function(){return"No results found"},searching:function(){return"Searching…"},removeAllItems:function(){return"Remove all items"},removeItem:function(){return"Remove item"},search:function(){return"Search"}}}),x.define("select2/defaults",["jquery","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/selectionCss","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./dropdown/dropdownCss","./dropdown/tagsSearchHighlight","./i18n/en"],function(t,e,i,n,s,o,r,a,l,c,h,u,d,p,f,m,g,v,y,b,_,w,x,k,D,C,S,T,M,E,A){function O(){this.reset()}return O.prototype.apply=function(h){var u;null==(h=t.extend(!0,{},this.defaults,h)).dataAdapter&&(null!=h.ajax?h.dataAdapter=f:null!=h.data?h.dataAdapter=p:h.dataAdapter=d,0<h.minimumInputLength&&(h.dataAdapter=c.Decorate(h.dataAdapter,v)),0<h.maximumInputLength&&(h.dataAdapter=c.Decorate(h.dataAdapter,y)),0<h.maximumSelectionLength&&(h.dataAdapter=c.Decorate(h.dataAdapter,b)),h.tags&&(h.dataAdapter=c.Decorate(h.dataAdapter,m)),null==h.tokenSeparators&&null==h.tokenizer||(h.dataAdapter=c.Decorate(h.dataAdapter,g))),null==h.resultsAdapter&&(h.resultsAdapter=e,null!=h.ajax&&(h.resultsAdapter=c.Decorate(h.resultsAdapter,k)),null!=h.placeholder&&(h.resultsAdapter=c.Decorate(h.resultsAdapter,x)),h.selectOnClose&&(h.resultsAdapter=c.Decorate(h.resultsAdapter,S)),h.tags&&(h.resultsAdapter=c.Decorate(h.resultsAdapter,E))),null==h.dropdownAdapter&&(h.multiple?h.dropdownAdapter=_:(u=c.Decorate(_,w),h.dropdownAdapter=u),0!==h.minimumResultsForSearch&&(h.dropdownAdapter=c.Decorate(h.dropdownAdapter,C)),h.closeOnSelect&&(h.dropdownAdapter=c.Decorate(h.dropdownAdapter,T)),null!=h.dropdownCssClass&&(h.dropdownAdapter=c.Decorate(h.dropdownAdapter,M)),h.dropdownAdapter=c.Decorate(h.dropdownAdapter,D)),null==h.selectionAdapter&&(h.multiple?h.selectionAdapter=n:h.selectionAdapter=i,null!=h.placeholder&&(h.selectionAdapter=c.Decorate(h.selectionAdapter,s)),h.allowClear&&(h.selectionAdapter=c.Decorate(h.selectionAdapter,o)),h.multiple&&(h.selectionAdapter=c.Decorate(h.selectionAdapter,r)),null!=h.selectionCssClass&&(h.selectionAdapter=c.Decorate(h.selectionAdapter,a)),h.selectionAdapter=c.Decorate(h.selectionAdapter,l)),h.language=this._resolveLanguage(h.language),h.language.push("en");for(var A=[],O=0;O<h.language.length;O++){var N=h.language[O];-1===A.indexOf(N)&&A.push(N)}return h.language=A,h.translations=this._processTranslations(h.language,h.debug),h},O.prototype.reset=function(){function e(t){return t.replace(/[^\u0000-\u007E]/g,function(t){return u[t]||t})}this.defaults={amdLanguageBase:"./i18n/",autocomplete:"off",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:c.escapeMarkup,language:{},matcher:function i(n,s){if(null==n.term||""===n.term.trim())return s;if(s.children&&0<s.children.length){for(var o=t.extend(!0,{},s),r=s.children.length-1;0<=r;r--)null==i(n,s.children[r])&&o.children.splice(r,1);return 0<o.children.length?o:i(n,o)}var a=e(s.text).toUpperCase(),l=e(n.term).toUpperCase();return-1<a.indexOf(l)?s:null},minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,scrollAfterSelect:!1,sorter:function(t){return t},templateResult:function(t){return t.text},templateSelection:function(t){return t.text},theme:"default",width:"resolve"}},O.prototype.applyFromElement=function(t,e){var i=t.language,n=this.defaults.language,s=e.prop("lang");e=e.closest("[lang]").prop("lang"),e=Array.prototype.concat.call(this._resolveLanguage(s),this._resolveLanguage(i),this._resolveLanguage(n),this._resolveLanguage(e));return t.language=e,t},O.prototype._resolveLanguage=function(e){if(!e)return[];if(t.isEmptyObject(e))return[];if(t.isPlainObject(e))return[e];for(var i,n=Array.isArray(e)?e:[e],s=[],o=0;o<n.length;o++)s.push(n[o]),"string"==typeof n[o]&&0<n[o].indexOf("-")&&(i=n[o].split("-")[0],s.push(i));return s},O.prototype._processTranslations=function(e,i){for(var n=new h,s=0;s<e.length;s++){var o=new h,r=e[s];if("string"==typeof r)try{o=h.loadPath(r)}catch(e){try{r=this.defaults.amdLanguageBase+r,o=h.loadPath(r)}catch(e){i&&window.console&&console.warn&&console.warn('Select2: The language file for "'+r+'" could not be automatically loaded. A fallback will be used instead.')}}else o=t.isPlainObject(r)?new h(r):r;n.extend(o)}return n},O.prototype.set=function(e,i){var n={};n[t.camelCase(e)]=i,n=c._convertData(n),t.extend(!0,this.defaults,n)},new O}),x.define("select2/options",["jquery","./defaults","./utils"],function(t,e,i){function n(t,i){this.options=t,null!=i&&this.fromElement(i),null!=i&&(this.options=e.applyFromElement(this.options,i)),this.options=e.apply(this.options)}return n.prototype.fromElement=function(e){var n=["select2"];null==this.options.multiple&&(this.options.multiple=e.prop("multiple")),null==this.options.disabled&&(this.options.disabled=e.prop("disabled")),null==this.options.autocomplete&&e.prop("autocomplete")&&(this.options.autocomplete=e.prop("autocomplete")),null==this.options.dir&&(e.prop("dir")?this.options.dir=e.prop("dir"):e.closest("[dir]").prop("dir")?this.options.dir=e.closest("[dir]").prop("dir"):this.options.dir="ltr"),e.prop("disabled",this.options.disabled),e.prop("multiple",this.options.multiple),i.GetData(e[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),i.StoreData(e[0],"data",i.GetData(e[0],"select2Tags")),i.StoreData(e[0],"tags",!0)),i.GetData(e[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),e.attr("ajax--url",i.GetData(e[0],"ajaxUrl")),i.StoreData(e[0],"ajax-Url",i.GetData(e[0],"ajaxUrl")));var s={};function o(t,e){return e.toUpperCase()}for(var r=0;r<e[0].attributes.length;r++){var a=e[0].attributes[r].name,l="data-";a.substr(0,l.length)==l&&(a=a.substring(l.length),l=i.GetData(e[0],a),s[a.replace(/-([a-z])/g,o)]=l)}t.fn.jquery&&"1."==t.fn.jquery.substr(0,2)&&e[0].dataset&&(s=t.extend(!0,{},e[0].dataset,s));var c,h=t.extend(!0,{},i.GetData(e[0]),s);for(c in h=i._convertData(h))-1<n.indexOf(c)||(t.isPlainObject(this.options[c])?t.extend(this.options[c],h[c]):this.options[c]=h[c]);return this},n.prototype.get=function(t){return this.options[t]},n.prototype.set=function(t,e){this.options[t]=e},n}),x.define("select2/core",["jquery","./options","./utils","./keys"],function(t,e,i,n){var s=function(t,n){null!=i.GetData(t[0],"select2")&&i.GetData(t[0],"select2").destroy(),this.$element=t,this.id=this._generateId(t),n=n||{},this.options=new e(n,t),s.__super__.constructor.call(this);var o=t.attr("tabindex")||0;i.StoreData(t[0],"old-tabindex",o),t.attr("tabindex","-1"),n=this.options.get("dataAdapter"),this.dataAdapter=new n(t,this.options),o=this.render(),this._placeContainer(o),n=this.options.get("selectionAdapter"),this.selection=new n(t,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,o),n=this.options.get("dropdownAdapter"),this.dropdown=new n(t,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,o),o=this.options.get("resultsAdapter"),this.results=new o(t,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var r=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current(function(t){r.trigger("selection:update",{data:t})}),t[0].classList.add("select2-hidden-accessible"),t.attr("aria-hidden","true"),this._syncAttributes(),i.StoreData(t[0],"select2",this),t.data("select2",this)};return i.Extend(s,i.Observable),s.prototype._generateId=function(t){return"select2-"+(null!=t.attr("id")?t.attr("id"):null!=t.attr("name")?t.attr("name")+"-"+i.generateChars(2):i.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},s.prototype._placeContainer=function(t){t.insertAfter(this.$element);var e=this._resolveWidth(this.$element,this.options.get("width"));null!=e&&t.css("width",e)},s.prototype._resolveWidth=function(t,e){var i=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==e){var n=this._resolveWidth(t,"style");return null!=n?n:this._resolveWidth(t,"element")}if("element"==e)return(n=t.outerWidth(!1))<=0?"auto":n+"px";if("style"!=e)return"computedstyle"!=e?e:window.getComputedStyle(t[0]).width;if("string"!=typeof(t=t.attr("style")))return null;for(var s=t.split(";"),o=0,r=s.length;o<r;o+=1){var a=s[o].replace(/\s/g,"").match(i);if(null!==a&&1<=a.length)return a[1]}return null},s.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},s.prototype._registerDomEvents=function(){var t=this;this.$element.on("change.select2",function(){t.dataAdapter.current(function(e){t.trigger("selection:update",{data:e})})}),this.$element.on("focus.select2",function(e){t.trigger("focus",e)}),this._syncA=i.bind(this._syncAttributes,this),this._syncS=i.bind(this._syncSubtree,this),this._observer=new window.MutationObserver(function(e){t._syncA(),t._syncS(e)}),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})},s.prototype._registerDataEvents=function(){var t=this;this.dataAdapter.on("*",function(e,i){t.trigger(e,i)})},s.prototype._registerSelectionEvents=function(){var t=this,e=["toggle","focus"];this.selection.on("toggle",function(){t.toggleDropdown()}),this.selection.on("focus",function(e){t.focus(e)}),this.selection.on("*",function(i,n){-1===e.indexOf(i)&&t.trigger(i,n)})},s.prototype._registerDropdownEvents=function(){var t=this;this.dropdown.on("*",function(e,i){t.trigger(e,i)})},s.prototype._registerResultsEvents=function(){var t=this;this.results.on("*",function(e,i){t.trigger(e,i)})},s.prototype._registerEvents=function(){var t=this;this.on("open",function(){t.$container[0].classList.add("select2-container--open")}),this.on("close",function(){t.$container[0].classList.remove("select2-container--open")}),this.on("enable",function(){t.$container[0].classList.remove("select2-container--disabled")}),this.on("disable",function(){t.$container[0].classList.add("select2-container--disabled")}),this.on("blur",function(){t.$container[0].classList.remove("select2-container--focus")}),this.on("query",function(e){t.isOpen()||t.trigger("open",{}),this.dataAdapter.query(e,function(i){t.trigger("results:all",{data:i,query:e})})}),this.on("query:append",function(e){this.dataAdapter.query(e,function(i){t.trigger("results:append",{data:i,query:e})})}),this.on("keypress",function(e){var i=e.which;t.isOpen()?i===n.ESC||i===n.UP&&e.altKey?(t.close(e),e.preventDefault()):i===n.ENTER||i===n.TAB?(t.trigger("results:select",{}),e.preventDefault()):i===n.SPACE&&e.ctrlKey?(t.trigger("results:toggle",{}),e.preventDefault()):i===n.UP?(t.trigger("results:previous",{}),e.preventDefault()):i===n.DOWN&&(t.trigger("results:next",{}),e.preventDefault()):(i===n.ENTER||i===n.SPACE||i===n.DOWN&&e.altKey)&&(t.open(),e.preventDefault())})},s.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.isDisabled()?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},s.prototype._isChangeMutation=function(t){var e=this;if(t.addedNodes&&0<t.addedNodes.length){for(var i=0;i<t.addedNodes.length;i++)if(t.addedNodes[i].selected)return!0}else{if(t.removedNodes&&0<t.removedNodes.length)return!0;if(Array.isArray(t))return t.some(function(t){return e._isChangeMutation(t)})}return!1},s.prototype._syncSubtree=function(t){t=this._isChangeMutation(t);var e=this;t&&this.dataAdapter.current(function(t){e.trigger("selection:update",{data:t})})},s.prototype.trigger=function(t,e){var i=s.__super__.trigger;if(void 0===e&&(e={}),t in(o={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"})){var n=o[t],o={prevented:!1,name:t,args:e};if(i.call(this,n,o),o.prevented)return void(e.prevented=!0)}i.call(this,t,e)},s.prototype.toggleDropdown=function(){this.isDisabled()||(this.isOpen()?this.close():this.open())},s.prototype.open=function(){this.isOpen()||this.isDisabled()||this.trigger("query",{})},s.prototype.close=function(t){this.isOpen()&&this.trigger("close",{originalEvent:t})},s.prototype.isEnabled=function(){return!this.isDisabled()},s.prototype.isDisabled=function(){return this.options.get("disabled")},s.prototype.isOpen=function(){return this.$container[0].classList.contains("select2-container--open")},s.prototype.hasFocus=function(){return this.$container[0].classList.contains("select2-container--focus")},s.prototype.focus=function(t){this.hasFocus()||(this.$container[0].classList.add("select2-container--focus"),this.trigger("focus",{}))},s.prototype.enable=function(t){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),t=!(t=null==t||0===t.length?[!0]:t)[0],this.$element.prop("disabled",t)},s.prototype.data=function(){this.options.get("debug")&&0<arguments.length&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var t=[];return this.dataAdapter.current(function(e){t=e}),t},s.prototype.val=function(t){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==t||0===t.length)return this.$element.val();t=t[0],Array.isArray(t)&&(t=t.map(function(t){return t.toString()})),this.$element.val(t).trigger("input").trigger("change")},s.prototype.destroy=function(){i.RemoveData(this.$container[0]),this.$container.remove(),this._observer.disconnect(),this._observer=null,this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",i.GetData(this.$element[0],"old-tabindex")),this.$element[0].classList.remove("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),i.RemoveData(this.$element[0]),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},s.prototype.render=function(){var e=t('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return e.attr("dir",this.options.get("dir")),this.$container=e,this.$container[0].classList.add("select2-container--"+this.options.get("theme")),i.StoreData(e[0],"element",this.$element),e},s}),x.define("jquery-mousewheel",["jquery"],function(t){return t}),x.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],function(t,e,i,n,s){var o;return null==t.fn.select2&&(o=["open","close","destroy"],t.fn.select2=function(e){if("object"==typeof(e=e||{}))return this.each(function(){var n=t.extend(!0,{},e);new i(t(this),n)}),this;if("string"!=typeof e)throw new Error("Invalid arguments for Select2: "+e);var n,r=Array.prototype.slice.call(arguments,1);return this.each(function(){var t=s.GetData(this,"select2");null==t&&window.console&&console.error&&console.error("The select2('"+e+"') method was called on an element that is not using Select2."),n=t[e].apply(t,r)}),-1<o.indexOf(e)?this:n}),null==t.fn.select2.defaults&&(t.fn.select2.defaults=n),i}),{define:x.define,require:x.require})).require("jquery.select2");return t.fn.select2.amd=m,x});var $jscomp=$jscomp||{};function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}$jscomp.scope={},$jscomp.findInternal=function(t,e,i){t instanceof String&&(t=String(t));for(var n=t.length,s=0;s<n;s++){var o=t[s];if(e.call(i,o,s,t))return{i:s,v:o}}return{i:-1,v:void 0}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,i){t!=Array.prototype&&t!=Object.prototype&&(t[e]=i.value)},$jscomp.getGlobal=function(t){return"undefined"!=typeof window&&window===t?t:"undefined"!=typeof global&&null!=global?global:t},$jscomp.global=$jscomp.getGlobal(this),$jscomp.polyfill=function(t,e,i,n){if(e){for(i=$jscomp.global,t=t.split("."),n=0;n<t.length-1;n++){var s=t[n];s in i||(i[s]={}),i=i[s]}(e=e(n=i[t=t[t.length-1]]))!=n&&null!=e&&$jscomp.defineProperty(i,t,{configurable:!0,writable:!0,value:e})}},$jscomp.polyfill("Array.prototype.find",function(t){return t||function(t,e){return $jscomp.findInternal(this,t,e).v}},"es6","es3"),function(t,e,i){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports&&"undefined"==typeof Meteor?module.exports=t(require("jquery")):t(e||i)}(function(t){var e=function(e,i,n){var s={invalid:[],getCaret:function(){try{var t=0,i=e.get(0),n=document.selection,o=i.selectionStart;if(n&&-1===navigator.appVersion.indexOf("MSIE 10")){var r=n.createRange();r.moveStart("character",-s.val().length),t=r.text.length}else(o||"0"===o)&&(t=o);return t}catch(t){}},setCaret:function(t){try{if(e.is(":focus")){var i=e.get(0);if(i.setSelectionRange)i.setSelectionRange(t,t);else{var n=i.createTextRange();n.collapse(!0),n.moveEnd("character",t),n.moveStart("character",t),n.select()}}}catch(t){}},events:function(){e.on("keydown.mask",function(t){e.data("mask-keycode",t.keyCode||t.which),e.data("mask-previus-value",e.val()),e.data("mask-previus-caret-pos",s.getCaret()),s.maskDigitPosMapOld=s.maskDigitPosMap}).on(t.jMaskGlobals.useInput?"input.mask":"keyup.mask",s.behaviour).on("paste.mask drop.mask",function(){setTimeout(function(){e.keydown().keyup()},100)}).on("change.mask",function(){e.data("changed",!0)}).on("blur.mask",function(){a===s.val()||e.data("changed")||e.trigger("change"),e.data("changed",!1)}).on("blur.mask",function(){a=s.val()}).on("focus.mask",function(e){!0===n.selectOnFocus&&t(e.target).select()}).on("focusout.mask",function(){n.clearIfNotMatch&&!o.test(s.val())&&s.val("")})},getRegexMask:function(){for(var t,e,n,s,o=[],a=0;a<i.length;a++)(t=r.translation[i.charAt(a)])?(e=t.pattern.toString().replace(/.{1}$|^.{1}/g,""),n=t.optional,(t=t.recursive)?(o.push(i.charAt(a)),s={digit:i.charAt(a),pattern:e}):o.push(n||t?e+"?":e)):o.push(i.charAt(a).replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"));return o=o.join(""),s&&(o=o.replace(new RegExp("("+s.digit+"(.*"+s.digit+")?)"),"($1)?").replace(new RegExp(s.digit,"g"),s.pattern)),new RegExp(o)},destroyEvents:function(){e.off("input keydown keyup paste drop blur focusout ".split(" ").join(".mask "))},val:function(t){var i=e.is("input")?"val":"text";return 0<arguments.length?(e[i]()!==t&&e[i](t),i=e):i=e[i](),i},calculateCaretPosition:function(t){var i=s.getMasked(),n=s.getCaret();if(t!==i){var o=e.data("mask-previus-caret-pos")||0;i=i.length;var r,a=t.length,l=t=0,c=0,h=0;for(r=n;r<i&&s.maskDigitPosMap[r];r++)l++;for(r=n-1;0<=r&&s.maskDigitPosMap[r];r--)t++;for(r=n-1;0<=r;r--)s.maskDigitPosMap[r]&&c++;for(r=o-1;0<=r;r--)s.maskDigitPosMapOld[r]&&h++;n>a?n=10*i:o>=n&&o!==a?s.maskDigitPosMapOld[n]||(o=n,n=n-(h-c)-t,s.maskDigitPosMap[n]&&(n=o)):n>o&&(n=n+(c-h)+l)}return n},behaviour:function(i){i=i||window.event,s.invalid=[];var n=e.data("mask-keycode");if(-1===t.inArray(n,r.byPassKeys)){n=s.getMasked();var o=s.getCaret(),a=e.data("mask-previus-value")||"";return setTimeout(function(){s.setCaret(s.calculateCaretPosition(a))},t.jMaskGlobals.keyStrokeCompensation),s.val(n),s.setCaret(o),s.callbacks(i)}},getMasked:function(t,e){var o,a=[],l=void 0===e?s.val():e+"",c=0,h=i.length,u=0,d=l.length,p=1,f="push",m=-1,g=0;if(e=[],n.reverse){f="unshift",p=-1;var v=0;c=h-1,u=d-1;var y=function(){return-1<c&&-1<u}}else v=h-1,y=function(){return c<h&&u<d};for(;y();){var b=i.charAt(c),_=l.charAt(u),w=r.translation[b];w?(_.match(w.pattern)?(a[f](_),w.recursive&&(-1===m?m=c:c===v&&c!==m&&(c=m-p),v===m&&(c-=p)),c+=p):_===o?(g--,o=void 0):w.optional?(c+=p,u-=p):w.fallback?(a[f](w.fallback),c+=p,u-=p):s.invalid.push({p:u,v:_,e:w.pattern}),u+=p):(t||a[f](b),_===b?(e.push(u),u+=p):(o=b,e.push(u+g),g++),c+=p)}return t=i.charAt(v),h!==d+1||r.translation[t]||a.push(t),a=a.join(""),s.mapMaskdigitPositions(a,e,d),a},mapMaskdigitPositions:function(t,e,i){for(t=n.reverse?t.length-i:0,s.maskDigitPosMap={},i=0;i<e.length;i++)s.maskDigitPosMap[e[i]+t]=1},callbacks:function(t){var o=s.val(),r=o!==a,l=[o,t,e,n],c=function(t,e,i){"function"==typeof n[t]&&e&&n[t].apply(this,i)};c("onChange",!0===r,l),c("onKeyPress",!0===r,l),c("onComplete",o.length===i.length,l),c("onInvalid",0<s.invalid.length,[o,t,e,s.invalid,n])}};e=t(e);var o,r=this,a=s.val();i="function"==typeof i?i(s.val(),void 0,e,n):i,r.mask=i,r.options=n,r.remove=function(){var t=s.getCaret();return r.options.placeholder&&e.removeAttr("placeholder"),e.data("mask-maxlength")&&e.removeAttr("maxlength"),s.destroyEvents(),s.val(r.getCleanVal()),s.setCaret(t),e},r.getCleanVal=function(){return s.getMasked(!0)},r.getMaskedVal=function(t){return s.getMasked(!1,t)},r.init=function(a){if(a=a||!1,n=n||{},r.clearIfNotMatch=t.jMaskGlobals.clearIfNotMatch,r.byPassKeys=t.jMaskGlobals.byPassKeys,r.translation=t.extend({},t.jMaskGlobals.translation,n.translation),r=t.extend(!0,{},r,n),o=s.getRegexMask(),a)s.events(),s.val(s.getMasked());else{n.placeholder&&e.attr("placeholder",n.placeholder),e.data("mask")&&e.attr("autocomplete","off"),a=0;for(var l=!0;a<i.length;a++){var c=r.translation[i.charAt(a)];if(c&&c.recursive){l=!1;break}}l&&e.attr("maxlength",i.length).data("mask-maxlength",!0),s.destroyEvents(),s.events(),a=s.getCaret(),s.val(s.getMasked()),s.setCaret(a)}},r.init(!e.is("input"))};t.maskWatchers={};var i=function(){var i=t(this),s={},o=i.attr("data-mask");if(i.attr("data-mask-reverse")&&(s.reverse=!0),i.attr("data-mask-clearifnotmatch")&&(s.clearIfNotMatch=!0),"true"===i.attr("data-mask-selectonfocus")&&(s.selectOnFocus=!0),n(i,o,s))return i.data("mask",new e(this,o,s))},n=function(e,i,n){n=n||{};var s=t(e).data("mask"),o=JSON.stringify;e=t(e).val()||t(e).text();try{return"function"==typeof i&&(i=i(e)),"object"!=typeof s||o(s.options)!==o(n)||s.mask!==i}catch(t){}},s=function(t){var e=document.createElement("div"),i=(t="on"+t)in e;return i||(e.setAttribute(t,"return;"),i="function"==typeof e[t]),i};t.fn.mask=function(i,s){s=s||{};var o=this.selector,r=t.jMaskGlobals,a=r.watchInterval;r=s.watchInputs||r.watchInputs;var l=function(){if(n(this,i,s))return t(this).data("mask",new e(this,i,s))};return t(this).each(l),o&&""!==o&&r&&(clearInterval(t.maskWatchers[o]),t.maskWatchers[o]=setInterval(function(){t(document).find(o).each(l)},a)),this},t.fn.masked=function(t){return this.data("mask").getMaskedVal(t)},t.fn.unmask=function(){return clearInterval(t.maskWatchers[this.selector]),delete t.maskWatchers[this.selector],this.each(function(){var e=t(this).data("mask");e&&e.remove().removeData("mask")})},t.fn.cleanVal=function(){return this.data("mask").getCleanVal()},t.applyDataMask=function(e){((e=e||t.jMaskGlobals.maskElements)instanceof t?e:t(e)).filter(t.jMaskGlobals.dataMaskAttr).each(i)},s={maskElements:"input,td,span,div",dataMaskAttr:"*[data-mask]",dataMask:!0,watchInterval:300,watchInputs:!0,keyStrokeCompensation:10,useInput:!/Chrome\/[2-4][0-9]|SamsungBrowser/.test(window.navigator.userAgent)&&s("input"),watchDataMask:!1,byPassKeys:[9,16,17,18,36,37,38,39,40,91],translation:{0:{pattern:/\d/},9:{pattern:/\d/,optional:!0},"#":{pattern:/\d/,recursive:!0},A:{pattern:/[a-zA-Z0-9]/},S:{pattern:/[a-zA-Z]/}}},t.jMaskGlobals=t.jMaskGlobals||{},(s=t.jMaskGlobals=t.extend(!0,{},s,t.jMaskGlobals)).dataMask&&t.applyDataMask(),setInterval(function(){t.jMaskGlobals.watchDataMask&&t.applyDataMask()},s.watchInterval)},window.jQuery,window.Zepto),function(t){var e=function(e,i){e=t(e);var n=this,s=[],o=t.extend({},t.fn.bootstrapWizard.defaults,i),r=null,a=null;this.rebindClick=function(t,e){t.unbind("click",e).bind("click",e)},this.fixNavigationButtons=function(){if(r.length||(a.find("a:first").tab("show"),r=a.find('li:has([data-toggle="tab"]):first')),t(o.previousSelector,e).toggleClass("disabled",n.firstIndex()>=n.currentIndex()),t(o.nextSelector,e).toggleClass("disabled",n.currentIndex()>=n.navigationLength()),t(o.backSelector,e).toggleClass("disabled",0==s.length),n.rebindClick(t(o.nextSelector,e),n.next),n.rebindClick(t(o.previousSelector,e),n.previous),n.rebindClick(t(o.lastSelector,e),n.last),n.rebindClick(t(o.firstSelector,e),n.first),n.rebindClick(t(o.backSelector,e),n.back),o.onTabShow&&"function"==typeof o.onTabShow&&!1===o.onTabShow(r,a,n.currentIndex()))return!1},this.next=function(t){if(e.hasClass("last")||o.onNext&&"function"==typeof o.onNext&&!1===o.onNext(r,a,n.nextIndex()))return!1;t=n.currentIndex(),$index=n.nextIndex(),$index>n.navigationLength()||(s.push(t),a.find('li:has([data-toggle="tab"]):eq('+$index+") a").tab("show"))},this.previous=function(t){if(e.hasClass("first")||o.onPrevious&&"function"==typeof o.onPrevious&&!1===o.onPrevious(r,a,n.previousIndex()))return!1;t=n.currentIndex(),$index=n.previousIndex(),0>$index||(s.push(t),a.find('li:has([data-toggle="tab"]):eq('+$index+") a").tab("show"))},this.first=function(t){if(o.onFirst&&"function"==typeof o.onFirst&&!1===o.onFirst(r,a,n.firstIndex())||e.hasClass("disabled"))return!1;s.push(n.currentIndex()),a.find('li:has([data-toggle="tab"]):eq(0) a').tab("show")},this.last=function(t){if(o.onLast&&"function"==typeof o.onLast&&!1===o.onLast(r,a,n.lastIndex())||e.hasClass("disabled"))return!1;s.push(n.currentIndex()),a.find('li:has([data-toggle="tab"]):eq('+n.navigationLength()+") a").tab("show")},this.back=function(){if(0==s.length)return null;var t=s.pop();if(o.onBack&&"function"==typeof o.onBack&&!1===o.onBack(r,a,t))return s.push(t),!1;e.find('li:has([data-toggle="tab"]):eq('+t+") a").tab("show")},this.currentIndex=function(){return a.find('li:has([data-toggle="tab"])').index(r)},this.firstIndex=function(){return 0},this.lastIndex=function(){return n.navigationLength()},this.getIndex=function(t){return a.find('li:has([data-toggle="tab"])').index(t)},this.nextIndex=function(){return a.find('li:has([data-toggle="tab"])').index(r)+1},this.previousIndex=function(){return a.find('li:has([data-toggle="tab"])').index(r)-1},this.navigationLength=function(){return a.find('li:has([data-toggle="tab"])').length-1},this.activeTab=function(){return r},this.nextTab=function(){return a.find('li:has([data-toggle="tab"]):eq('+(n.currentIndex()+1)+")").length?a.find('li:has([data-toggle="tab"]):eq('+(n.currentIndex()+1)+")"):null},this.previousTab=function(){return 0>=n.currentIndex()?null:a.find('li:has([data-toggle="tab"]):eq('+parseInt(n.currentIndex()-1)+")")},this.show=function(t){0<(t=isNaN(t)?e.find('li:has([data-toggle="tab"]) a[href=#'+t+"]"):e.find('li:has([data-toggle="tab"]):eq('+t+") a")).length&&(s.push(n.currentIndex()),t.tab("show"))},this.disable=function(t){a.find('li:has([data-toggle="tab"]):eq('+t+")").addClass("disabled")},this.enable=function(t){a.find('li:has([data-toggle="tab"]):eq('+t+")").removeClass("disabled")},this.hide=function(t){a.find('li:has([data-toggle="tab"]):eq('+t+")").hide()},this.display=function(t){a.find('li:has([data-toggle="tab"]):eq('+t+")").show()},this.remove=function(e){var i=void 0!==e[1]&&e[1];e=a.find('li:has([data-toggle="tab"]):eq('+e[0]+")"),i&&(i=e.find("a").attr("href"),t(i).remove()),e.remove()};var l=function(e){var i=a.find('li:has([data-toggle="tab"])');if(e=i.index(t(e.currentTarget).parent('li:has([data-toggle="tab"])')),i=t(i[e]),o.onTabClick&&"function"==typeof o.onTabClick&&!1===o.onTabClick(r,a,n.currentIndex(),e,i))return!1},c=function(e){if($element=t(e.target).parent(),e=a.find('li:has([data-toggle="tab"])').index($element),$element.hasClass("disabled")||o.onTabChange&&"function"==typeof o.onTabChange&&!1===o.onTabChange(r,a,n.currentIndex(),e))return!1;r=$element,n.fixNavigationButtons()};this.resetWizard=function(){t('a[data-toggle="tab"]',a).off("click",l),t('a[data-toggle="tab"]',a).off("shown shown.bs.tab",c),a=e.find("ul:first",e),r=a.find('li:has([data-toggle="tab"]).active',e),t('a[data-toggle="tab"]',a).on("click",l),t('a[data-toggle="tab"]',a).on("shown shown.bs.tab",c),n.fixNavigationButtons()},a=e.find("ul:first",e),r=a.find('li:has([data-toggle="tab"]).active',e),a.hasClass(o.tabClass)||a.addClass(o.tabClass),o.onInit&&"function"==typeof o.onInit&&o.onInit(r,a,0),o.onShow&&"function"==typeof o.onShow&&o.onShow(r,a,n.nextIndex()),t('a[data-toggle="tab"]',a).on("click",l),t('a[data-toggle="tab"]',a).on("shown shown.bs.tab",c)};t.fn.bootstrapWizard=function(i){if("string"==typeof i){var n=Array.prototype.slice.call(arguments,1);return 1===n.length&&n.toString(),this.data("bootstrapWizard")[i](n)}return this.each(function(n){if(!(n=t(this)).data("bootstrapWizard")){var s=new e(n,i);n.data("bootstrapWizard",s),s.fixNavigationButtons()}})},t.fn.bootstrapWizard.defaults={tabClass:"nav nav-pills",nextSelector:".wizard li.next",previousSelector:".wizard li.previous",firstSelector:".wizard li.first",lastSelector:".wizard li.last",backSelector:".wizard li.back",onShow:null,onInit:null,onNext:null,onPrevious:null,onLast:null,onFirst:null,onBack:null,onTabChange:null,onTabClick:null,onTabShow:null}}(jQuery),function(t,e,i){"use strict";var n=function(e,i){this.widget="",this.$element=t(e),this.defaultTime=i.defaultTime,this.disableFocus=i.disableFocus,this.disableMousewheel=i.disableMousewheel,this.isOpen=i.isOpen,this.minuteStep=i.minuteStep,this.modalBackdrop=i.modalBackdrop,this.orientation=i.orientation,this.secondStep=i.secondStep,this.snapToStep=i.snapToStep,this.showInputs=i.showInputs,this.showMeridian=i.showMeridian,this.showSeconds=i.showSeconds,this.template=i.template,this.appendWidgetTo=i.appendWidgetTo,this.showWidgetOnAddonClick=i.showWidgetOnAddonClick,this.icons=i.icons,this.maxHours=i.maxHours,this.explicitMode=i.explicitMode,this.handleDocumentClick=function(t){var e=t.data.scope;e.$element.parent().find(t.target).length||e.$widget.is(t.target)||e.$widget.find(t.target).length||e.hideWidget()},this._init()};n.prototype={constructor:n,_init:function(){var e=this;this.showWidgetOnAddonClick&&this.$element.parent().hasClass("input-group")&&this.$element.parent().hasClass("bootstrap-timepicker")?(this.$element.parent(".input-group.bootstrap-timepicker").find(".input-group-addon").on({"click.timepicker":t.proxy(this.showWidget,this)}),this.$element.on({"focus.timepicker":t.proxy(this.highlightUnit,this),"click.timepicker":t.proxy(this.highlightUnit,this),"keydown.timepicker":t.proxy(this.elementKeydown,this),"blur.timepicker":t.proxy(this.blurElement,this),"mousewheel.timepicker DOMMouseScroll.timepicker":t.proxy(this.mousewheel,this)})):this.template?this.$element.on({"focus.timepicker":t.proxy(this.showWidget,this),"click.timepicker":t.proxy(this.showWidget,this),"blur.timepicker":t.proxy(this.blurElement,this),"mousewheel.timepicker DOMMouseScroll.timepicker":t.proxy(this.mousewheel,this)}):this.$element.on({"focus.timepicker":t.proxy(this.highlightUnit,this),"click.timepicker":t.proxy(this.highlightUnit,this),"keydown.timepicker":t.proxy(this.elementKeydown,this),"blur.timepicker":t.proxy(this.blurElement,this),"mousewheel.timepicker DOMMouseScroll.timepicker":t.proxy(this.mousewheel,this)}),!1!==this.template?this.$widget=t(this.getTemplate()).on("click",t.proxy(this.widgetClick,this)):this.$widget=!1,this.showInputs&&!1!==this.$widget&&this.$widget.find("input").each(function(){t(this).on({"click.timepicker":function(){t(this).select()},"keydown.timepicker":t.proxy(e.widgetKeydown,e),"keyup.timepicker":t.proxy(e.widgetKeyup,e)})}),this.setDefaultTime(this.defaultTime)},blurElement:function(){this.highlightedUnit=null,this.updateFromElementVal()},clear:function(){this.hour="",this.minute="",this.second="",this.meridian="",this.$element.val("")},decrementHour:function(){if(this.showMeridian)if(1===this.hour)this.hour=12;else{if(12===this.hour)return this.hour--,this.toggleMeridian();if(0===this.hour)return this.hour=11,this.toggleMeridian();this.hour--}else this.hour<=0?this.hour=this.maxHours-1:this.hour--},decrementMinute:function(t){var e;0>(e=t?this.minute-t:this.minute-this.minuteStep)?(this.decrementHour(),this.minute=e+60):this.minute=e},decrementSecond:function(){var t=this.second-this.secondStep;0>t?(this.decrementMinute(!0),this.second=t+60):this.second=t},elementKeydown:function(t){switch(t.which){case 9:if(t.shiftKey){if("hour"===this.highlightedUnit){this.hideWidget();break}this.highlightPrevUnit()}else{if(this.showMeridian&&"meridian"===this.highlightedUnit||this.showSeconds&&"second"===this.highlightedUnit||!this.showMeridian&&!this.showSeconds&&"minute"===this.highlightedUnit){this.hideWidget();break}this.highlightNextUnit()}t.preventDefault(),this.updateFromElementVal();break;case 27:this.updateFromElementVal();break;case 37:t.preventDefault(),this.highlightPrevUnit(),this.updateFromElementVal();break;case 38:switch(t.preventDefault(),this.highlightedUnit){case"hour":this.incrementHour(),this.highlightHour();break;case"minute":this.incrementMinute(),this.highlightMinute();break;case"second":this.incrementSecond(),this.highlightSecond();break;case"meridian":this.toggleMeridian(),this.highlightMeridian()}this.update();break;case 39:t.preventDefault(),this.highlightNextUnit(),this.updateFromElementVal();break;case 40:switch(t.preventDefault(),this.highlightedUnit){case"hour":this.decrementHour(),this.highlightHour();break;case"minute":this.decrementMinute(),this.highlightMinute();break;case"second":this.decrementSecond(),this.highlightSecond();break;case"meridian":this.toggleMeridian(),this.highlightMeridian()}this.update()}},getCursorPosition:function(){var t=this.$element.get(0);if("selectionStart"in t)return t.selectionStart;if(i.selection){t.focus();var e=i.selection.createRange(),n=i.selection.createRange().text.length;return e.moveStart("character",-t.value.length),e.text.length-n}},getTemplate:function(){var t,e,i,n,s,o;switch(this.showInputs?(e='<input type="text" class="bootstrap-timepicker-hour" maxlength="2"/>',i='<input type="text" class="bootstrap-timepicker-minute" maxlength="2"/>',n='<input type="text" class="bootstrap-timepicker-second" maxlength="2"/>',s='<input type="text" class="bootstrap-timepicker-meridian" maxlength="2"/>'):(e='<span class="bootstrap-timepicker-hour"></span>',i='<span class="bootstrap-timepicker-minute"></span>',n='<span class="bootstrap-timepicker-second"></span>',s='<span class="bootstrap-timepicker-meridian"></span>'),o='<table><tr><td><a href="#" data-action="incrementHour"><span class="'+this.icons.up+'"></span></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="incrementMinute"><span class="'+this.icons.up+'"></span></a></td>'+(this.showSeconds?'<td class="separator">&nbsp;</td><td><a href="#" data-action="incrementSecond"><span class="'+this.icons.up+'"></span></a></td>':"")+(this.showMeridian?'<td class="separator">&nbsp;</td><td class="meridian-column"><a href="#" data-action="toggleMeridian"><span class="'+this.icons.up+'"></span></a></td>':"")+"</tr><tr><td>"+e+'</td> <td class="separator">:</td><td>'+i+"</td> "+(this.showSeconds?'<td class="separator">:</td><td>'+n+"</td>":"")+(this.showMeridian?'<td class="separator">&nbsp;</td><td>'+s+"</td>":"")+'</tr><tr><td><a href="#" data-action="decrementHour"><span class="'+this.icons.down+'"></span></a></td><td class="separator"></td><td><a href="#" data-action="decrementMinute"><span class="'+this.icons.down+'"></span></a></td>'+(this.showSeconds?'<td class="separator">&nbsp;</td><td><a href="#" data-action="decrementSecond"><span class="'+this.icons.down+'"></span></a></td>':"")+(this.showMeridian?'<td class="separator">&nbsp;</td><td><a href="#" data-action="toggleMeridian"><span class="'+this.icons.down+'"></span></a></td>':"")+"</tr></table>",this.template){case"modal":t='<div class="bootstrap-timepicker-widget modal hide fade in" data-backdrop="'+(this.modalBackdrop?"true":"false")+'"><div class="modal-header"><a href="#" class="close" data-dismiss="modal">&times;</a><h3>Pick a Time</h3></div><div class="modal-content">'+o+'</div><div class="modal-footer"><a href="#" class="btn btn-primary" data-dismiss="modal">OK</a></div></div>';break;case"dropdown":t='<div class="bootstrap-timepicker-widget dropdown-menu">'+o+"</div>"}return t},getTime:function(){return""===this.hour?"":this.hour+":"+(1===this.minute.toString().length?"0"+this.minute:this.minute)+(this.showSeconds?":"+(1===this.second.toString().length?"0"+this.second:this.second):"")+(this.showMeridian?" "+this.meridian:"")},hideWidget:function(){!1!==this.isOpen&&(this.$element.trigger({type:"hide.timepicker",time:{value:this.getTime(),hours:this.hour,minutes:this.minute,seconds:this.second,meridian:this.meridian}}),"modal"===this.template&&this.$widget.modal?this.$widget.modal("hide"):this.$widget.removeClass("open"),t(i).off("mousedown.timepicker, touchend.timepicker",this.handleDocumentClick),this.isOpen=!1,this.$widget.detach())},highlightUnit:function(){this.position=this.getCursorPosition(),this.position>=0&&this.position<=2?this.highlightHour():this.position>=3&&this.position<=5?this.highlightMinute():this.position>=6&&this.position<=8?this.showSeconds?this.highlightSecond():this.highlightMeridian():this.position>=9&&this.position<=11&&this.highlightMeridian()},highlightNextUnit:function(){switch(this.highlightedUnit){case"hour":this.highlightMinute();break;case"minute":this.showSeconds?this.highlightSecond():this.showMeridian?this.highlightMeridian():this.highlightHour();break;case"second":this.showMeridian?this.highlightMeridian():this.highlightHour();break;case"meridian":this.highlightHour()}},highlightPrevUnit:function(){switch(this.highlightedUnit){case"hour":this.showMeridian?this.highlightMeridian():this.showSeconds?this.highlightSecond():this.highlightMinute();break;case"minute":this.highlightHour();break;case"second":this.highlightMinute();break;case"meridian":this.showSeconds?this.highlightSecond():this.highlightMinute()}},highlightHour:function(){var t=this.$element.get(0),e=this;this.highlightedUnit="hour",t.setSelectionRange&&setTimeout(function(){e.hour<10?t.setSelectionRange(0,1):t.setSelectionRange(0,2)},0)},highlightMinute:function(){var t=this.$element.get(0),e=this;this.highlightedUnit="minute",t.setSelectionRange&&setTimeout(function(){e.hour<10?t.setSelectionRange(2,4):t.setSelectionRange(3,5)},0)},highlightSecond:function(){var t=this.$element.get(0),e=this;this.highlightedUnit="second",t.setSelectionRange&&setTimeout(function(){e.hour<10?t.setSelectionRange(5,7):t.setSelectionRange(6,8)},0)},highlightMeridian:function(){var t=this.$element.get(0),e=this;this.highlightedUnit="meridian",t.setSelectionRange&&(this.showSeconds?setTimeout(function(){e.hour<10?t.setSelectionRange(8,10):t.setSelectionRange(9,11)},0):setTimeout(function(){e.hour<10?t.setSelectionRange(5,7):t.setSelectionRange(6,8)},0))},incrementHour:function(){if(this.showMeridian){if(11===this.hour)return this.hour++,this.toggleMeridian();12===this.hour&&(this.hour=0)}return this.hour===this.maxHours-1?void(this.hour=0):void this.hour++},incrementMinute:function(t){var e;(e=t?this.minute+t:this.minute+this.minuteStep-this.minute%this.minuteStep)>59?(this.incrementHour(),this.minute=e-60):this.minute=e},incrementSecond:function(){var t=this.second+this.secondStep-this.second%this.secondStep;t>59?(this.incrementMinute(!0),this.second=t-60):this.second=t},mousewheel:function(e){if(!this.disableMousewheel){e.preventDefault(),e.stopPropagation();var i=e.originalEvent.wheelDelta||-e.originalEvent.detail,n=null;switch("mousewheel"===e.type?n=-1*e.originalEvent.wheelDelta:"DOMMouseScroll"===e.type&&(n=40*e.originalEvent.detail),n&&(e.preventDefault(),t(this).scrollTop(n+t(this).scrollTop())),this.highlightedUnit){case"minute":i>0?this.incrementMinute():this.decrementMinute(),this.highlightMinute();break;case"second":i>0?this.incrementSecond():this.decrementSecond(),this.highlightSecond();break;case"meridian":this.toggleMeridian(),this.highlightMeridian();break;default:i>0?this.incrementHour():this.decrementHour(),this.highlightHour()}return!1}},changeToNearestStep:function(t,e){return t%e==0?t:Math.round(t%e/e)?(t+(e-t%e))%60:t-t%e},place:function(){if(!this.isInline){var i=this.$widget.outerWidth(),n=this.$widget.outerHeight(),s=t(e).width(),o=t(e).height(),r=t(e).scrollTop(),a=parseInt(this.$element.parents().filter(function(){return"auto"!==t(this).css("z-index")}).first().css("z-index"),10)+10,l=this.component?this.component.parent().offset():this.$element.offset(),c=this.component?this.component.outerHeight(!0):this.$element.outerHeight(!1),h=this.component?this.component.outerWidth(!0):this.$element.outerWidth(!1),u=l.left,d=l.top;this.$widget.removeClass("timepicker-orient-top timepicker-orient-bottom timepicker-orient-right timepicker-orient-left"),"auto"!==this.orientation.x?(this.$widget.addClass("timepicker-orient-"+this.orientation.x),"right"===this.orientation.x&&(u-=i-h)):(this.$widget.addClass("timepicker-orient-left"),l.left<0?u-=l.left-10:l.left+i>s&&(u=s-i-10));var p,f,m=this.orientation.y;"auto"===m&&(p=-r+l.top-n,f=r+o-(l.top+c+n),m=Math.max(p,f)===f?"top":"bottom"),this.$widget.addClass("timepicker-orient-"+m),"top"===m?d+=c:d-=n+parseInt(this.$widget.css("padding-top"),10),this.$widget.css({top:d,left:u,zIndex:a})}},remove:function(){t("document").off(".timepicker"),this.$widget&&this.$widget.remove(),delete this.$element.data().timepicker},setDefaultTime:function(t){if(this.$element.val())this.updateFromElementVal();else if("current"===t){var e=new Date,i=e.getHours(),n=e.getMinutes(),s=e.getSeconds(),o="AM";0!==s&&(60===(s=Math.ceil(e.getSeconds()/this.secondStep)*this.secondStep)&&(n+=1,s=0)),0!==n&&(60===(n=Math.ceil(e.getMinutes()/this.minuteStep)*this.minuteStep)&&(i+=1,n=0)),this.showMeridian&&(0===i?i=12:i>=12?(i>12&&(i-=12),o="PM"):o="AM"),this.hour=i,this.minute=n,this.second=s,this.meridian=o,this.update()}else!1===t?(this.hour=0,this.minute=0,this.second=0,this.meridian="AM"):this.setTime(t)},setTime:function(t,e){if(t){var i,n,s,o,r,a;if("object"==typeof t&&t.getMonth)s=t.getHours(),o=t.getMinutes(),r=t.getSeconds(),this.showMeridian&&(a="AM",s>12&&(a="PM",s%=12),12===s&&(a="PM"));else{if((i=(/a/i.test(t)?1:0)+(/p/i.test(t)?2:0))>2)return void this.clear();if(s=(n=t.replace(/[^0-9\:]/g,"").split(":"))[0]?n[0].toString():n.toString(),this.explicitMode&&s.length>2&&s.length%2!=0)return void this.clear();o=n[1]?n[1].toString():"",r=n[2]?n[2].toString():"",s.length>4&&(r=s.slice(-2),s=s.slice(0,-2)),s.length>2&&(o=s.slice(-2),s=s.slice(0,-2)),o.length>2&&(r=o.slice(-2),o=o.slice(0,-2)),s=parseInt(s,10),o=parseInt(o,10),r=parseInt(r,10),isNaN(s)&&(s=0),isNaN(o)&&(o=0),isNaN(r)&&(r=0),r>59&&(r=59),o>59&&(o=59),s>=this.maxHours&&(s=this.maxHours-1),this.showMeridian?(s>12&&(i=2,s-=12),i||(i=1),0===s&&(s=12),a=1===i?"AM":"PM"):12>s&&2===i?s+=12:s>=this.maxHours?s=this.maxHours-1:(0>s||12===s&&1===i)&&(s=0)}this.hour=s,this.snapToStep?(this.minute=this.changeToNearestStep(o,this.minuteStep),this.second=this.changeToNearestStep(r,this.secondStep)):(this.minute=o,this.second=r),this.meridian=a,this.update(e)}else this.clear()},showWidget:function(){this.isOpen||this.$element.is(":disabled")||(this.$widget.appendTo(this.appendWidgetTo),t(i).on("mousedown.timepicker, touchend.timepicker",{scope:this},this.handleDocumentClick),this.$element.trigger({type:"show.timepicker",time:{value:this.getTime(),hours:this.hour,minutes:this.minute,seconds:this.second,meridian:this.meridian}}),this.place(),this.disableFocus&&this.$element.blur(),""===this.hour&&(this.defaultTime?this.setDefaultTime(this.defaultTime):this.setTime("0:0:0")),"modal"===this.template&&this.$widget.modal?this.$widget.modal("show").on("hidden",t.proxy(this.hideWidget,this)):!1===this.isOpen&&this.$widget.addClass("open"),this.isOpen=!0)},toggleMeridian:function(){this.meridian="AM"===this.meridian?"PM":"AM"},update:function(t){this.updateElement(),t||this.updateWidget(),this.$element.trigger({type:"changeTime.timepicker",time:{value:this.getTime(),hours:this.hour,minutes:this.minute,seconds:this.second,meridian:this.meridian}})},updateElement:function(){this.$element.val(this.getTime()).change()},updateFromElementVal:function(){this.setTime(this.$element.val())},updateWidget:function(){if(!1!==this.$widget){var t=this.hour,e=1===this.minute.toString().length?"0"+this.minute:this.minute,i=1===this.second.toString().length?"0"+this.second:this.second;this.showInputs?(this.$widget.find("input.bootstrap-timepicker-hour").val(t),this.$widget.find("input.bootstrap-timepicker-minute").val(e),this.showSeconds&&this.$widget.find("input.bootstrap-timepicker-second").val(i),this.showMeridian&&this.$widget.find("input.bootstrap-timepicker-meridian").val(this.meridian)):(this.$widget.find("span.bootstrap-timepicker-hour").text(t),this.$widget.find("span.bootstrap-timepicker-minute").text(e),this.showSeconds&&this.$widget.find("span.bootstrap-timepicker-second").text(i),this.showMeridian&&this.$widget.find("span.bootstrap-timepicker-meridian").text(this.meridian))}},updateFromWidgetInputs:function(){if(!1!==this.$widget){var t=this.$widget.find("input.bootstrap-timepicker-hour").val()+":"+this.$widget.find("input.bootstrap-timepicker-minute").val()+(this.showSeconds?":"+this.$widget.find("input.bootstrap-timepicker-second").val():"")+(this.showMeridian?this.$widget.find("input.bootstrap-timepicker-meridian").val():"");this.setTime(t,!0)}},widgetClick:function(e){e.stopPropagation(),e.preventDefault();var i=t(e.target),n=i.closest("a").data("action");n&&this[n](),this.update(),i.is("input")&&i.get(0).setSelectionRange(0,2)},widgetKeydown:function(e){var i=t(e.target),n=i.attr("class").replace("bootstrap-timepicker-","");switch(e.which){case 9:if(e.shiftKey){if("hour"===n)return this.hideWidget()}else if(this.showMeridian&&"meridian"===n||this.showSeconds&&"second"===n||!this.showMeridian&&!this.showSeconds&&"minute"===n)return this.hideWidget();break;case 27:this.hideWidget();break;case 38:switch(e.preventDefault(),n){case"hour":this.incrementHour();break;case"minute":this.incrementMinute();break;case"second":this.incrementSecond();break;case"meridian":this.toggleMeridian()}this.setTime(this.getTime()),i.get(0).setSelectionRange(0,2);break;case 40:switch(e.preventDefault(),n){case"hour":this.decrementHour();break;case"minute":this.decrementMinute();break;case"second":this.decrementSecond();break;case"meridian":this.toggleMeridian()}this.setTime(this.getTime()),i.get(0).setSelectionRange(0,2)}},widgetKeyup:function(t){(65===t.which||77===t.which||80===t.which||46===t.which||8===t.which||t.which>=48&&t.which<=57||t.which>=96&&t.which<=105)&&this.updateFromWidgetInputs()}},t.fn.timepicker=function(e){var i=Array.apply(null,arguments);return i.shift(),this.each(function(){var s=t(this),o=s.data("timepicker"),r="object"==typeof e&&e;o||s.data("timepicker",o=new n(this,t.extend({},t.fn.timepicker.defaults,r,t(this).data()))),"string"==typeof e&&o[e].apply(o,i)})},t.fn.timepicker.defaults={defaultTime:"current",disableFocus:!1,disableMousewheel:!1,isOpen:!1,minuteStep:15,modalBackdrop:!1,orientation:{x:"auto",y:"auto"},secondStep:15,snapToStep:!1,showSeconds:!1,showInputs:!0,showMeridian:!0,template:"dropdown",appendWidgetTo:"body",showWidgetOnAddonClick:!0,icons:{up:"glyphicon glyphicon-chevron-up",down:"glyphicon glyphicon-chevron-down"},maxHours:24,explicitMode:!1},t.fn.timepicker.Constructor=n,t(i).on("focus.timepicker.data-api click.timepicker.data-api",'[data-provide="timepicker"]',function(e){var i=t(this);i.data("timepicker")||(e.preventDefault(),i.timepicker())})}(jQuery,window,document),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"===("undefined"==typeof module?"undefined":_typeof(module))&&module.exports?module.exports=function(e,i){return void 0===i&&(i="undefined"!=typeof window?require("jquery"):require("jquery")(e)),t(i),i}:t(jQuery)}(function(t){var e=0;t.fn.TouchSpin=function(i){var n={min:0,max:100,initval:"",replacementval:"",firstclickvalueifempty:null,step:1,decimals:0,stepinterval:100,forcestepdivisibility:"round",stepintervaldelay:500,verticalbuttons:!1,verticalup:"&plus;",verticaldown:"&minus;",verticalupclass:"",verticaldownclass:"",prefix:"",postfix:"",prefix_extraclass:"",postfix_extraclass:"",booster:!0,boostat:10,maxboostedstep:!1,mousewheel:!0,buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary",buttondown_txt:"&minus;",buttonup_txt:"&plus;",callback_before_calculation:function(t){return t},callback_after_calculation:function(t){return t}},s={min:"min",max:"max",initval:"init-val",replacementval:"replacement-val",firstclickvalueifempty:"first-click-value-if-empty",step:"step",decimals:"decimals",stepinterval:"step-interval",verticalbuttons:"vertical-buttons",verticalupclass:"vertical-up-class",verticaldownclass:"vertical-down-class",forcestepdivisibility:"force-step-divisibility",stepintervaldelay:"step-interval-delay",prefix:"prefix",postfix:"postfix",prefix_extraclass:"prefix-extra-class",postfix_extraclass:"postfix-extra-class",booster:"booster",boostat:"boostat",maxboostedstep:"max-boosted-step",mousewheel:"mouse-wheel",buttondown_class:"button-down-class",buttonup_class:"button-up-class",buttondown_txt:"button-down-txt",buttonup_txt:"button-up-txt"};return this.each(function(){var o,r,a,l,c,h,u,d,p,f,m,g,v,y,b,_,w,x=t(this),k=x.data(),D=0,C=!1;function S(){""===o.prefix&&(r=l.prefix.detach()),""===o.postfix&&(a=l.postfix.detach())}function T(){var t,e,i=o.callback_before_calculation(x.val());""===i?""!==o.replacementval&&(x.val(o.replacementval),x.trigger("change")):0<o.decimals&&"."===i||(t=parseFloat(i),(e=t=isNaN(t)?""!==o.replacementval?o.replacementval:0:t).toString()!==i&&(e=t),e=function(t){switch(o.forcestepdivisibility){case"round":return(Math.round(t/o.step)*o.step).toFixed(o.decimals);case"floor":return(Math.floor(t/o.step)*o.step).toFixed(o.decimals);case"ceil":return(Math.ceil(t/o.step)*o.step).toFixed(o.decimals);default:return t.toFixed(o.decimals)}}(t),null!==o.min&&t<o.min&&(e=o.min),null!==o.max&&t>o.max&&(e=o.max),parseFloat(t).toString()!==parseFloat(e).toString()&&x.val(e),x.val(o.callback_after_calculation(parseFloat(e).toFixed(o.decimals))))}function M(){var t;return o.booster?(t=Math.pow(2,Math.floor(D/o.boostat))*o.step,o.maxboostedstep&&t>o.maxboostedstep&&(t=o.maxboostedstep,h=Math.round(h/t)*t),Math.max(o.step,t)):o.step}function E(){return"number"==typeof o.firstclickvalueifempty?o.firstclickvalueifempty:(o.min+o.max)/2}function A(){var t=x.is(":disabled,[readonly]");l.up.prop("disabled",t),l.down.prop("disabled",t),t&&j()}function O(){var t,e;x.is(":disabled,[readonly]")||(T(),t=h=parseFloat(o.callback_before_calculation(l.input.val())),isNaN(h)?h=E():(e=M(),h+=e),null!==o.max&&h>=o.max&&(h=o.max,x.trigger("touchspin.on.max"),j()),l.input.val(o.callback_after_calculation(parseFloat(h).toFixed(o.decimals))),t!==h&&x.trigger("change"))}function N(){var t,e;x.is(":disabled,[readonly]")||(T(),t=h=parseFloat(o.callback_before_calculation(l.input.val())),isNaN(h)?h=E():(e=M(),h-=e),null!==o.min&&h<=o.min&&(h=o.min,x.trigger("touchspin.on.min"),j()),l.input.val(o.callback_after_calculation(parseFloat(h).toFixed(o.decimals))),t!==h&&x.trigger("change"))}function L(){x.is(":disabled,[readonly]")||(j(),D=0,C="down",x.trigger("touchspin.on.startspin"),x.trigger("touchspin.on.startdownspin"),p=setTimeout(function(){u=setInterval(function(){D++,N()},o.stepinterval)},o.stepintervaldelay))}function P(){x.is(":disabled,[readonly]")||(j(),D=0,C="up",x.trigger("touchspin.on.startspin"),x.trigger("touchspin.on.startupspin"),f=setTimeout(function(){d=setInterval(function(){D++,O()},o.stepinterval)},o.stepintervaldelay))}function j(){switch(clearTimeout(p),clearTimeout(f),clearInterval(u),clearInterval(d),C){case"up":x.trigger("touchspin.on.stopupspin"),x.trigger("touchspin.on.stopspin");break;case"down":x.trigger("touchspin.on.stopdownspin"),x.trigger("touchspin.on.stopspin")}D=0,C=!1}x.data("alreadyinitialized")||(x.data("alreadyinitialized",!0),e+=1,x.data("spinnerid",e),x.is("input")?(o=t.extend({},n,k,function(){var e={};return t.each(s,function(t,i){i="bts-"+i,x.is("[data-"+i+"]")&&(e[t]=x.data(i))}),t.each(["min","max","step"],function(t,i){x.is("["+i+"]")&&(void 0!==e[i]&&console.warn('Both the "data-bts-'+i+'" data attribute and the "'+i+'" individual attribute were specified, the individual attribute will take precedence on: ',x),e[i]=x.attr(i))}),e}(),i),1!==parseFloat(o.step)&&(0!=(m=o.max%o.step)&&(o.max=parseFloat(o.max)-m),0!=(m=o.min%o.step))&&(o.min=parseFloat(o.min)+(parseFloat(o.step)-m)),""!==o.initval&&""===x.val()&&x.val(o.initval),T(),m=x.val(),k=x.parent(),""!==m&&(m=o.callback_before_calculation(m),m=o.callback_after_calculation(parseFloat(m).toFixed(o.decimals))),x.data("initvalue",m).val(m),x.addClass("form-control"),c='\n          <span class="input-group-addon bootstrap-touchspin-vertical-button-wrapper">\n            <span class="input-group-btn-vertical">\n              <button tabindex="-1" class="'.concat(o.buttondown_class," bootstrap-touchspin-up ").concat(o.verticalupclass,'" type="button">').concat(o.verticalup,'</button>\n              <button tabindex="-1" class="').concat(o.buttonup_class," bootstrap-touchspin-down ").concat(o.verticaldownclass,'" type="button">').concat(o.verticaldown,"</button>\n            </span>\n          </span>\n       "),k.hasClass("input-group")?((k=k).addClass("bootstrap-touchspin"),w=x.prev(),y=x.next(),b='\n            <span class="input-group-addon input-group-prepend bootstrap-touchspin-prefix input-group-prepend bootstrap-touchspin-injected">\n              <span class="input-group-text">'.concat(o.prefix,"</span>\n            </span>\n          "),_='\n            <span class="input-group-addon input-group-append bootstrap-touchspin-postfix input-group-append bootstrap-touchspin-injected">\n              <span class="input-group-text">'.concat(o.postfix,"</span>\n            </span>\n          "),o.verticalbuttons?t(c).insertAfter(x):(w.hasClass("input-group-btn")||w.hasClass("input-group-prepend")?(g='\n              <button tabindex="-1" class="'.concat(o.buttondown_class,' bootstrap-touchspin-down bootstrap-touchspin-injected" type="button">').concat(o.buttondown_txt,"</button>\n            "),w.append(g)):(g='\n              <span class="input-group-btn input-group-prepend bootstrap-touchspin-injected">\n                <button tabindex="-1" class="'.concat(o.buttondown_class,' bootstrap-touchspin-down" type="button">').concat(o.buttondown_txt,"</button>\n              </span>\n            "),t(g).insertBefore(x)),y.hasClass("input-group-btn")||y.hasClass("input-group-append")?(v='\n            <button tabindex="-1" class="'.concat(o.buttonup_class,' bootstrap-touchspin-up bootstrap-touchspin-injected" type="button">').concat(o.buttonup_txt,"</button>\n          "),y.prepend(v)):(v='\n            <span class="input-group-btn input-group-append bootstrap-touchspin-injected">\n              <button tabindex="-1" class="'.concat(o.buttonup_class,' bootstrap-touchspin-up" type="button">').concat(o.buttonup_txt,"</button>\n            </span>\n          "),t(v).insertAfter(x))),t(b).insertBefore(x),t(_).insertAfter(x),g=k):(w="",x.hasClass("input-sm")||x.hasClass("form-control-sm")?w="input-group-sm":(x.hasClass("input-lg")||x.hasClass("form-control-lg"))&&(w="input-group-lg"),w=o.verticalbuttons?'\n            <div class="input-group '.concat(w,' bootstrap-touchspin bootstrap-touchspin-injected">\n              <span class="input-group-addon input-group-prepend bootstrap-touchspin-prefix">\n                <span class="input-group-text">').concat(o.prefix,'</span>\n              </span>\n              <span class="input-group-addon bootstrap-touchspin-postfix input-group-append">\n                <span class="input-group-text">').concat(o.postfix,"</span>\n              </span>\n              ").concat(c,"\n            </div>\n          "):'\n            <div class="input-group bootstrap-touchspin bootstrap-touchspin-injected">\n              <span class="input-group-btn input-group-prepend">\n                <button tabindex="-1" class="'.concat(o.buttondown_class,' bootstrap-touchspin-down" type="button">').concat(o.buttondown_txt,'</button>\n              </span>\n              <span class="input-group-addon bootstrap-touchspin-prefix input-group-prepend">\n                <span class="input-group-text">').concat(o.prefix,'</span>\n              </span>\n              <span class="input-group-addon bootstrap-touchspin-postfix input-group-append">\n                <span class="input-group-text">').concat(o.postfix,'</span>\n              </span>\n              <span class="input-group-btn input-group-append">\n                <button tabindex="-1" class="').concat(o.buttonup_class,' bootstrap-touchspin-up" type="button">').concat(o.buttonup_txt,"</button>\n              </span>\n            </div>"),g=t(w).insertBefore(x),t(".bootstrap-touchspin-prefix",g).after(x),x.hasClass("input-sm")||x.hasClass("form-control-sm")?g.addClass("input-group-sm"):(x.hasClass("input-lg")||x.hasClass("form-control-lg"))&&g.addClass("input-group-lg")),l={down:t(".bootstrap-touchspin-down",g),up:t(".bootstrap-touchspin-up",g),input:t("input",g),prefix:t(".bootstrap-touchspin-prefix",g).addClass(o.prefix_extraclass),postfix:t(".bootstrap-touchspin-postfix",g).addClass(o.postfix_extraclass)},A(),S(),"undefined"!=typeof MutationObserver&&new MutationObserver(function(t){t.forEach(function(t){"attributes"!==t.type||"disabled"!==t.attributeName&&"readonly"!==t.attributeName||A()})}).observe(x[0],{attributes:!0}),x.on("keydown.touchspin",function(t){var e=t.keyCode||t.which;38===e?("up"!==C&&(O(),P()),t.preventDefault()):40===e?("down"!==C&&(N(),L()),t.preventDefault()):9!==e&&13!==e||T()}),x.on("keyup.touchspin",function(t){38!==(t=t.keyCode||t.which)&&40!==t||j()}),t(document).on("mousedown touchstart",function(e){t(e.target).is(x)||T()}),x.on("blur.touchspin",function(){T()}),l.down.on("keydown",function(t){var e=t.keyCode||t.which;32!==e&&13!==e||("down"!==C&&(N(),L()),t.preventDefault())}),l.down.on("keyup.touchspin",function(t){32!==(t=t.keyCode||t.which)&&13!==t||j()}),l.up.on("keydown.touchspin",function(t){var e=t.keyCode||t.which;32!==e&&13!==e||("up"!==C&&(O(),P()),t.preventDefault())}),l.up.on("keyup.touchspin",function(t){32!==(t=t.keyCode||t.which)&&13!==t||j()}),l.down.on("mousedown.touchspin",function(t){l.down.off("touchstart.touchspin"),x.is(":disabled,[readonly]")||(N(),L(),t.preventDefault(),t.stopPropagation())}),l.down.on("touchstart.touchspin",function(t){l.down.off("mousedown.touchspin"),x.is(":disabled,[readonly]")||(N(),L(),t.preventDefault(),t.stopPropagation())}),l.up.on("mousedown.touchspin",function(t){l.up.off("touchstart.touchspin"),x.is(":disabled,[readonly]")||(O(),P(),t.preventDefault(),t.stopPropagation())}),l.up.on("touchstart.touchspin",function(t){l.up.off("mousedown.touchspin"),x.is(":disabled,[readonly]")||(O(),P(),t.preventDefault(),t.stopPropagation())}),l.up.on("mouseup.touchspin mouseout.touchspin touchleave.touchspin touchend.touchspin touchcancel.touchspin",function(t){C&&(t.stopPropagation(),j())}),l.down.on("mouseup.touchspin mouseout.touchspin touchleave.touchspin touchend.touchspin touchcancel.touchspin",function(t){C&&(t.stopPropagation(),j())}),l.down.on("mousemove.touchspin touchmove.touchspin",function(t){C&&(t.stopPropagation(),t.preventDefault())}),l.up.on("mousemove.touchspin touchmove.touchspin",function(t){C&&(t.stopPropagation(),t.preventDefault())}),x.on("mousewheel.touchspin DOMMouseScroll.touchspin",function(t){var e;o.mousewheel&&x.is(":focus")&&(e=t.originalEvent.wheelDelta||-t.originalEvent.deltaY||-t.originalEvent.detail,t.stopPropagation(),t.preventDefault(),(e<0?N:O)())}),x.on("touchspin.destroy",function(){var e=x.parent();j(),x.off(".touchspin"),e.hasClass("bootstrap-touchspin-injected")?(x.siblings().remove(),x.unwrap()):(t(".bootstrap-touchspin-injected",e).remove(),e.removeClass("bootstrap-touchspin")),x.data("alreadyinitialized",!1)}),x.on("touchspin.uponce",function(){j(),O()}),x.on("touchspin.downonce",function(){j(),N()}),x.on("touchspin.startupspin",function(){P()}),x.on("touchspin.startdownspin",function(){L()}),x.on("touchspin.stopspin",function(){j()}),x.on("touchspin.updatesettings",function(e,i){var n=i;o=t.extend({},o,n),n.postfix&&(0===x.parent().find(".bootstrap-touchspin-postfix").length&&a.insertAfter(x),x.parent().find(".bootstrap-touchspin-postfix .input-group-text").text(n.postfix)),n.prefix&&(0===x.parent().find(".bootstrap-touchspin-prefix").length&&r.insertBefore(x),x.parent().find(".bootstrap-touchspin-prefix .input-group-text").text(n.prefix)),S(),T(),""!==(i=l.input.val())&&(i=parseFloat(o.callback_before_calculation(l.input.val())),l.input.val(o.callback_after_calculation(parseFloat(i).toFixed(o.decimals))))})):console.log("Must be an input."))})}}),function(t){"use strict";t.event.special.destroyed||(t.event.special.destroyed={remove:function(t){t.handler&&t.handler()}}),t.fn.extend({maxlength:function(e,i){var n=t("body");function s(t){var e=t.charCodeAt();return e?e<128?1:e<2048?2:3:0}function o(t){return t.split("").map(s).concat(0).reduce(function(t,e){return t+e})}function r(t){var i=t.val();i=e.twoCharLinebreak?i.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"):i.replace(/(?:\r\n|\r|\n)/g,"\n");var n=0;return n=e.utf8?o(i):i.length,"file"===t.prop("type")&&""!==t.val()&&(n-=12),n}function a(t,e){return e-r(t)}function l(t,e){e.css({display:"block"}),t.trigger("maxlength.shown")}function c(t,i,n){var s="";return e.message?s="function"==typeof e.message?e.message(t,i):e.message.replace("%charsTyped%",n).replace("%charsRemaining%",i-n).replace("%charsTotal%",i):(e.preText&&(s+=e.preText),e.showCharsTyped?s+=n:s+=i-n,e.showMaxLength&&(s+=e.separator+i),e.postText&&(s+=e.postText)),s}function h(t,i,n,s){var o,a,h,u;s&&(s.html(c(i.val(),n,n-t)),t>0?(o=i,a=e.threshold,h=n,u=!0,!e.alwaysShow&&h-r(o)>a&&(u=!1),u?l(i,s.removeClass(e.limitReachedClass+" "+e.limitExceededClass).addClass(e.warningClass)):function(t,i){e.alwaysShow||(i.css({display:"none"}),t.trigger("maxlength.hidden"))}(i,s)):e.limitExceededClass?l(i,0===t?s.removeClass(e.warningClass+" "+e.limitExceededClass).addClass(e.limitReachedClass):s.removeClass(e.warningClass+" "+e.limitReachedClass).addClass(e.limitExceededClass)):l(i,s.removeClass(e.warningClass).addClass(e.limitReachedClass))),e.customMaxAttribute&&(t<0?i.addClass(e.customMaxClass):i.removeClass(e.customMaxClass))}function u(i,n){var s=function(e){var i=e[0];return t.extend({},"function"==typeof i.getBoundingClientRect?i.getBoundingClientRect():{width:i.offsetWidth,height:i.offsetHeight},e.offset())}(i);if("function"!==t.type(e.placement))if(t.isPlainObject(e.placement))!function(i,n){if(i&&n){var s={};t.each(["top","bottom","left","right","position"],function(t,i){var n=e.placement[i];void 0!==n&&(s[i]=n)}),n.css(s)}}(e.placement,n);else{var o=i.outerWidth(),r=n.outerWidth(),a=n.width(),l=n.height();switch(e.appendToParent&&(s.top-=i.parent().offset().top,s.left-=i.parent().offset().left),e.placement){case"bottom":n.css({top:s.top+s.height,left:s.left+s.width/2-a/2});break;case"top":n.css({top:s.top-l,left:s.left+s.width/2-a/2});break;case"left":n.css({top:s.top+s.height/2-l/2,left:s.left-a});break;case"right":n.css({top:s.top+s.height/2-l/2,left:s.left+s.width});break;case"bottom-right":n.css({top:s.top+s.height,left:s.left+s.width});break;case"top-right":n.css({top:s.top-l,left:s.left+o});break;case"top-left":n.css({top:s.top-l,left:s.left-r});break;case"bottom-left":n.css({top:s.top+i.outerHeight(),left:s.left-r});break;case"centered-right":n.css({top:s.top+l/2,left:s.left+o-r-3});break;case"bottom-right-inside":n.css({top:s.top+s.height,left:s.left+s.width-r});break;case"top-right-inside":n.css({top:s.top-l,left:s.left+o-r});break;case"top-left-inside":n.css({top:s.top-l,left:s.left});break;case"bottom-left-inside":n.css({top:s.top+i.outerHeight(),left:s.left})}}else e.placement(i,n,s)}function d(t){var i=t.attr("maxlength")||e.customMaxAttribute;if(e.customMaxAttribute&&!e.allowOverMax){var n=t.attr(e.customMaxAttribute);(!i||n<i)&&(i=n)}return i||(i=t.attr("size")),i}return t.isFunction(e)&&!i&&(i=e,e={}),e=t.extend({showOnReady:!1,alwaysShow:!0,threshold:0,warningClass:"small form-text text-muted",limitReachedClass:"small form-text text-danger",limitExceededClass:"",separator:" / ",preText:"",postText:"",showMaxLength:!0,placement:"bottom-right-inside",message:null,showCharsTyped:!0,validate:!1,utf8:!1,appendToParent:!1,twoCharLinebreak:!0,customMaxAttribute:null,customMaxClass:"overmax",allowOverMax:!1,zIndex:1099},e),this.each(function(){var i,r,l=t(this);function p(){var s=c(l.val(),i,"0");i=d(l),r||(r=t('<span class="bootstrap-maxlength"></span>').css({display:"none",position:"absolute",whiteSpace:"nowrap",zIndex:e.zIndex}).html(s)),l.is("textarea")&&(l.data("maxlenghtsizex",l.outerWidth()),l.data("maxlenghtsizey",l.outerHeight()),l.mouseup(function(){l.outerWidth()===l.data("maxlenghtsizex")&&l.outerHeight()===l.data("maxlenghtsizey")||u(l,r),l.data("maxlenghtsizex",l.outerWidth()),l.data("maxlenghtsizey",l.outerHeight())})),e.appendToParent?(l.parent().append(r),l.parent().css("position","relative")):n.append(r),h(a(l,d(l)),l,i,r),u(l,r)}t(window).resize(function(){r&&u(l,r)}),e.showOnReady?l.ready(function(){p()}):l.focus(function(){p()}),l.on("maxlength.reposition",function(){u(l,r)}),l.on("destroyed",function(){r&&r.remove()}),l.on("blur",function(){r&&!e.showOnReady&&r.remove()}),l.on("input",function(){var t=d(l),n=a(l,t),c=!0;return e.validate&&n<0?(function(t,i){var n=t.val();if(e.twoCharLinebreak&&"\n"===(n=n.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"))[n.length-1]&&(i-=n.length%2),e.utf8){for(var r=n.split("").map(s),a=0,l=o(n)-i;a<l;a+=r.pop());i-=i-r.length}t.val(n.substr(0,i))}(l,t),c=!1):h(n,l,i,r),c})})}})}(jQuery),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t("object"==typeof exports?require("jquery"):jQuery)}(function(t,e){function i(){return new Date(Date.UTC.apply(Date,arguments))}function n(){var t=new Date;return i(t.getFullYear(),t.getMonth(),t.getDate())}function s(t,e){return t.getUTCFullYear()===e.getUTCFullYear()&&t.getUTCMonth()===e.getUTCMonth()&&t.getUTCDate()===e.getUTCDate()}function o(i,n){return function(){return n!==e&&t.fn.datepicker.deprecated(n),this[i].apply(this,arguments)}}function r(e){var i={};if(f[e]||(e=e.split("-")[0],f[e])){var n=f[e];return t.each(p,function(t,e){e in n&&(i[e]=n[e])}),i}}var a=function(){var e={get:function(t){return this.slice(t)[0]},contains:function(t){for(var e=t&&t.valueOf(),i=0,n=this.length;i<n;i++)if(0<=this[i].valueOf()-e&&this[i].valueOf()-e<864e5)return i;return-1},remove:function(t){this.splice(t,1)},replace:function(t){t&&(Array.isArray(t)||(t=[t]),this.clear(),this.push.apply(this,t))},clear:function(){this.length=0},copy:function(){var t=new a;return t.replace(this),t}};return function(){var i=[];return i.push.apply(i,arguments),t.extend(i,e),i}}(),l=function(e,i){t.data(e,"datepicker",this),this._events=[],this._secondaryEvents=[],this._process_options(i),this.dates=new a,this.viewDate=this.o.defaultViewDate,this.focusDate=null,this.element=t(e),this.isInput=this.element.is("input"),this.inputField=this.isInput?this.element:this.element.find("input"),this.component=!!this.element.hasClass("date")&&this.element.find(".add-on, .input-group-addon, .input-group-append, .input-group-prepend, .btn"),this.component&&0===this.component.length&&(this.component=!1),null===this.o.isInline?this.isInline=!this.component&&!this.isInput:this.isInline=this.o.isInline,this.picker=t(m.template),this._check_template(this.o.templates.leftArrow)&&this.picker.find(".prev").html(this.o.templates.leftArrow),this._check_template(this.o.templates.rightArrow)&&this.picker.find(".next").html(this.o.templates.rightArrow),this._buildEvents(),this._attachEvents(),this.isInline?this.picker.addClass("datepicker-inline").appendTo(this.element):this.picker.addClass("datepicker-dropdown dropdown-menu"),this.o.rtl&&this.picker.addClass("datepicker-rtl"),this.o.calendarWeeks&&this.picker.find(".datepicker-days .datepicker-switch, thead .datepicker-title, tfoot .today, tfoot .clear").attr("colspan",function(t,e){return Number(e)+1}),this._process_options({startDate:this._o.startDate,endDate:this._o.endDate,daysOfWeekDisabled:this.o.daysOfWeekDisabled,daysOfWeekHighlighted:this.o.daysOfWeekHighlighted,datesDisabled:this.o.datesDisabled}),this._allow_update=!1,this.setViewMode(this.o.startView),this._allow_update=!0,this.fillDow(),this.fillMonths(),this.update(),this.isInline&&this.show()};l.prototype={constructor:l,_resolveViewName:function(e){return t.each(m.viewModes,function(i,n){if(e===i||-1!==t.inArray(e,n.names))return e=i,!1}),e},_resolveDaysOfWeek:function(e){return Array.isArray(e)||(e=e.split(/[,\s]*/)),t.map(e,Number)},_check_template:function(i){try{return i!==e&&""!==i&&((i.match(/[<>]/g)||[]).length<=0||t(i).length>0)}catch(t){return!1}},_process_options:function(e){this._o=t.extend({},this._o,e);var s=this.o=t.extend({},this._o),o=s.language;f[o]||(o=o.split("-")[0],f[o]||(o=d.language)),s.language=o,s.startView=this._resolveViewName(s.startView),s.minViewMode=this._resolveViewName(s.minViewMode),s.maxViewMode=this._resolveViewName(s.maxViewMode),s.startView=Math.max(this.o.minViewMode,Math.min(this.o.maxViewMode,s.startView)),!0!==s.multidate&&(s.multidate=Number(s.multidate)||!1,!1!==s.multidate&&(s.multidate=Math.max(0,s.multidate))),s.multidateSeparator=String(s.multidateSeparator),s.weekStart%=7,s.weekEnd=(s.weekStart+6)%7;var r=m.parseFormat(s.format);s.startDate!==-1/0&&(s.startDate?s.startDate instanceof Date?s.startDate=this._local_to_utc(this._zero_time(s.startDate)):s.startDate=m.parseDate(s.startDate,r,s.language,s.assumeNearbyYear):s.startDate=-1/0),s.endDate!==1/0&&(s.endDate?s.endDate instanceof Date?s.endDate=this._local_to_utc(this._zero_time(s.endDate)):s.endDate=m.parseDate(s.endDate,r,s.language,s.assumeNearbyYear):s.endDate=1/0),s.daysOfWeekDisabled=this._resolveDaysOfWeek(s.daysOfWeekDisabled||[]),s.daysOfWeekHighlighted=this._resolveDaysOfWeek(s.daysOfWeekHighlighted||[]),s.datesDisabled=s.datesDisabled||[],Array.isArray(s.datesDisabled)||(s.datesDisabled=s.datesDisabled.split(",")),s.datesDisabled=t.map(s.datesDisabled,function(t){return m.parseDate(t,r,s.language,s.assumeNearbyYear)});var a=String(s.orientation).toLowerCase().split(/\s+/g),l=s.orientation.toLowerCase();if(a=t.grep(a,function(t){return/^auto|left|right|top|bottom$/.test(t)}),s.orientation={x:"auto",y:"auto"},l&&"auto"!==l)if(1===a.length)switch(a[0]){case"top":case"bottom":s.orientation.y=a[0];break;case"left":case"right":s.orientation.x=a[0]}else l=t.grep(a,function(t){return/^left|right$/.test(t)}),s.orientation.x=l[0]||"auto",l=t.grep(a,function(t){return/^top|bottom$/.test(t)}),s.orientation.y=l[0]||"auto";if(s.defaultViewDate instanceof Date||"string"==typeof s.defaultViewDate)s.defaultViewDate=m.parseDate(s.defaultViewDate,r,s.language,s.assumeNearbyYear);else if(s.defaultViewDate){var c=s.defaultViewDate.year||(new Date).getFullYear(),h=s.defaultViewDate.month||0,u=s.defaultViewDate.day||1;s.defaultViewDate=i(c,h,u)}else s.defaultViewDate=n()},_applyEvents:function(t){for(var i,n,s,o=0;o<t.length;o++)i=t[o][0],2===t[o].length?(n=e,s=t[o][1]):3===t[o].length&&(n=t[o][1],s=t[o][2]),i.on(s,n)},_unapplyEvents:function(t){for(var i,n,s,o=0;o<t.length;o++)i=t[o][0],2===t[o].length?(s=e,n=t[o][1]):3===t[o].length&&(s=t[o][1],n=t[o][2]),i.off(n,s)},_buildEvents:function(){var e={keyup:t.proxy(function(e){-1===t.inArray(e.keyCode,[27,37,39,38,40,32,13,9])&&this.update()},this),keydown:t.proxy(this.keydown,this),paste:t.proxy(this.paste,this)};!0===this.o.showOnFocus&&(e.focus=t.proxy(this.show,this)),this.isInput?this._events=[[this.element,e]]:this.component&&this.inputField.length?this._events=[[this.inputField,e],[this.component,{click:t.proxy(this.show,this)}]]:this._events=[[this.element,{click:t.proxy(this.show,this),keydown:t.proxy(this.keydown,this)}]],this._events.push([this.element,"*",{blur:t.proxy(function(t){this._focused_from=t.target},this)}],[this.element,{blur:t.proxy(function(t){this._focused_from=t.target},this)}]),this.o.immediateUpdates&&this._events.push([this.element,{"changeYear changeMonth":t.proxy(function(t){this.update(t.date)},this)}]),this._secondaryEvents=[[this.picker,{click:t.proxy(this.click,this)}],[this.picker,".prev, .next",{click:t.proxy(this.navArrowsClick,this)}],[this.picker,".day:not(.disabled)",{click:t.proxy(this.dayCellClick,this)}],[t(window),{resize:t.proxy(this.place,this)}],[t(document),{"mousedown touchstart":t.proxy(function(t){this.element.is(t.target)||this.element.find(t.target).length||this.picker.is(t.target)||this.picker.find(t.target).length||this.isInline||this.hide()},this)}]]},_attachEvents:function(){this._detachEvents(),this._applyEvents(this._events)},_detachEvents:function(){this._unapplyEvents(this._events)},_attachSecondaryEvents:function(){this._detachSecondaryEvents(),this._applyEvents(this._secondaryEvents)},_detachSecondaryEvents:function(){this._unapplyEvents(this._secondaryEvents)},_trigger:function(e,i){var n=i||this.dates.get(-1),s=this._utc_to_local(n);this.element.trigger({type:e,date:s,viewMode:this.viewMode,dates:t.map(this.dates,this._utc_to_local),format:t.proxy(function(t,e){0===arguments.length?(t=this.dates.length-1,e=this.o.format):"string"==typeof t&&(e=t,t=this.dates.length-1),e=e||this.o.format;var i=this.dates.get(t);return m.formatDate(i,e,this.o.language)},this)})},show:function(){if(!(this.inputField.is(":disabled")||this.inputField.prop("readonly")&&!1===this.o.enableOnReadonly))return this.isInline||this.picker.appendTo(this.o.container),this.place(),this.picker.show(),this._attachSecondaryEvents(),this._trigger("show"),(window.navigator.msMaxTouchPoints||"ontouchstart"in document)&&this.o.disableTouchKeyboard&&t(this.element).blur(),this},hide:function(){return this.isInline||!this.picker.is(":visible")?this:(this.focusDate=null,this.picker.hide().detach(),this._detachSecondaryEvents(),this.setViewMode(this.o.startView),this.o.forceParse&&this.inputField.val()&&this.setValue(),this._trigger("hide"),this)},destroy:function(){return this.hide(),this._detachEvents(),this._detachSecondaryEvents(),this.picker.remove(),delete this.element.data().datepicker,this.isInput||delete this.element.data().date,this},paste:function(e){var i;if(e.originalEvent.clipboardData&&e.originalEvent.clipboardData.types&&-1!==t.inArray("text/plain",e.originalEvent.clipboardData.types))i=e.originalEvent.clipboardData.getData("text/plain");else{if(!window.clipboardData)return;i=window.clipboardData.getData("Text")}this.setDate(i),this.update(),e.preventDefault()},_utc_to_local:function(t){if(!t)return t;var e=new Date(t.getTime()+6e4*t.getTimezoneOffset());return e.getTimezoneOffset()!==t.getTimezoneOffset()&&(e=new Date(t.getTime()+6e4*e.getTimezoneOffset())),e},_local_to_utc:function(t){return t&&new Date(t.getTime()-6e4*t.getTimezoneOffset())},_zero_time:function(t){return t&&new Date(t.getFullYear(),t.getMonth(),t.getDate())},_zero_utc_time:function(t){return t&&i(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate())},getDates:function(){return t.map(this.dates,this._utc_to_local)},getUTCDates:function(){return t.map(this.dates,function(t){return new Date(t)})},getDate:function(){return this._utc_to_local(this.getUTCDate())},getUTCDate:function(){var t=this.dates.get(-1);return t!==e?new Date(t):null},clearDates:function(){this.inputField.val(""),this._trigger("changeDate"),this.update(),this.o.autoclose&&this.hide()},setDates:function(){var t=Array.isArray(arguments[0])?arguments[0]:arguments;return this.update.apply(this,t),this._trigger("changeDate"),this.setValue(),this},setUTCDates:function(){var e=Array.isArray(arguments[0])?arguments[0]:arguments;return this.setDates.apply(this,t.map(e,this._utc_to_local)),this},setDate:o("setDates"),setUTCDate:o("setUTCDates"),remove:o("destroy","Method `remove` is deprecated and will be removed in version 2.0. Use `destroy` instead"),setValue:function(){var t=this.getFormattedDate();return this.inputField.val(t),this},getFormattedDate:function(i){i===e&&(i=this.o.format);var n=this.o.language;return t.map(this.dates,function(t){return m.formatDate(t,i,n)}).join(this.o.multidateSeparator)},getStartDate:function(){return this.o.startDate},setStartDate:function(t){return this._process_options({startDate:t}),this.update(),this.updateNavArrows(),this},getEndDate:function(){return this.o.endDate},setEndDate:function(t){return this._process_options({endDate:t}),this.update(),this.updateNavArrows(),this},setDaysOfWeekDisabled:function(t){return this._process_options({daysOfWeekDisabled:t}),this.update(),this},setDaysOfWeekHighlighted:function(t){return this._process_options({daysOfWeekHighlighted:t}),this.update(),this},setDatesDisabled:function(t){return this._process_options({datesDisabled:t}),this.update(),this},place:function(){if(this.isInline)return this;var e=this.picker.outerWidth(),i=this.picker.outerHeight(),n=t(this.o.container),s=n.width(),o="body"===this.o.container?t(document).scrollTop():n.scrollTop(),r=n.offset(),a=[0];this.element.parents().each(function(){var e=t(this).css("z-index");"auto"!==e&&0!==Number(e)&&a.push(Number(e))});var l=Math.max.apply(Math,a)+this.o.zIndexOffset,c=this.component?this.component.parent().offset():this.element.offset(),h=this.component?this.component.outerHeight(!0):this.element.outerHeight(!1),u=this.component?this.component.outerWidth(!0):this.element.outerWidth(!1),d=c.left-r.left,p=c.top-r.top;"body"!==this.o.container&&(p+=o),this.picker.removeClass("datepicker-orient-top datepicker-orient-bottom datepicker-orient-right datepicker-orient-left"),"auto"!==this.o.orientation.x?(this.picker.addClass("datepicker-orient-"+this.o.orientation.x),"right"===this.o.orientation.x&&(d-=e-u)):c.left<0?(this.picker.addClass("datepicker-orient-left"),d-=c.left-10):d+e>s?(this.picker.addClass("datepicker-orient-right"),d+=u-e):this.o.rtl?this.picker.addClass("datepicker-orient-right"):this.picker.addClass("datepicker-orient-left");var f=this.o.orientation.y;if("auto"===f&&(f=-o+p-i<0?"bottom":"top"),this.picker.addClass("datepicker-orient-"+f),"top"===f?p-=i+parseInt(this.picker.css("padding-top")):p+=h,this.o.rtl){var m=s-(d+u);this.picker.css({top:p,right:m,zIndex:l})}else this.picker.css({top:p,left:d,zIndex:l});return this},_allow_update:!0,update:function(){if(!this._allow_update)return this;var e=this.dates.copy(),i=[],n=!1;return arguments.length?(t.each(arguments,t.proxy(function(t,e){e instanceof Date&&(e=this._local_to_utc(e)),i.push(e)},this)),n=!0):(i=(i=this.isInput?this.element.val():this.element.data("date")||this.inputField.val())&&this.o.multidate?i.split(this.o.multidateSeparator):[i],delete this.element.data().date),i=t.map(i,t.proxy(function(t){return m.parseDate(t,this.o.format,this.o.language,this.o.assumeNearbyYear)},this)),i=t.grep(i,t.proxy(function(t){return!this.dateWithinRange(t)||!t},this),!0),this.dates.replace(i),this.o.updateViewDate&&(this.dates.length?this.viewDate=new Date(this.dates.get(-1)):this.viewDate<this.o.startDate?this.viewDate=new Date(this.o.startDate):this.viewDate>this.o.endDate?this.viewDate=new Date(this.o.endDate):this.viewDate=this.o.defaultViewDate),n?(this.setValue(),this.element.change()):this.dates.length&&String(e)!==String(this.dates)&&n&&(this._trigger("changeDate"),this.element.change()),!this.dates.length&&e.length&&(this._trigger("clearDate"),this.element.change()),this.fill(),this},fillDow:function(){if(this.o.showWeekDays){var e=this.o.weekStart,i="<tr>";for(this.o.calendarWeeks&&(i+='<th class="cw">&#160;</th>');e<this.o.weekStart+7;)i+='<th class="dow',-1!==t.inArray(e,this.o.daysOfWeekDisabled)&&(i+=" disabled"),i+='">'+f[this.o.language].daysMin[e++%7]+"</th>";i+="</tr>",this.picker.find(".datepicker-days thead").append(i)}},fillMonths:function(){for(var t=this._utc_to_local(this.viewDate),e="",i=0;i<12;i++)e+='<span class="month'+(t&&t.getMonth()===i?" focused":"")+'">'+f[this.o.language].monthsShort[i]+"</span>";this.picker.find(".datepicker-months td").html(e)},setRange:function(e){e&&e.length?this.range=t.map(e,function(t){return t.valueOf()}):delete this.range,this.fill()},getClassNames:function(e){var i=[],o=this.viewDate.getUTCFullYear(),r=this.viewDate.getUTCMonth(),a=n();return e.getUTCFullYear()<o||e.getUTCFullYear()===o&&e.getUTCMonth()<r?i.push("old"):(e.getUTCFullYear()>o||e.getUTCFullYear()===o&&e.getUTCMonth()>r)&&i.push("new"),this.focusDate&&e.valueOf()===this.focusDate.valueOf()&&i.push("focused"),this.o.todayHighlight&&s(e,a)&&i.push("today"),-1!==this.dates.contains(e)&&i.push("active"),this.dateWithinRange(e)||i.push("disabled"),this.dateIsDisabled(e)&&i.push("disabled","disabled-date"),-1!==t.inArray(e.getUTCDay(),this.o.daysOfWeekHighlighted)&&i.push("highlighted"),this.range&&(e>this.range[0]&&e<this.range[this.range.length-1]&&i.push("range"),-1!==t.inArray(e.valueOf(),this.range)&&i.push("selected"),e.valueOf()===this.range[0]&&i.push("range-start"),e.valueOf()===this.range[this.range.length-1]&&i.push("range-end")),i},_fill_yearsView:function(i,n,s,o,r,a,l){for(var c,h,u,d="",p=s/10,f=this.picker.find(i),m=Math.floor(o/s)*s,g=m+9*p,v=Math.floor(this.viewDate.getFullYear()/p)*p,y=t.map(this.dates,function(t){return Math.floor(t.getUTCFullYear()/p)*p}),b=m-p;b<=g+p;b+=p)c=[n],h=null,b===m-p?c.push("old"):b===g+p&&c.push("new"),-1!==t.inArray(b,y)&&c.push("active"),(b<r||b>a)&&c.push("disabled"),b===v&&c.push("focused"),l!==t.noop&&((u=l(new Date(b,0,1)))===e?u={}:"boolean"==typeof u?u={enabled:u}:"string"==typeof u&&(u={classes:u}),!1===u.enabled&&c.push("disabled"),u.classes&&(c=c.concat(u.classes.split(/\s+/))),u.tooltip&&(h=u.tooltip)),d+='<span class="'+c.join(" ")+'"'+(h?' title="'+h+'"':"")+">"+b+"</span>";f.find(".datepicker-switch").text(m+"-"+g),f.find("td").html(d)},fill:function(){var s,o,r=new Date(this.viewDate),a=r.getUTCFullYear(),l=r.getUTCMonth(),c=this.o.startDate!==-1/0?this.o.startDate.getUTCFullYear():-1/0,h=this.o.startDate!==-1/0?this.o.startDate.getUTCMonth():-1/0,u=this.o.endDate!==1/0?this.o.endDate.getUTCFullYear():1/0,d=this.o.endDate!==1/0?this.o.endDate.getUTCMonth():1/0,p=f[this.o.language].today||f.en.today||"",g=f[this.o.language].clear||f.en.clear||"",v=f[this.o.language].titleFormat||f.en.titleFormat,y=n(),b=(!0===this.o.todayBtn||"linked"===this.o.todayBtn)&&y>=this.o.startDate&&y<=this.o.endDate&&!this.weekOfDateIsDisabled(y);if(!isNaN(a)&&!isNaN(l)){this.picker.find(".datepicker-days .datepicker-switch").text(m.formatDate(r,v,this.o.language)),this.picker.find("tfoot .today").text(p).css("display",b?"table-cell":"none"),this.picker.find("tfoot .clear").text(g).css("display",!0===this.o.clearBtn?"table-cell":"none"),this.picker.find("thead .datepicker-title").text(this.o.title).css("display","string"==typeof this.o.title&&""!==this.o.title?"table-cell":"none"),this.updateNavArrows(),this.fillMonths();var _=i(a,l,0),w=_.getUTCDate();_.setUTCDate(w-(_.getUTCDay()-this.o.weekStart+7)%7);var x=new Date(_);_.getUTCFullYear()<100&&x.setUTCFullYear(_.getUTCFullYear()),x.setUTCDate(x.getUTCDate()+42),x=x.valueOf();for(var k,D,C=[];_.valueOf()<x;){if((k=_.getUTCDay())===this.o.weekStart&&(C.push("<tr>"),this.o.calendarWeeks)){var S=new Date(+_+(this.o.weekStart-k-7)%7*864e5),T=new Date(Number(S)+(11-S.getUTCDay())%7*864e5),M=new Date(Number(M=i(T.getUTCFullYear(),0,1))+(11-M.getUTCDay())%7*864e5),E=(T-M)/864e5/7+1;C.push('<td class="cw">'+E+"</td>")}(D=this.getClassNames(_)).push("day");var A=_.getUTCDate();this.o.beforeShowDay!==t.noop&&((o=this.o.beforeShowDay(this._utc_to_local(_)))===e?o={}:"boolean"==typeof o?o={enabled:o}:"string"==typeof o&&(o={classes:o}),!1===o.enabled&&D.push("disabled"),o.classes&&(D=D.concat(o.classes.split(/\s+/))),o.tooltip&&(s=o.tooltip),o.content&&(A=o.content)),D="function"==typeof t.uniqueSort?t.uniqueSort(D):t.unique(D),C.push('<td class="'+D.join(" ")+'"'+(s?' title="'+s+'"':"")+' data-date="'+_.getTime().toString()+'">'+A+"</td>"),s=null,k===this.o.weekEnd&&C.push("</tr>"),_.setUTCDate(_.getUTCDate()+1)}this.picker.find(".datepicker-days tbody").html(C.join(""));var O=f[this.o.language].monthsTitle||f.en.monthsTitle||"Months",N=this.picker.find(".datepicker-months").find(".datepicker-switch").text(this.o.maxViewMode<2?O:a).end().find("tbody span").removeClass("active");if(t.each(this.dates,function(t,e){e.getUTCFullYear()===a&&N.eq(e.getUTCMonth()).addClass("active")}),(a<c||a>u)&&N.addClass("disabled"),a===c&&N.slice(0,h).addClass("disabled"),a===u&&N.slice(d+1).addClass("disabled"),this.o.beforeShowMonth!==t.noop){var L=this;t.each(N,function(i,n){var s=new Date(a,i,1),o=L.o.beforeShowMonth(s);o===e?o={}:"boolean"==typeof o?o={enabled:o}:"string"==typeof o&&(o={classes:o}),!1!==o.enabled||t(n).hasClass("disabled")||t(n).addClass("disabled"),o.classes&&t(n).addClass(o.classes),o.tooltip&&t(n).prop("title",o.tooltip)})}this._fill_yearsView(".datepicker-years","year",10,a,c,u,this.o.beforeShowYear),this._fill_yearsView(".datepicker-decades","decade",100,a,c,u,this.o.beforeShowDecade),this._fill_yearsView(".datepicker-centuries","century",1e3,a,c,u,this.o.beforeShowCentury)}},updateNavArrows:function(){if(this._allow_update){var t,e,i=new Date(this.viewDate),n=i.getUTCFullYear(),s=i.getUTCMonth(),o=this.o.startDate!==-1/0?this.o.startDate.getUTCFullYear():-1/0,r=this.o.startDate!==-1/0?this.o.startDate.getUTCMonth():-1/0,a=this.o.endDate!==1/0?this.o.endDate.getUTCFullYear():1/0,l=this.o.endDate!==1/0?this.o.endDate.getUTCMonth():1/0,c=1;switch(this.viewMode){case 4:c*=10;case 3:c*=10;case 2:c*=10;case 1:t=Math.floor(n/c)*c<=o,e=Math.floor(n/c)*c+c>a;break;case 0:t=n<=o&&s<=r,e=n>=a&&s>=l}this.picker.find(".prev").toggleClass("disabled",t),this.picker.find(".next").toggleClass("disabled",e)}},click:function(e){var s,o,r;e.preventDefault(),e.stopPropagation(),(s=t(e.target)).hasClass("datepicker-switch")&&this.viewMode!==this.o.maxViewMode&&this.setViewMode(this.viewMode+1),s.hasClass("today")&&!s.hasClass("day")&&(this.setViewMode(0),this._setDate(n(),"linked"===this.o.todayBtn?null:"view")),s.hasClass("clear")&&this.clearDates(),s.hasClass("disabled")||(s.hasClass("month")||s.hasClass("year")||s.hasClass("decade")||s.hasClass("century"))&&(this.viewDate.setUTCDate(1),1,1===this.viewMode?(r=s.parent().find("span").index(s),o=this.viewDate.getUTCFullYear(),this.viewDate.setUTCMonth(r)):(r=0,o=Number(s.text()),this.viewDate.setUTCFullYear(o)),this._trigger(m.viewModes[this.viewMode-1].e,this.viewDate),this.viewMode===this.o.minViewMode?this._setDate(i(o,r,1)):(this.setViewMode(this.viewMode-1),this.fill())),this.picker.is(":visible")&&this._focused_from&&this._focused_from.focus(),delete this._focused_from},dayCellClick:function(e){var i=t(e.currentTarget).data("date"),n=new Date(i);this.o.updateViewDate&&(n.getUTCFullYear()!==this.viewDate.getUTCFullYear()&&this._trigger("changeYear",this.viewDate),n.getUTCMonth()!==this.viewDate.getUTCMonth()&&this._trigger("changeMonth",this.viewDate)),this._setDate(n)},navArrowsClick:function(e){var i=t(e.currentTarget).hasClass("prev")?-1:1;0!==this.viewMode&&(i*=12*m.viewModes[this.viewMode].navStep),this.viewDate=this.moveMonth(this.viewDate,i),this._trigger(m.viewModes[this.viewMode].e,this.viewDate),this.fill()},_toggle_multidate:function(t){var e=this.dates.contains(t);if(t||this.dates.clear(),-1!==e?(!0===this.o.multidate||this.o.multidate>1||this.o.toggleActive)&&this.dates.remove(e):!1===this.o.multidate?(this.dates.clear(),this.dates.push(t)):this.dates.push(t),"number"==typeof this.o.multidate)for(;this.dates.length>this.o.multidate;)this.dates.remove(0)},_setDate:function(t,e){e&&"date"!==e||this._toggle_multidate(t&&new Date(t)),(!e&&this.o.updateViewDate||"view"===e)&&(this.viewDate=t&&new Date(t)),this.fill(),this.setValue(),e&&"view"===e||this._trigger("changeDate"),this.inputField.trigger("change"),!this.o.autoclose||e&&"date"!==e||this.hide()},moveDay:function(t,e){var i=new Date(t);return i.setUTCDate(t.getUTCDate()+e),i},moveWeek:function(t,e){return this.moveDay(t,7*e)},moveMonth:function(t,e){if(!function(t){return t&&!isNaN(t.getTime())}(t))return this.o.defaultViewDate;if(!e)return t;var i,n,s=new Date(t.valueOf()),o=s.getUTCDate(),r=s.getUTCMonth(),a=Math.abs(e);if(e=e>0?1:-1,1===a)n=-1===e?function(){return s.getUTCMonth()===r}:function(){return s.getUTCMonth()!==i},i=r+e,s.setUTCMonth(i),i=(i+12)%12;else{for(var l=0;l<a;l++)s=this.moveMonth(s,e);i=s.getUTCMonth(),s.setUTCDate(o),n=function(){return i!==s.getUTCMonth()}}for(;n();)s.setUTCDate(--o),s.setUTCMonth(i);return s},moveYear:function(t,e){return this.moveMonth(t,12*e)},moveAvailableDate:function(t,e,i){do{if(t=this[i](t,e),!this.dateWithinRange(t))return!1;i="moveDay"}while(this.dateIsDisabled(t));return t},weekOfDateIsDisabled:function(e){return-1!==t.inArray(e.getUTCDay(),this.o.daysOfWeekDisabled)},dateIsDisabled:function(e){return this.weekOfDateIsDisabled(e)||t.grep(this.o.datesDisabled,function(t){return s(e,t)}).length>0},dateWithinRange:function(t){return t>=this.o.startDate&&t<=this.o.endDate},keydown:function(t){if(this.picker.is(":visible")){var e,i,n=!1,s=this.focusDate||this.viewDate;switch(t.keyCode){case 27:this.focusDate?(this.focusDate=null,this.viewDate=this.dates.get(-1)||this.viewDate,this.fill()):this.hide(),t.preventDefault(),t.stopPropagation();break;case 37:case 38:case 39:case 40:if(!this.o.keyboardNavigation||7===this.o.daysOfWeekDisabled.length)break;e=37===t.keyCode||38===t.keyCode?-1:1,0===this.viewMode?t.ctrlKey?(i=this.moveAvailableDate(s,e,"moveYear"))&&this._trigger("changeYear",this.viewDate):t.shiftKey?(i=this.moveAvailableDate(s,e,"moveMonth"))&&this._trigger("changeMonth",this.viewDate):37===t.keyCode||39===t.keyCode?i=this.moveAvailableDate(s,e,"moveDay"):this.weekOfDateIsDisabled(s)||(i=this.moveAvailableDate(s,e,"moveWeek")):1===this.viewMode?(38!==t.keyCode&&40!==t.keyCode||(e*=4),i=this.moveAvailableDate(s,e,"moveMonth")):2===this.viewMode&&(38!==t.keyCode&&40!==t.keyCode||(e*=4),i=this.moveAvailableDate(s,e,"moveYear")),i&&(this.focusDate=this.viewDate=i,this.setValue(),this.fill(),t.preventDefault());break;case 13:if(!this.o.forceParse)break;s=this.focusDate||this.dates.get(-1)||this.viewDate,this.o.keyboardNavigation&&(this._toggle_multidate(s),n=!0),this.focusDate=null,this.viewDate=this.dates.get(-1)||this.viewDate,this.setValue(),this.fill(),this.picker.is(":visible")&&(t.preventDefault(),t.stopPropagation(),this.o.autoclose&&this.hide());break;case 9:this.focusDate=null,this.viewDate=this.dates.get(-1)||this.viewDate,this.fill(),this.hide()}n&&(this.dates.length?this._trigger("changeDate"):this._trigger("clearDate"),this.inputField.trigger("change"))}else 40!==t.keyCode&&27!==t.keyCode||(this.show(),t.stopPropagation())},setViewMode:function(t){this.viewMode=t,this.picker.children("div").hide().filter(".datepicker-"+m.viewModes[this.viewMode].clsName).show(),this.updateNavArrows(),this._trigger("changeViewMode",new Date(this.viewDate))}};var c=function(e,i){t.data(e,"datepicker",this),this.element=t(e),this.inputs=t.map(i.inputs,function(t){return t.jquery?t[0]:t}),delete i.inputs,this.keepEmptyValues=i.keepEmptyValues,delete i.keepEmptyValues,u.call(t(this.inputs),i).on("changeDate",t.proxy(this.dateUpdated,this)),this.pickers=t.map(this.inputs,function(e){return t.data(e,"datepicker")}),this.updateDates()};c.prototype={updateDates:function(){this.dates=t.map(this.pickers,function(t){return t.getUTCDate()}),this.updateRanges()},updateRanges:function(){var e=t.map(this.dates,function(t){return t.valueOf()});t.each(this.pickers,function(t,i){i.setRange(e)})},clearDates:function(){t.each(this.pickers,function(t,e){e.clearDates()})},dateUpdated:function(i){if(!this.updating){this.updating=!0;var n=t.data(i.target,"datepicker");if(n!==e){var s=n.getUTCDate(),o=this.keepEmptyValues,r=t.inArray(i.target,this.inputs),a=r-1,l=r+1,c=this.inputs.length;if(-1!==r){if(t.each(this.pickers,function(t,e){e.getUTCDate()||e!==n&&o||e.setUTCDate(s)}),s<this.dates[a])for(;a>=0&&s<this.dates[a]&&(this.pickers[a].element.val()||"").length>0;)this.pickers[a--].setUTCDate(s);else if(s>this.dates[l])for(;l<c&&s>this.dates[l]&&(this.pickers[l].element.val()||"").length>0;)this.pickers[l++].setUTCDate(s);this.updateDates(),delete this.updating}}}},destroy:function(){t.map(this.pickers,function(t){t.destroy()}),t(this.inputs).off("changeDate",this.dateUpdated),delete this.element.data().datepicker},remove:o("destroy","Method `remove` is deprecated and will be removed in version 2.0. Use `destroy` instead")};var h=t.fn.datepicker,u=function(i){var n,s=Array.apply(null,arguments);if(s.shift(),this.each(function(){var e=t(this),o=e.data("datepicker"),a="object"==typeof i&&i;if(!o){var h=function(e,i){function n(t,e){return e.toLowerCase()}var s=t(e).data(),o={},r=new RegExp("^"+i.toLowerCase()+"([A-Z])");for(var a in i=new RegExp("^"+i.toLowerCase()),s)i.test(a)&&(o[a.replace(r,n)]=s[a]);return o}(this,"date"),u=r(t.extend({},d,h,a).language),p=t.extend({},d,u,h,a);e.hasClass("input-daterange")||p.inputs?(t.extend(p,{inputs:p.inputs||e.find("input").toArray()}),o=new c(this,p)):o=new l(this,p),e.data("datepicker",o)}"string"==typeof i&&"function"==typeof o[i]&&(n=o[i].apply(o,s))}),n===e||n instanceof l||n instanceof c)return this;if(this.length>1)throw new Error("Using only allowed for the collection of a single element ("+i+" function)");return n};t.fn.datepicker=u;var d=t.fn.datepicker.defaults={assumeNearbyYear:!1,autoclose:!1,beforeShowDay:t.noop,beforeShowMonth:t.noop,beforeShowYear:t.noop,beforeShowDecade:t.noop,beforeShowCentury:t.noop,calendarWeeks:!1,clearBtn:!1,toggleActive:!1,daysOfWeekDisabled:[],daysOfWeekHighlighted:[],datesDisabled:[],endDate:1/0,forceParse:!0,format:"mm/dd/yyyy",isInline:null,keepEmptyValues:!1,keyboardNavigation:!0,language:"en",minViewMode:0,maxViewMode:4,multidate:!1,multidateSeparator:",",orientation:"auto",rtl:!1,startDate:-1/0,startView:0,todayBtn:!1,todayHighlight:!1,updateViewDate:!0,weekStart:0,disableTouchKeyboard:!1,enableOnReadonly:!0,showOnFocus:!0,zIndexOffset:10,container:"body",immediateUpdates:!1,title:"",templates:{leftArrow:"&#x00AB;",rightArrow:"&#x00BB;"},showWeekDays:!0},p=t.fn.datepicker.locale_opts=["format","rtl","weekStart"];t.fn.datepicker.Constructor=l;var f=t.fn.datepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",titleFormat:"MM yyyy"}},m={viewModes:[{names:["days","month"],clsName:"days",e:"changeMonth"},{names:["months","year"],clsName:"months",e:"changeYear",navStep:1},{names:["years","decade"],clsName:"years",e:"changeDecade",navStep:10},{names:["decades","century"],clsName:"decades",e:"changeCentury",navStep:100},{names:["centuries","millennium"],clsName:"centuries",e:"changeMillennium",navStep:1e3}],validParts:/dd?|DD?|mm?|MM?|yy(?:yy)?/g,nonpunctuation:/[^ -\/:-@\u5e74\u6708\u65e5\[-`{-~\t\n\r]+/g,parseFormat:function(t){if("function"==typeof t.toValue&&"function"==typeof t.toDisplay)return t;var e=t.replace(this.validParts,"\0").split("\0"),i=t.match(this.validParts);if(!e||!e.length||!i||0===i.length)throw new Error("Invalid date format.");return{separators:e,parts:i}},parseDate:function(i,s,o,r){function a(){var t=this.slice(0,c[d].length),e=c[d].slice(0,t.length);return t.toLowerCase()===e.toLowerCase()}if(!i)return e;if(i instanceof Date)return i;if("string"==typeof s&&(s=m.parseFormat(s)),s.toValue)return s.toValue(i,s,o);var c,h,u,d,p,g={d:"moveDay",m:"moveMonth",w:"moveWeek",y:"moveYear"},v={yesterday:"-1d",today:"+0d",tomorrow:"+1d"};if(i in v&&(i=v[i]),/^[\-+]\d+[dmwy]([\s,]+[\-+]\d+[dmwy])*$/i.test(i)){for(c=i.match(/([\-+]\d+)([dmwy])/gi),i=new Date,d=0;d<c.length;d++)h=c[d].match(/([\-+]\d+)([dmwy])/i),u=Number(h[1]),p=g[h[2].toLowerCase()],i=l.prototype[p](i,u);return l.prototype._zero_utc_time(i)}c=i&&i.match(this.nonpunctuation)||[];var y,b,_={},w=["yyyy","yy","M","MM","m","mm","d","dd"],x={yyyy:function(t,e){return t.setUTCFullYear(r?function(t,e){return!0===e&&(e=10),t<100&&(t+=2e3)>(new Date).getFullYear()+e&&(t-=100),t}(e,r):e)},m:function(t,e){if(isNaN(t))return t;for(e-=1;e<0;)e+=12;for(e%=12,t.setUTCMonth(e);t.getUTCMonth()!==e;)t.setUTCDate(t.getUTCDate()-1);return t},d:function(t,e){return t.setUTCDate(e)}};x.yy=x.yyyy,x.M=x.MM=x.mm=x.m,x.dd=x.d,i=n();var k=s.parts.slice();if(c.length!==k.length&&(k=t(k).filter(function(e,i){return-1!==t.inArray(i,w)}).toArray()),c.length===k.length){var D,C,S;for(d=0,D=k.length;d<D;d++){if(y=parseInt(c[d],10),h=k[d],isNaN(y))switch(h){case"MM":b=t(f[o].months).filter(a),y=t.inArray(b[0],f[o].months)+1;break;case"M":b=t(f[o].monthsShort).filter(a),y=t.inArray(b[0],f[o].monthsShort)+1}_[h]=y}for(d=0;d<w.length;d++)(S=w[d])in _&&!isNaN(_[S])&&(C=new Date(i),x[S](C,_[S]),isNaN(C)||(i=C))}return i},formatDate:function(e,i,n){if(!e)return"";if("string"==typeof i&&(i=m.parseFormat(i)),i.toDisplay)return i.toDisplay(e,i,n);var s={d:e.getUTCDate(),D:f[n].daysShort[e.getUTCDay()],DD:f[n].days[e.getUTCDay()],m:e.getUTCMonth()+1,M:f[n].monthsShort[e.getUTCMonth()],MM:f[n].months[e.getUTCMonth()],yy:e.getUTCFullYear().toString().substring(2),yyyy:e.getUTCFullYear()};s.dd=(s.d<10?"0":"")+s.d,s.mm=(s.m<10?"0":"")+s.m,e=[];for(var o=t.extend([],i.separators),r=0,a=i.parts.length;r<=a;r++)o.length&&e.push(o.shift()),e.push(s[i.parts[r]]);return e.join("")},headTemplate:'<thead><tr><th colspan="7" class="datepicker-title"></th></tr><tr><th class="prev">'+d.templates.leftArrow+'</th><th colspan="5" class="datepicker-switch"></th><th class="next">'+d.templates.rightArrow+"</th></tr></thead>",contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr><tr><th colspan="7" class="clear"></th></tr></tfoot>'};m.template='<div class="datepicker"><div class="datepicker-days"><table class="table-condensed">'+m.headTemplate+"<tbody></tbody>"+m.footTemplate+'</table></div><div class="datepicker-months"><table class="table-condensed">'+m.headTemplate+m.contTemplate+m.footTemplate+'</table></div><div class="datepicker-years"><table class="table-condensed">'+m.headTemplate+m.contTemplate+m.footTemplate+'</table></div><div class="datepicker-decades"><table class="table-condensed">'+m.headTemplate+m.contTemplate+m.footTemplate+'</table></div><div class="datepicker-centuries"><table class="table-condensed">'+m.headTemplate+m.contTemplate+m.footTemplate+"</table></div></div>",t.fn.datepicker.DPGlobal=m,t.fn.datepicker.noConflict=function(){return t.fn.datepicker=h,this},t.fn.datepicker.version="1.10.0",t.fn.datepicker.deprecated=function(t){var e=window.console;e&&e.warn&&e.warn("DEPRECATED: "+t)},t(document).on("focus.datepicker.data-api click.datepicker.data-api",'[data-provide="datepicker"]',function(e){var i=t(this);i.data("datepicker")||(e.preventDefault(),u.call(i,"show"))}),t(function(){u.call(t('[data-provide="datepicker-inline"]'))})});
//# sourceMappingURL=vendor.min-min.js.map
