{% extends 'vertical_base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Column Details" %}{% endblock title %}

{% block content %}
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">{% trans "Column Details" %}</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title">{% trans "Column Information" %}</h4>
                        <p class="text-muted font-14">
                            {% trans "Details for column:" %} {{ column.original_column_name }}
                        </p>

                        <form>
                            {% csrf_token %}

                            <div class="mb-3">
                                <label for="{{ form.table_name.id_for_label }}" class="form-label">{% trans "Table Name" %}</label>
                                {{ form.table_name }}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.original_column_name.id_for_label }}" class="form-label">{% trans "Original Column Name" %}</label>
                                {{ form.original_column_name }}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.column_name.id_for_label }}" class="form-label">{% trans "Column Name" %}</label>
                                {{ form.column_name }}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.column_description.id_for_label }}" class="form-label">{% trans "Column Description" %}</label>
                                {{ form.column_description }}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.value_description.id_for_label }}" class="form-label">{% trans "Value Description" %}</label>
                                {{ form.value_description }}
                            </div>

                            <div class="mt-4">
                                <a href="{% url 'thoth_ai_backend:columns' %}" class="btn btn-secondary">{% trans "Back to Columns" %}</a>
                            </div>
                        </form>
                    </div> <!-- end card-body -->
                </div> <!-- end card -->
            </div><!-- end col -->
        </div><!-- end row -->
    </div> <!-- container -->
{% endblock content %}