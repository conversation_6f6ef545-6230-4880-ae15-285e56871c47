# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import json
import logging
import pandas as pd
from django.db import transaction
from haystack import Pipeline
from haystack.components.builders import ChatPromptBuilder
from haystack.dataclasses import ChatMessage
from tabulate import tabulate

from thoth_core.thoth_ai.thoth_workflow.comment_generation_utils import setup_default_comment_llm_model, setup_sql_db, output_to_json
from thoth_core.models import Setting, LLMChoices, SqlColumn, SqlTable
from thoth_core.thoth_ai.prompts.columns_comment_prompt import get_columns_prompt

# Configure logging
logger = logging.getLogger(__name__)

def create_column_comments_async(column_ids: list) -> str:
    """
    Async-compatible function to generate comments for specified columns.
    
    Args:
        column_ids: List of column IDs to process
        
    Returns:
        Status message indicating success or failure
    """
    try:
        if not column_ids:
            return "No columns provided"
        
        # Get columns
        columns = SqlColumn.objects.filter(id__in=column_ids)
        if not columns.exists():
            return "No valid columns found"
        
        # Group columns by table for efficient processing
        columns_by_table = {}
        for column in columns:
            table_id = column.sql_table.id
            if table_id not in columns_by_table:
                columns_by_table[table_id] = []
            columns_by_table[table_id].append(column)
        
        # Process each table's columns
        total_processed = 0
        for table_id, table_columns in columns_by_table.items():
            table = SqlTable.objects.get(id=table_id)
            workspace = Workspace.objects.filter(sql_db=table.sql_db).first()
            
            if not workspace or not workspace.setting:
                logger.warning(f"No workspace or settings found for table {table.name}")
                continue
            
            setting = workspace.setting
            language = table.sql_db.language if table.sql_db.language else setting.language
            
            # Process columns for this table
            result = process_column_chunk_for_table(table, table_columns, setting, language)
            if result == "OK":
                total_processed += len(table_columns)
            else:
                logger.error(f"Error processing columns for table {table.name}: {result}")
        
        return f"Successfully processed {total_processed} columns"
        
    except Exception as e:
        logger.error(f"Error in create_column_comments_async: {str(e)}")
        return f"Error: {str(e)}"

@transaction.atomic
def process_column_chunk_for_table(table, columns, setting, language):
    """
    Process a chunk of columns for a specific table.
    
    Args:
        table: The SqlTable object
        columns: List of SqlColumn objects to process
        setting: The Setting object
        language: Language for comments
        
    Returns:
        "OK" on success, error message on failure
    """
    try:
        # Setup database
        table_db = table.sql_db
        try:
            db = setup_sql_db(table_db)
        except Exception as e:
            return f"Error setting up SQL database: {str(e)}"

        # Get the table schema
        table_schema = db.get_table_schema(table.name)
        all_example_data = db.get_example_data(table.name, setting.example_rows_for_comment)

        # Setup LLM model
        llm = setup_default_comment_llm_model(setting)
        if llm is None:
            return "Default LLM model not found"

        # Get column names
        selected_column_names = [col.original_column_name for col in columns]
        tabulated_selected_columns = tabulate([selected_column_names], headers=['Selected Columns'], tablefmt='pipe')

        # Get the table comments
        table_comment = table.description if table.description and table.description != '' else table.generated_comment
        selected_column_comments = create_filtered_column_comments_dataframe(table, selected_column_names)

        # Prepare example data for available columns
        available_columns_in_data = set(all_example_data.keys())
        selected_columns_present_in_data = [col for col in selected_column_names if col in available_columns_in_data]
        
        # Create example data table with available columns
        if selected_columns_present_in_data:
            filtered_example_data = {col: all_example_data[col] for col in selected_columns_present_in_data}
            example_data = tabulate(filtered_example_data, headers='keys', tablefmt='pipe', showindex=False)
        else:
            example_data = "No example data available for the selected columns."

        # Create prompt template
        prompt_template = get_columns_prompt()
        if setting.comment_model.basic_model.provider == LLMChoices.GEMINI:
            template = [ChatMessage.from_user(prompt_template)]
        else:
            template = [ChatMessage.from_user(prompt_template),
                        ChatMessage.from_system('You are an expert in relational database management, SQL and database semantics. You will be given a prompt related to database management.')]

        # Create builder and pipeline
        builder = ChatPromptBuilder(template=template)
        pipeline = Pipeline()
        pipeline.add_component('builder', builder)
        pipeline.add_component('generator', llm)
        pipeline.connect('builder', 'generator')

        # Execute pipeline
        output = pipeline.run(
            data={
                "builder": {
                    "table_schema": table_schema,
                    "table_comment": table_comment,
                    "column_list": tabulated_selected_columns,
                    "column_comments": selected_column_comments,
                    "example_data": example_data,
                    "table": table,
                    "language": language,
                }
            }
        )
        
        try:
            table_comment_json = output_to_json(output)
            if table_comment_json is None:
                return f"Error: No JSON-like content found in the response"
            else:
                return_code = update_column_comments_on_backend(table_comment_json, table)
                return return_code
        except json.JSONDecodeError:
            return "Error: The generated content is not a valid JSON"
            
    except Exception as e:
        return f"Error processing column chunk: {str(e)}"

def update_column_comments(table, column_comments):
    """
    Update the generated_comment field for columns in the given table.
    
    Args:
        table: The SqlTable object containing the columns to update.
        column_comments: A list of dictionaries, each containing 'name' and 'generated_comment' keys.
    
    Returns:
        str: "OK" on success, error message on failure
    """
    try:
        for comment in column_comments:
            original_column_name = comment['name']
            generated_comment = comment['generated_comment']
            
            # Get the column object
            column = table.columns.filter(original_column_name=original_column_name).first()
            
            if column:
                # Update the generated_comment field
                column.generated_comment = generated_comment
                column.save()
                logger.info(f"Updated comment for column '{original_column_name}' in table '{table.name}'")
            else:
                return f"Column '{original_column_name}' not found in table '{table.name}'"
        return "OK"
    except Exception as e:
        logger.error(f"Error updating column comments: {str(e)}", exc_info=True)
        return f"Error updating column comments: {str(e)}"

def update_column_comments_on_backend(columns_comment_json, table):
    """Helper function to update column comments from JSON."""
    if columns_comment_json and isinstance(columns_comment_json, list):
        column_comments = [
            {"name": item["column_name"], "generated_comment": item["description"]}
            for item in columns_comment_json
        ]
        return update_column_comments(table, column_comments)
    return "Invalid JSON format"

def create_filtered_column_comments_dataframe(table, selected_names: list):
    """Create filtered column comments dataframe for processing."""
    # Create a DataFrame with column information
    column_comments = pd.DataFrame(list(table.columns.values('original_column_name', 'column_name', 'column_description', 'generated_comment', 'value_description')))

    # Replace original_comment with generated_comment where original_comment is empty
    column_comments['description'] = column_comments.apply(
        lambda row: row['generated_comment'] if pd.isna(row['column_description']) or row['column_description'] == '' else row['column_description'],
        axis=1
    )
    # Replace original_column_name with name
    column_comments['name'] = column_comments['original_column_name']
    # Keep only the 'name' and 'comment' columns
    column_comments = column_comments[['name', 'description', 'value_description']]

    # Rename columns for clarity
    column_comments = column_comments.rename(columns={"name": "Column Name", "description": "Description", "value_description": "Value Description"})

    # Remove rows where both Comment and Value Description are empty or null
    column_comments = column_comments[
        (column_comments['Description'].notna() & (column_comments['Description'] != '')) |
        (column_comments['Value Description'].notna() & (column_comments['Value Description'] != ''))
    ]
    
    # Filter the DataFrame to include only the columns in selected_names
    column_comments = column_comments[column_comments['Column Name'].isin(selected_names)]
    
    return column_comments