# Thoth SQL Database Manager - Usage Examples

This document provides practical examples for using the Thoth SQL Database Manager library across different database types and use cases.

## Table of Contents

1. [Basic Database Operations](#basic-database-operations)
2. [Database-Specific Examples](#database-specific-examples)
3. [LSH Similarity Search](#lsh-similarity-search)
4. [Advanced Features](#advanced-features)
5. [Error Handling](#error-handling)
6. [Real-World Use Cases](#real-world-use-cases)

## Basic Database Operations

### Creating a Database Manager

```python
from dbmanager import ThothDbManager

# PostgreSQL
pg_manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    db_mode="production",
    host="localhost",
    port=5432,
    database="myapp",
    user="dbuser",
    password="dbpass"
)

# SQLite
sqlite_manager = ThothDbManager.get_instance(
    db_type="sqlite",
    db_root_path="./data",
    db_mode="dev",
    database_path="./data/myapp.db"
)
```

### Exploring Database Structure

```python
# Get all tables
tables = manager.get_tables()
print(f"Found {len(tables)} tables:")
for table in tables:
    print(f"  - {table['name']}: {table['comment']}")

# Get columns for a specific table
columns = manager.get_columns("users")
print(f"\nColumns in 'users' table:")
for col in columns:
    pk_flag = " (PRIMARY KEY)" if col['is_pk'] else ""
    nullable = "NULL" if col['is_nullable'] else "NOT NULL"
    print(f"  - {col['name']}: {col['data_type']} {nullable}{pk_flag}")
    if col['comment']:
        print(f"    Comment: {col['comment']}")

# Get foreign key relationships
foreign_keys = manager.get_foreign_keys()
print(f"\nForeign key relationships:")
for fk in foreign_keys:
    print(f"  {fk['source_table_name']}.{fk['source_column_name']} -> "
          f"{fk['target_table_name']}.{fk['target_column_name']}")
```

### Executing SQL Queries

```python
# Simple SELECT query
users = manager.execute_sql("SELECT * FROM users LIMIT 10")
print(f"Retrieved {len(users)} users")

# Parameterized query (PostgreSQL style)
active_users = manager.execute_sql(
    "SELECT * FROM users WHERE active = %(active)s AND age > %(min_age)s",
    params={"active": True, "min_age": 18}
)

# Fetch single row
user = manager.execute_sql(
    "SELECT * FROM users WHERE id = %(user_id)s",
    params={"user_id": 123},
    fetch="one"
)

# Fetch limited number of rows
recent_orders = manager.execute_sql(
    "SELECT * FROM orders ORDER BY created_at DESC",
    fetch=5
)

# INSERT, UPDATE, DELETE operations
manager.execute_sql(
    "INSERT INTO users (name, email, age) VALUES (%(name)s, %(email)s, %(age)s)",
    params={"name": "John Doe", "email": "<EMAIL>", "age": 30}
)

manager.execute_sql(
    "UPDATE users SET last_login = NOW() WHERE id = %(user_id)s",
    params={"user_id": 123}
)
```

## Database-Specific Examples

### PostgreSQL Example

```python
from dbmanager import ThothDbManager

# Connect to PostgreSQL
pg_manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    db_mode="production",
    host="localhost",
    port=5432,
    database="ecommerce",
    user="app_user",
    password="secure_password"
)

# Create a table with PostgreSQL-specific features
pg_manager.execute_sql("""
    CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        category_id INTEGER REFERENCES categories(id),
        tags TEXT[],
        metadata JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
""")

# Insert data with PostgreSQL arrays and JSON
pg_manager.execute_sql("""
    INSERT INTO products (name, description, price, category_id, tags, metadata)
    VALUES (
        %(name)s, 
        %(description)s, 
        %(price)s, 
        %(category_id)s, 
        %(tags)s, 
        %(metadata)s
    )
""", params={
    "name": "Wireless Headphones",
    "description": "High-quality wireless headphones with noise cancellation",
    "price": 199.99,
    "category_id": 1,
    "tags": ["electronics", "audio", "wireless"],
    "metadata": {"brand": "TechCorp", "warranty_years": 2, "color": "black"}
})

# Query with PostgreSQL-specific features
products = pg_manager.execute_sql("""
    SELECT 
        p.name,
        p.price,
        p.tags,
        p.metadata->>'brand' as brand,
        c.name as category_name
    FROM products p
    JOIN categories c ON p.category_id = c.id
    WHERE p.tags && %(search_tags)s
    ORDER BY p.created_at DESC
""", params={"search_tags": ["electronics", "wireless"]})

print("PostgreSQL Products:")
for product in products:
    print(f"  {product['name']} - ${product['price']} ({product['brand']})")
```

### MySQL/MariaDB Example

```python
from dbmanager import ThothDbManager

# Connect to MySQL
mysql_manager = ThothDbManager.get_instance(
    db_type="mysql",
    db_root_path="./data",
    db_mode="production",
    host="localhost",
    port=3306,
    database="blog",
    user="blog_user",
    password="blog_password"
)

# Create tables with MySQL-specific features
mysql_manager.execute_sql("""
    CREATE TABLE IF NOT EXISTS posts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content LONGTEXT,
        author_id INT,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        tags SET('technology', 'science', 'business', 'lifestyle'),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FULLTEXT(title, content)
    ) ENGINE=InnoDB
""")

# Insert blog post
mysql_manager.execute_sql("""
    INSERT INTO posts (title, content, author_id, status, tags)
    VALUES (%(title)s, %(content)s, %(author_id)s, %(status)s, %(tags)s)
""", params={
    "title": "Introduction to Database Management",
    "content": "This post covers the basics of database management...",
    "author_id": 1,
    "status": "published",
    "tags": "technology,science"
})

# Full-text search (MySQL specific)
search_results = mysql_manager.execute_sql("""
    SELECT id, title, 
           MATCH(title, content) AGAINST(%(search_term)s) as relevance
    FROM posts 
    WHERE MATCH(title, content) AGAINST(%(search_term)s)
    ORDER BY relevance DESC
""", params={"search_term": "database management"})

print("MySQL Search Results:")
for result in search_results:
    print(f"  {result['title']} (relevance: {result['relevance']:.2f})")
```

### SQLite Example

```python
from dbmanager import ThothDbManager
import os

# Ensure data directory exists
os.makedirs("./data", exist_ok=True)

# Connect to SQLite
sqlite_manager = ThothDbManager.get_instance(
    db_type="sqlite",
    db_root_path="./data",
    db_mode="dev",
    database_path="./data/inventory.db"
)

# Create tables
sqlite_manager.execute_sql("""
    CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_name TEXT NOT NULL,
        sku TEXT UNIQUE NOT NULL,
        quantity INTEGER DEFAULT 0,
        price REAL NOT NULL,
        supplier TEXT,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
    )
""")

sqlite_manager.execute_sql("""
    CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        inventory_id INTEGER,
        transaction_type TEXT CHECK(transaction_type IN ('in', 'out')),
        quantity INTEGER NOT NULL,
        transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        notes TEXT,
        FOREIGN KEY (inventory_id) REFERENCES inventory (id)
    )
""")

# Insert sample data
products = [
    ("Laptop Computer", "LAP001", 10, 999.99, "TechSupplier"),
    ("Wireless Mouse", "MOU001", 50, 29.99, "TechSupplier"),
    ("Office Chair", "CHR001", 5, 199.99, "FurnitureSupplier"),
    ("Monitor 24inch", "MON001", 15, 299.99, "TechSupplier")
]

for product in products:
    sqlite_manager.execute_sql("""
        INSERT OR REPLACE INTO inventory 
        (product_name, sku, quantity, price, supplier)
        VALUES (?, ?, ?, ?, ?)
    """, params=product)

# Query with aggregation
inventory_report = sqlite_manager.execute_sql("""
    SELECT 
        supplier,
        COUNT(*) as product_count,
        SUM(quantity * price) as total_value,
        AVG(price) as avg_price
    FROM inventory
    GROUP BY supplier
    ORDER BY total_value DESC
""")

print("SQLite Inventory Report:")
for row in inventory_report:
    print(f"  {row['supplier']}: {row['product_count']} products, "
          f"${row['total_value']:.2f} total value, "
          f"${row['avg_price']:.2f} avg price")
```

## LSH Similarity Search

### Basic LSH Usage

```python
from dbmanager import ThothDbManager

# Create manager and ensure LSH is initialized
manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    db_mode="dev",
    host="localhost",
    database="customer_db",
    user="user",
    password="pass"
)

# Initialize LSH (this may take time for large databases)
lsh_status = manager.set_lsh()
if lsh_status == "success":
    print("LSH initialized successfully")
else:
    print("LSH initialization failed")
    exit(1)

# Search for similar customer names
similar_names = manager.query_lsh(
    keyword="john smith",
    top_n=10,
    signature_size=30,
    n_gram=3
)

print("Similar names to 'john smith':")
for table_name, columns in similar_names.items():
    for column_name, values in columns.items():
        if values:  # Only show columns with results
            print(f"\n{table_name}.{column_name}:")
            for i, value in enumerate(values, 1):
                print(f"  {i}. {value}")
```

### Advanced LSH Search

```python
# Search with different parameters for different use cases

# High precision search (larger signature, smaller n-gram)
precise_search = manager.query_lsh(
    keyword="customer service",
    signature_size=50,  # Higher precision
    n_gram=2,          # Smaller n-gram for better word matching
    top_n=5
)

# Fast fuzzy search (smaller signature, larger n-gram)
fuzzy_search = manager.query_lsh(
    keyword="<EMAIL>",
    signature_size=20,  # Faster processing
    n_gram=4,          # Better for email-like patterns
    top_n=15
)

# Search for product descriptions
product_search = manager.query_lsh(
    keyword="wireless bluetooth headphones",
    signature_size=40,
    n_gram=3,
    top_n=8
)

print("Product search results:")
for table, columns in product_search.items():
    for column, values in columns.items():
        if values and 'description' in column.lower():
            print(f"\nSimilar product descriptions in {table}.{column}:")
            for value in values:
                print(f"  - {value[:100]}...")  # Truncate long descriptions
```

### LSH for Data Quality

```python
# Use LSH to find potential duplicates
def find_potential_duplicates(manager, table_name, column_name, threshold=0.8):
    """Find potential duplicate entries using LSH"""
    
    # Get all unique values from the column
    unique_values = manager.get_unique_values()
    
    if table_name not in unique_values or column_name not in unique_values[table_name]:
        print(f"Column {table_name}.{column_name} not found")
        return []
    
    values = unique_values[table_name][column_name]
    duplicates = []
    
    # Check each value against LSH
    for value in values:
        similar = manager.query_lsh(
            keyword=value,
            signature_size=30,
            n_gram=3,
            top_n=10
        )
        
        if table_name in similar and column_name in similar[table_name]:
            similar_values = similar[table_name][column_name]
            # Filter out the exact match and very different values
            potential_dups = [v for v in similar_values if v != value and len(v) > 0]
            if potential_dups:
                duplicates.append({
                    'original': value,
                    'similar': potential_dups[:3]  # Top 3 similar values
                })
    
    return duplicates

# Find duplicate customer names
duplicates = find_potential_duplicates(manager, "customers", "full_name")
print("Potential duplicate customer names:")
for dup in duplicates[:10]:  # Show first 10
    print(f"'{dup['original']}' similar to: {dup['similar']}")
```

## Advanced Features

### Using the Factory Pattern

```python
from dbmanager import ThothDbFactory

# List available database types
available_dbs = ThothDbFactory.list_available_databases()
print(f"Available databases: {available_dbs}")

# Get parameter requirements for each database type
for db_type in available_dbs:
    params = ThothDbFactory.get_required_parameters(db_type)
    print(f"\n{db_type.upper()} parameters:")
    print(f"  Required: {params.get('required', [])}")
    print(f"  Optional: {params.get('optional', [])}")

# Create manager with validation
try:
    manager = ThothDbFactory.create_with_validation(
        db_type="postgresql",
        db_root_path="./data",
        db_mode="production",
        host="localhost",
        port=5432,
        database="myapp",
        user="dbuser",
        password="dbpass"
    )
    print("Manager created successfully with validation")
except ValueError as e:
    print(f"Validation failed: {e}")

# Get plugin status
status = ThothDbFactory.get_plugin_status()
print(f"\nPlugin status: {status['total_plugins']} plugins available")
for db_type, info in status['plugins'].items():
    print(f"  {db_type}: {info['status']}")
```

### Document-Based API

```python
from dbmanager import ThothDbManager

manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    host="localhost",
    database="mydb",
    user="user",
    password="pass"
)

# Check if document-based API is available
if hasattr(manager, 'get_tables_as_documents'):
    print("Document-based API is available")
    
    # Get tables as structured documents
    table_docs = manager.get_tables_as_documents()
    print(f"\nFound {len(table_docs)} table documents:")
    
    for doc in table_docs:
        print(f"\nTable Document:")
        print(f"  ID: {doc.id}")
        print(f"  Name: {doc.table_name}")
        print(f"  Schema: {doc.schema_name}")
        print(f"  Type: {doc.thoth_type.value}")
        print(f"  Comment: {doc.comment}")
        print(f"  Row Count: {doc.row_count}")
        print(f"  Generated Text: {doc.text}")
        
        # Get columns for this table
        if hasattr(manager, 'get_columns_as_documents'):
            column_docs = manager.get_columns_as_documents(doc.table_name)
            print(f"  Columns ({len(column_docs)}):")
            for col_doc in column_docs[:3]:  # Show first 3 columns
                print(f"    - {col_doc.column_name}: {col_doc.data_type}")
                if col_doc.is_pk:
                    print(f"      (Primary Key)")
                if not col_doc.is_nullable:
                    print(f"      (NOT NULL)")

# Health check
if hasattr(manager, 'health_check'):
    is_healthy = manager.health_check()
    print(f"\nDatabase health check: {'✓ Healthy' if is_healthy else '✗ Unhealthy'}")

# Connection info
if hasattr(manager, 'get_connection_info'):
    conn_info = manager.get_connection_info()
    print(f"\nConnection Info:")
    for key, value in conn_info.items():
        if key != 'connection_params':  # Don't show sensitive info
            print(f"  {key}: {value}")
```

### Working with Example Data

```python
# Get example data to understand table contents
def analyze_table_data(manager, table_name):
    """Analyze table data using example values"""
    
    print(f"\n=== Analyzing table: {table_name} ===")
    
    # Get column information
    columns = manager.get_columns(table_name)
    print(f"Columns: {len(columns)}")
    
    # Get example data
    examples = manager.get_example_data(table_name, number_of_rows=20)
    
    for column_info in columns:
        column_name = column_info['name']
        data_type = column_info['data_type']
        
        if column_name in examples:
            values = examples[column_name]
            print(f"\n{column_name} ({data_type}):")
            print(f"  Unique values: {len(values)}")
            print(f"  Examples: {values[:5]}")  # Show first 5 values
            
            # Analyze data patterns
            if values:
                avg_length = sum(len(str(v)) for v in values) / len(values)
                print(f"  Average length: {avg_length:.1f} characters")
                
                # Check for common patterns
                if any('@' in str(v) for v in values):
                    print(f"  Contains email-like values")
                if any(str(v).isdigit() for v in values):
                    print(f"  Contains numeric values")

# Analyze multiple tables
tables = manager.get_tables()
for table in tables[:3]:  # Analyze first 3 tables
    analyze_table_data(manager, table['name'])
```

## Error Handling

### Comprehensive Error Handling

```python
from dbmanager import ThothDbManager, ThothDbFactory
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_manager_safely(db_config):
    """Create database manager with comprehensive error handling"""
    
    try:
        # Validate database type first
        if not ThothDbFactory.validate_database_type(db_config['db_type']):
            available = ThothDbFactory.list_available_databases()
            raise ValueError(f"Unsupported database type '{db_config['db_type']}'. "
                           f"Available: {available}")
        
        # Check required parameters
        required_params = ThothDbFactory.get_required_parameters(db_config['db_type'])
        missing_params = []
        
        for param in required_params.get('required', []):
            if param not in db_config:
                missing_params.append(param)
        
        if missing_params:
            raise ValueError(f"Missing required parameters: {missing_params}")
        
        # Create manager
        manager = ThothDbManager.get_instance(**db_config)
        logger.info(f"Successfully created {db_config['db_type']} manager")
        return manager
        
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        raise
    except ImportError as e:
        logger.error(f"Missing database driver: {e}")
        # Provide installation instructions
        db_type = db_config.get('db_type', 'unknown')
        if db_type == 'postgresql':
            logger.info("Install with: pip install psycopg2-binary")
        elif db_type == 'mysql':
            logger.info("Install with: pip install mysql-connector-python")
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating manager: {e}")
        raise

def execute_query_safely(manager, sql, params=None):
    """Execute SQL query with error handling"""
    
    try:
        results = manager.execute_sql(sql, params=params)
        logger.info(f"Query executed successfully, returned {len(results) if results else 0} rows")
        return results
        
    except Exception as e:
        logger.error(f"Query execution failed: {e}")
        logger.error(f"SQL: {sql}")
        logger.error(f"Params: {params}")
        
        # Check if it's a connection issue
        if hasattr(manager, 'health_check'):
            if not manager.health_check():
                logger.error("Database connection is unhealthy")
        
        raise

# Example usage with error handling
db_configs = [
    {
        'db_type': 'postgresql',
        'db_root_path': './data',
        'host': 'localhost',
        'port': 5432,
        'database': 'testdb',
        'user': 'testuser',
        'password': 'testpass'
    },
    {
        'db_type': 'sqlite',
        'db_root_path': './data',
        'database_path': './data/test.db'
    }
]

for config in db_configs:
    try:
        manager = create_manager_safely(config)
        
        # Test basic operations
        tables = manager.get_tables()
        logger.info(f"Found {len(tables)} tables")
        
        # Test query execution
        if tables:
            table_name = tables[0]['name']
            columns = execute_query_safely(
                manager, 
                f"SELECT * FROM {table_name} LIMIT 1"
            )
            
    except Exception as e:
        logger.error(f"Failed to work with {config['db_type']}: {e}")
        continue
```

## Real-World Use Cases

### E-commerce Product Catalog

```python
from dbmanager import ThothDbManager

# Connect to product database
manager = ThothDbManager.get_instance(
    db_type="postgresql",
    db_root_path="./data",
    db_mode="production",
    host="localhost",
    port=5432,
    database="ecommerce",
    user="app_user",
    password="secure_password"
)

def search_products(search_term, category=None, price_range=None):
    """Search products using both SQL and LSH"""
    
    # Build SQL query
    sql = """
        SELECT p.id, p.name, p.description, p.price, c.name as category
        FROM products p
        JOIN categories c ON p.category_id = c.id
        WHERE 1=1
    """
    params = {}
    
    if category:
        sql += " AND c.name = %(category)s"
        params['category'] = category
    
    if price_range:
        sql += " AND p.price BETWEEN %(min_price)s AND %(max_price)s"
        params['min_price'] = price_range[0]
        params['max_price'] = price_range[1]
    
    # Text search using database full-text search
    if search_term:
        sql += " AND (p.name ILIKE %(search)s OR p.description ILIKE %(search)s)"
        params['search'] = f"%{search_term}%"
    
    sql += " ORDER BY p.name LIMIT 20"
    
    # Execute SQL search
    sql_results = manager.execute_sql(sql, params=params)
    
    # Also search using LSH for similar product names/descriptions
    lsh_results = []
    if search_term:
        similar = manager.query_lsh(search_term, top_n=10)
        for table, columns in similar.items():
            if table == 'products':
                for column, values in columns.items():
                    if column in ['name', 'description'] and values:
                        lsh_results.extend(values)
    
    return {
        'sql_results': sql_results,
        'similar_terms': lsh_results[:10]  # Top 10 similar terms
    }

# Example product search
results = search_products(
    search_term="wireless headphones",
    category="Electronics",
    price_range=(50, 300)
)

print("SQL Search Results:")
for product in results['sql_results']:
    print(f"  {product['name']} - ${product['price']} ({product['category']})")

print("\nSimilar terms found by LSH:")
for term in results['similar_terms']:
    print(f"  - {term}")
```

### Customer Data Analysis

```python
def analyze_customer_data(manager):
    """Comprehensive customer data analysis"""
    
    print("=== Customer Data Analysis ===")
    
    # Get customer demographics
    demographics = manager.execute_sql("""
        SELECT 
            CASE 
                WHEN age < 25 THEN '18-24'
                WHEN age < 35 THEN '25-34'
                WHEN age < 45 THEN '35-44'
                WHEN age < 55 THEN '45-54'
                ELSE '55+'
            END as age_group,
            COUNT(*) as customer_count,
            AVG(total_spent) as avg_spent
        FROM customers
        WHERE age IS NOT NULL
        GROUP BY age_group
        ORDER BY age_group
    """)
    
    print("\nCustomer Demographics:")
    for demo in demographics:
        print(f"  {demo['age_group']}: {demo['customer_count']} customers, "
              f"avg spent: ${demo['avg_spent']:.2f}")
    
    # Find potential duplicate customers using LSH
    print("\n=== Potential Duplicate Customers ===")
    
    # Get unique customer names
    unique_values = manager.get_unique_values()
    if 'customers' in unique_values and 'full_name' in unique_values['customers']:
        customer_names = unique_values['customers']['full_name']
        
        duplicates_found = 0
        for name in customer_names[:50]:  # Check first 50 names
            similar = manager.query_lsh(name, top_n=5)
            if 'customers' in similar and 'full_name' in similar['customers']:
                similar_names = [n for n in similar['customers']['full_name'] if n != name]
                if similar_names:
                    print(f"'{name}' similar to: {similar_names}")
                    duplicates_found += 1
        
        print(f"\nFound {duplicates_found} potential duplicate customer names")
    
    # Analyze customer email domains
    email_domains = manager.execute_sql("""
        SELECT 
            SUBSTRING(email FROM '@(.*)$') as domain,
            COUNT(*) as customer_count
        FROM customers
        WHERE email IS NOT NULL
        GROUP BY domain
        ORDER BY customer_count DESC
        LIMIT 10
    """)
    
    print("\nTop Email Domains:")
    for domain in email_domains:
        print(f"  {domain['domain']}: {domain['customer_count']} customers")

# Run customer analysis
analyze_customer_data(manager)
```

### Data Migration and Validation

```python
def migrate_data_between_databases():
    """Example of migrating data between different database types"""
    
    # Source database (PostgreSQL)
    source_manager = ThothDbManager.get_instance(
        db_type="postgresql",
        db_root_path="./data",
        host="old-server",
        database="legacy_db",
        user="migration_user",
        password="migration_pass"
    )
    
    # Target database (MySQL)
    target_manager = ThothDbManager.get_instance(
        db_type="mysql",
        db_root_path="./data",
        host="new-server",
        database="new_db",
        user="migration_user",
        password="migration_pass"
    )
    
    # Get table structure from source
    source_tables = source_manager.get_tables()
    
    for table in source_tables:
        table_name = table['name']
        print(f"Migrating table: {table_name}")
        
        # Get column structure
        columns = source_manager.get_columns(table_name)
        
        # Create table in target (simplified - would need type mapping)
        create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ("
        column_defs = []
        
        for col in columns:
            col_def = f"{col['name']} {col['data_type']}"
            if not col['is_nullable']:
                col_def += " NOT NULL"
            if col['is_pk']:
                col_def += " PRIMARY KEY"
            column_defs.append(col_def)
        
        create_sql += ", ".join(column_defs) + ")"
        
        try:
            target_manager.execute_sql(create_sql)
            print(f"  Created table structure")
        except Exception as e:
            print(f"  Error creating table: {e}")
            continue
        
        # Migrate data in batches
        batch_size = 1000
        offset = 0
        
        while True:
            # Get batch from source
            batch_data = source_manager.execute_sql(
                f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
            )
            
            if not batch_data:
                break
            
            # Insert batch into target
            for row in batch_data:
                placeholders = ", ".join(["%s"] * len(row))
                insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
                
                try:
                    target_manager.execute_sql(insert_sql, params=list(row.values()))
                except Exception as e:
                    print(f"  Error inserting row: {e}")
            
            print(f"  Migrated {len(batch_data)} rows (offset: {offset})")
            offset += batch_size
        
        print(f"  Completed migration of {table_name}")

# Run migration
migrate_data_between_databases()
```

This comprehensive collection of examples demonstrates the full capabilities of the Thoth SQL Database Manager library across different database types, use cases, and advanced features. The examples progress from basic operations to complex real-world scenarios, providing practical guidance for implementing the library in various applications.
