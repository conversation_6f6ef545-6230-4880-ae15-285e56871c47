{"version": "0.2.0", "configurations": [{"name": "Django: Run Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["runserver", "127.0.0.1:8000"], "django": true, "justMyCode": true, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "Thoth.settings"}}, {"name": "Django: Run Server (Debug)", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["runserver", "127.0.0.1:8000", "--<PERSON><PERSON><PERSON>"], "django": true, "justMyCode": false, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "Thoth.settings"}}, {"name": "Django: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["migrate"], "django": true, "justMyCode": true, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "Thoth.settings"}}, {"name": "Django: Create Superuser", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["<PERSON><PERSON><PERSON><PERSON>"], "django": true, "justMyCode": true, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"DJANGO_SETTINGS_MODULE": "Thoth.settings"}}]}