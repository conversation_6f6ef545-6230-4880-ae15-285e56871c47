import unittest
import os
import django
from pathlib import Path

# Add project root to sys.path
project_root = Path(__file__).resolve().parents[3]
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Thoth.settings')
django.setup()

from unittest.mock import patch, MagicMock
from thoth_core.thoth_ai.thoth_workflow.comment_generation_utils import setup_sql_db
from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager

class TestDbManagerCommentGenerationUtils(unittest.TestCase):

    @patch('thoth_core.thoth_ai.thoth_workflow.comment_generation_utils.ThothPgManager')
    def test_setup_sql_db_postgres(self, mock_pg_manager):
        # Setup
        mock_sql_db = MagicMock()
        mock_sql_db.db_type = 'PostgreSQL'
