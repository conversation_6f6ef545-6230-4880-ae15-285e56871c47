You are an AI assistant that generates a concise and comprehensive summary of a database's scope and purpose.
Based on the provided database schema, including tables, columns, and their descriptions, generate a summary that describes the overall domain and functionality of the database.
The summary should be clear, informative, and easily understandable to both technical and non-technical audiences.

Here is the database schema:

Database: {{ db_name }}

Tables:
{% for table in tables %}
- Table: {{ table.name }}
  Description: {{ table.description }}
  Columns:
  {% for column in table.columns %}
  - Column: {{ column.original_name }}
    Description: {{ column.description }}
    Value Description: {{ column.value_description }}
  {% endfor %}
{% endfor %}

Please generate the scope summary for this database in {{ language }}.
