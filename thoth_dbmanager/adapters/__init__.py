"""
Database adapters for Thoth SQL Database Manager.
"""

from .postgresql import PostgreSQLAdapter
from .sqlite import SQLiteAdapter
from .supabase import <PERSON>pa<PERSON><PERSON>dapter
from .mysql import MySQLAdapter
from .mariadb import MariaDBA<PERSON>pter
from .sqlserver import SQLServerAdapter
from .oracle import OracleAdapter
from .informix import InformixAdapter

__all__ = [
    "PostgreSQLAdapter",
    "SQLiteAdapter",
    "SupabaseAdapter",
    "MySQLAdapter",
    "MariaDBAdapter",
    "SQLServerAdapter",
    "OracleAdapter",
    "InformixAdapter",
]
