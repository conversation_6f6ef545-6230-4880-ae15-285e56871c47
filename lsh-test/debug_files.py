#!/usr/bin/env python3
"""
Debug script to check available files and paths.
"""

from pathlib import Path
from config_loader import Config<PERSON>oa<PERSON>

def debug_files():
    """Debug file availability."""
    print("🔍 Debugging LSH Test Setup")
    print("=" * 50)
    
    try:
        # Load config
        config = ConfigLoader()
        print(f"✅ Config loaded from: {config.config_path}")
        
        # Show config values
        print("\n📋 Configuration:")
        for key, value in config.config.items():
            if 'API_KEY' in key:
                print(f"  {key}: {'*' * 20}")
            else:
                print(f"  {key}: {value}")
        
        # Check LSH path
        lsh_path = config.get_lsh_path()
        print(f"\n📁 LSH Path: {lsh_path}")
        print(f"   Exists: {lsh_path.exists()}")
        
        if lsh_path.exists():
            print("\n📄 Files in LSH directory:")
            for file in lsh_path.iterdir():
                print(f"   {file.name} ({file.stat().st_size} bytes)")
        else:
            # Check parent directories
            print(f"\n🔍 Checking parent directories:")
            current = lsh_path
            while current != current.parent:
                print(f"   {current}: {'✅' if current.exists() else '❌'}")
                current = current.parent
        
        # Check for specific LSH files
        db_name = config.get('DB_NAME')
        expected_files = [
            f"{db_name}_lsh.pkl",
            f"{db_name}_minhashes.pkl"
        ]
        
        print(f"\n🎯 Expected LSH files for '{db_name}':")
        for filename in expected_files:
            filepath = lsh_path / filename
            print(f"   {filename}: {'✅' if filepath.exists() else '❌'}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_files()