# Thoth SQL Database Manager - Memory Bank

This memory bank contains essential information about the Thoth SQL Database Manager project for quick reference and onboarding.

## Memory Bank Contents

### 📋 Project Overview
- **File**: `project-overview.md`
- **Purpose**: High-level project description and key features
- **When to use**: Initial project understanding, onboarding new developers

### 🏗️ Technical Architecture
- **File**: `technical-architecture.md`
- **Purpose**: Detailed technical architecture and design patterns
- **When to use**: Understanding system design, making architectural decisions

### 🗄️ Database Support Matrix
- **File**: `database-support-matrix.md`
- **Purpose**: Complete database support information
- **When to use**: Choosing databases, understanding capabilities

### 🧪 Testing Strategy
- **File**: `testing-strategy.md`
- **Purpose**: Testing approach and execution guidelines
- **When to use**: Running tests, adding new test cases

### 🔧 Development Workflow
- **File**: `development-workflow.md`
- **Purpose**: Setup instructions and development commands
- **When to use**: Initial setup, daily development tasks

### ⚠️ Common Issues
- **File**: `common-issues.md`
- **Purpose**: Troubleshooting guide for common problems
- **When to use**: Debugging issues, resolving errors

### ⚡ Quick Reference
- **File**: `quick-reference.md`
- **Purpose**: Quick code snippets and commands
- **When to use**: Daily development, quick lookups

## How to Use This Memory Bank

1. **New Team Members**: Start with `project-overview.md` and `development-workflow.md`
2. **Database Decisions**: Use `database-support-matrix.md`
3. **Debugging**: Check `common-issues.md` first
4. **Daily Work**: Use `quick-reference.md` for common commands
5. **Architecture Changes**: Review `technical-architecture.md`

## Updating the Memory Bank

When adding new information:
1. Update the relevant file(s)
2. Keep information concise and actionable
3. Include code examples where helpful
4. Update this README if adding new files

## Project Status
- **Last Updated**: 2025-07-24
- **Version**: 2.0.0 (New Architecture)
- **Status**: Active Development
- **Next Milestone**: Production Release
