import logging
from pathlib import Path
import sys
from tabulate import tabulate

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dbmanager.impl.ThothSqliteManager import ThothSqliteManager

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_user_input(prompt: str, default: str) -> str:
    """
    Prompts the user for input with a default value.
    """
    user_input = input(f"{prompt} [{default}]: ")
    return user_input.strip() or default

def main():
    """
    Main function to run the interactive test.
    """
    print("--- Interactive Test for ThothSqliteManager.execute_sql ---")

    # Get initialization parameters from the user
    db_id = get_user_input("Enter the db_id", "california_schools")
    db_root_path = get_user_input("Enter the db_root_path", "/Users/<USER>/DjangoExperimental/Thoth/data")
    db_mode = get_user_input("Enter the db_mode", "dev")

    # Get the SQL query from the user
    print("\nEnter the SQL query to execute.")
    sql_query = input("> ")

    if not sql_query:
        logger.error("SQL query cannot be empty.")
        return

    try:
        # Initialize the manager
        logger.info(f"Initializing ThothSqliteManager with: db_id='{db_id}', db_root_path='{db_root_path}', db_mode='{db_mode}'")
        db_manager = ThothSqliteManager.get_instance(
            db_id=db_id,
            db_root_path=db_root_path,
            db_mode=db_mode
        )
        logger.info("Successfully connected to the database.")

        # Execute the SQL query
        logger.info(f"Executing SQL query:\n{sql_query}")
        result = db_manager.execute_sql(sql_query)

        # Display the result
        if isinstance(result, list) and result:
            print("\n--- Query Result ---")
            # Assuming the result is a list of dicts
            headers = result[0].keys()
            print(tabulate(result, headers="keys", tablefmt="psql"))
        elif isinstance(result, dict):
             print("\n--- Query Result ---")
             print(tabulate([result], headers="keys", tablefmt="psql"))
        elif result is None:
            print("\n--- Query Result ---")
            print("Query executed successfully, but returned no result.")
        else:
            print("\n--- Query Result ---")
            print(f"Query executed. Result (row count or other): {result}")

    except FileNotFoundError as e:
        logger.error(f"Database file not found. Please check your paths. Details: {e}")
    except Exception as e:
        logger.error(f"An error occurred: {e}")

if __name__ == '__main__':
    main()
