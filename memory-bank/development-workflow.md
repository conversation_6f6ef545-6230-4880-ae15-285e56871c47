# Development Workflow

## Setup Instructions

### 1. Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### 2. Database Setup
Each database requires specific setup:

#### SQLite
- No additional setup required
- Uses file-based database

#### PostgreSQL
```bash
# Install PostgreSQL
# Create database and user
createdb thoth_test
createuser thoth_user
```

#### MySQL/MariaDB
```bash
# Install MySQL/MariaDB
# Create database and user
mysql -u root -p
CREATE DATABASE thoth_test;
CREATE USER 'thoth_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL ON thoth_test.* TO 'thoth_user'@'localhost';
```

#### Oracle
- Install Oracle Instant Client
- Set environment variables
- Configure tnsnames.ora

#### SQL Server
- Install SQL Server
- Create database
- Configure connection strings

#### Informix
- Install Informix client
- Configure environment variables
- Set up connection parameters

## Development Commands

### Running Tests
```bash
# All tests
pytest

# Specific database
pytest tests/test_thoth_sqlite_manager.py

# With coverage
pytest --cov=dbmanager tests/

# Verbose output
pytest -v
```

### Code Quality
```bash
# Format code
black dbmanager/ tests/

# Lint code
flake8 dbmanager/ tests/

# Type checking
mypy dbmanager/
```

### Documentation
```bash
# Generate documentation
sphinx-build -b html docs/ docs/_build/

# Serve documentation
python -m http.server 8000 --directory docs/_build/
```

## Git Workflow

### Branch Strategy
- **main**: Production-ready code
- **develop**: Integration branch
- **feature/**: Feature development
- **bugfix/**: Bug fixes
- **hotfix/**: Critical fixes

### Commit Messages
Follow conventional commits:
```
feat: add new database adapter
fix: resolve connection pooling issue
docs: update API documentation
test: add integration tests
```

### Pull Request Process
1. Create feature branch
2. Implement changes
3. Add/update tests
4. Update documentation
5. Create pull request
6. Code review
7. Merge to develop

## Release Process
1. Update version in `pyproject.toml`
2. Update `CHANGELOG.md`
3. Create release branch
4. Final testing
5. Merge to main
6. Tag release
7. Publish to PyPI
