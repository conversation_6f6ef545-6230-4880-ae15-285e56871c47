# Sistema di Importazione CSV Multi-Livello

## Panoramica

Il sistema di importazione CSV è strutturato in 5 livelli per rispettare le dipendenze tra i modelli Django. Ogni livello deve essere completato prima di procedere al successivo per garantire l'integrità referenziale.

## Architettura a 5 Livelli

### Livello 1 - Modelli Base (Nessuna Dipendenza)

**Modelli:**
- `User` (Django built-in)
- `Group` (Django built-in)  
- `BasicAiModel` (thoth_core.models)
- `VectorDb` (thoth_core.models)

**Comandi:**
- `python manage.py import_users` ✅ (esistente)
- `python manage.py import_groups` ✅ (esistente)
- `python manage.py import_basicaimodels` ✅ (creato)
- `python manage.py import_vectordb` ✅ (creato)

**File CSV:**
- `exports/users.csv`
- `exports/groups.csv`
- `exports/basicaimodel.csv`
- `exports/vectordb.csv`

**Caratteristiche:**
- Nessuna dipendenza da altri modelli
- Possono essere importati in parallelo
- Base per tutti i livelli successivi

---

### Livello 2 - Dipendenze di Primo Livello

**Modelli:**
- `AiModel` (dipende da BasicAiModel)

**Comandi:**
- `python manage.py import_aimodels` ✅ (creato)

**File CSV:**
- `exports/aimodel.csv`

**Dipendenze:**
- `AiModel.basic_model` → `BasicAiModel`

---

### Livello 3 - Dipendenze di Secondo Livello

**Modelli:**
- `Agent` (dipende da AiModel)
- `Setting` (dipende da AiModel)

**Comandi:**
- `python manage.py import_agents` ✅ (creato)
- `python manage.py import_settings` ✅ (creato)

**File CSV:**
- `exports/agent.csv`
- `exports/setting.csv`

**Dipendenze:**
- `Agent.ai_model` → `AiModel` (opzionale)
- `Setting.comment_model` → `AiModel` (opzionale)

---

### Livello 4 - Dipendenze Complesse

**Modelli:**
- `Workspace` (dipende da SqlDb, Setting, AiModel, Agent, User)

**Comandi:**
- `python manage.py import_workspaces` ✅ (creato)

**File CSV:**
- `exports/workspace.csv`

**Dipendenze Multiple:**
- `Workspace.sql_db` → `SqlDb`
- `Workspace.setting` → `Setting`
- `Workspace.comment_ai_model` → `AiModel`
- `Workspace.plotly_generator_ai_model` → `AiModel`
- `Workspace.users` → `User` (M2M)
- `Workspace.default_workspace` → `User` (M2M)
- `Workspace.[vari]_agent` → `Agent` (multiple FK)

---

### Livello 5 - Struttura Database

**Modelli:**
- `SqlDb` (dipende da VectorDb)
- `SqlTable` (dipende da SqlDb)
- `SqlColumn` (dipende da SqlTable)
- `Relationship` (dipende da SqlColumn)

**Comandi:**
- `python manage.py import_db_structure` ✅ (esistente)

**File CSV:**
- `exports/selected_dbs.csv` (database principali)
- `exports/[database_name]_tables.csv` (tabelle per database)
- `exports/[database_name]_columns.csv` (colonne per database)
- `exports/[database_name]_relationships.csv` (relazioni per database)

**Dipendenze:**
- `SqlDb.vector_db` → `VectorDb` (opzionale)
- `SqlTable.sql_db` → `SqlDb`
- `SqlColumn.sql_table` → `SqlTable`
- `Relationship.source_column` → `SqlColumn`
- `Relationship.target_column` → `SqlColumn`

**Nota:** Questo comando gestisce automaticamente l'importazione di tutti i componenti della struttura database in un'unica operazione, rispettando le dipendenze interne.

---

## Comando Master

### `import_all_csv`

Esegue tutti i comandi di importazione nell'ordine corretto con gestione della cache.

**Utilizzo:**
```bash
# Importazione completa
python manage.py import_all_csv

# Importa solo un livello specifico
python manage.py import_all_csv --only-level 3

# Salta livelli specifici
python manage.py import_all_csv --skip-level 1 2

# Disabilita pulizia cache (più veloce ma potenzialmente problematico)
python manage.py import_all_csv --no-cache-clear
```

**Funzionalità:**
- Esecuzione sequenziale dei livelli
- Gestione automatica della cache
- Verifica dell'integrità al termine
- Report dettagliato con tempi di esecuzione
- Gestione degli errori con continuazione

---

## Gestione della Cache

### Problematiche della Cache

1. **ORM Cache**: Django mantiene cache degli oggetti in memoria
2. **Query Cache**: Cache delle query del database
3. **Model Cache**: Cache dei metadati dei modelli
4. **Connection Cache**: Cache delle connessioni al database

### Soluzioni Implementate

Il comando `import_all_csv` implementa una pulizia completa della cache tra ogni importazione:

```python
def clear_all_caches(self):
    # 1. Chiude tutte le connessioni DB
    from django.db import connections
    for conn in connections.all():
        conn.close()
    
    # 2. Pulisce cache Django
    from django.core.cache import cache
    cache.clear()
    
    # 3. Reset delle query
    from django.db import reset_queries
    reset_queries()
    
    # 4. Garbage collection
    import gc
    gc.collect()
```

### Benefici della Gestione Cache

- **Prevenzione errori di integrità referenziale**
- **Accesso ai dati più aggiornati**
- **Riduzione conflitti di concorrenza**
- **Stabilità del processo di importazione**

---

## Testing

### Test Suite Completa

Il file `thoth_core/tests_csv_import.py` contiene test completi per:

**Test Funzionali:**
- Importazione per ogni livello
- Verifica delle dipendenze
- Integrità referenziale
- Gestione errori
- File mancanti

**Test di Performance:**
- Tempi di esecuzione
- Utilizzo memoria
- Efficienza cache

**Esecuzione Test:**
```bash
# Test completi
python manage.py test thoth_core.tests_csv_import

# Test specifici
python manage.py test thoth_core.tests_csv_import.CSVImportTestCase.test_level_1_imports_base_models
```

---

## Esempi di Utilizzo

### Importazione Completa
```bash
# Importa tutti i CSV nell'ordine corretto
python manage.py import_all_csv
```

### Importazione Selettiva
```bash
# Solo modelli base
python manage.py import_all_csv --only-level 1

# Salta utenti e gruppi, importa il resto
python manage.py import_all_csv --skip-level 1
```

### Importazione Singola
```bash
# Importa solo BasicAiModel
python manage.py import_basicaimodels

# Importa solo Workspace (richiede dipendenze già importate)
python manage.py import_workspaces
```

### Debug e Troubleshooting
```bash
# Importazione senza pulizia cache (più veloce per debug)
python manage.py import_all_csv --no-cache-clear

# Verifica stato dopo importazione
python manage.py shell -c "
from thoth_core.models import *
print(f'BasicAiModels: {BasicAiModel.objects.count()}')
print(f'AiModels: {AiModel.objects.count()}')
print(f'Workspaces: {Workspace.objects.count()}')
"
```

---

## Risoluzione Problemi

### Errori Comuni

**1. Dipendenze Mancanti**
```
Error: BasicAiModel with ID 'X' not found for AiModel 'Y'
```
**Soluzione:** Importare prima il Livello 1

**2. File CSV Mancanti**
```
Error: CSV file not found at exports/model.csv
```
**Soluzione:** Verificare presenza file nella directory exports/

**3. Errori di Cache (Risolto)**
```
Error: Object does not exist (cache issue)
```
**Soluzione:** ✅ Risolto automaticamente con miglioramenti al sistema di cache

**4. Problemi Docker/Container**
```
BasicAiModel 'X' not found for AiModel 'Y'. Using 'Default Basic Model' instead.
```
**Soluzione:** ✅ Risolto con gestione migliorata delle transazioni e cache

### Best Practices

1. **Sempre usare `import_all_csv`** per importazioni complete
2. **Verificare file CSV** prima dell'importazione
3. **Backup database** prima di importazioni massive
4. **Monitorare log** per errori e warning
5. **Testare su ambiente di sviluppo** prima della produzione

---

## Struttura File

```
thoth_core/
├── management/
│   └── commands/
│       ├── import_all_csv.py          # Comando master
│       ├── import_basicaimodels.py    # Livello 1
│       ├── import_vectordb.py         # Livello 1
│       ├── import_aimodels.py         # Livello 2
│       ├── import_agents.py           # Livello 3
│       ├── import_settings.py         # Livello 3
│       ├── import_workspaces.py       # Livello 4
│       └── import_db_structure.py     # Livello 5 (esistente)
├── tests_csv_import.py                # Test suite
└── models.py                          # Definizioni modelli

exports/
├── users.csv                          # Livello 1
├── groups.csv                         # Livello 1
├── basicaimodel.csv                   # Livello 1
├── vectordb.csv                       # Livello 1
├── aimodel.csv                        # Livello 2
├── agent.csv                          # Livello 3
├── setting.csv                        # Livello 3
├── workspace.csv                      # Livello 4
├── selected_dbs.csv                   # Livello 5 (database principali)
├── [database_name]_tables.csv         # Livello 5 (tabelle per DB)
├── [database_name]_columns.csv        # Livello 5 (colonne per DB)
└── [database_name]_relationships.csv  # Livello 5 (relazioni per DB)
```

---
