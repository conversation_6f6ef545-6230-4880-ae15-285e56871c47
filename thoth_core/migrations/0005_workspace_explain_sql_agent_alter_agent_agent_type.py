# Generated by Django 5.2 on 2025-07-21 00:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('thoth_core', '0004_workspace_question_validator_alter_agent_agent_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='workspace',
            name='explain_sql_agent',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_explain_sql', to='thoth_core.agent'),
        ),
        migrations.AlterField(
            model_name='agent',
            name='agent_type',
            field=models.CharField(choices=[('GENERATESQLBASIC', 'Generate SQL Basic'), ('DEFAULT', 'Default'), ('EXTRACTKEYWORDS', 'Extract Keywords'), ('SELECTCOLUMNS', 'Select Columns'), ('SQLBASIC', 'Sql - Basic'), ('SQLADVANCED', 'Sql - Advanced'), ('SQLEXPERT', 'Sql - Expert'), ('SQLTITAN', 'Sql - Titan'), ('TESTGENERATOR', 'Test Generator'), ('TESTEXECUTOR', 'Test Executor'), ('EXPLAINSQL', 'Explain SQL'), ('ASKFORHUMANHELP', 'Ask For Human Help'), ('VALIDATEQUESTION', 'Validate Question')], default='EXTRACTKEYWORDS', max_length=255),
        ),
    ]
