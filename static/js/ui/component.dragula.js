(r=>{function a(){this.$body=r("body")}a.prototype.init=function(){r('[data-plugin="dragula"]').each(function(){var a=r(this).data("containers"),n=[];if(a)for(var t=0;t<a.length;t++)n.push(r("#"+a[t])[0]);else n=[r(this)[0]];var i=r(this).data("handleclass");i?dragula(n,{moves:function(a,n,t){return t.classList.contains(i)}}):dragula(n)})},r.<PERSON>=new a,r.Dragula.Constructor=a})(window.jQuery),window.jQuery.Dragula.init();