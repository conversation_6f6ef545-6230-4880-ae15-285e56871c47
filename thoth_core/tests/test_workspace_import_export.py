# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.test import TestCase
from django.contrib.admin.sites import AdminSite
from django.contrib.auth.models import User
from django.contrib.messages.storage.fallback import FallbackStorage
from django.http import HttpRequest
from unittest.mock import Mock, patch
import tempfile
import os
import csv

from thoth_core.models import Workspace, SqlDb, Agent, Setting, AiModel, BasicAiModel, VectorDb
from thoth_core.admin import WorkspaceAdmin
from thoth_core.utilities.utils import export_csv, import_csv, get_workspace_excluded_fields


class WorkspaceImportExportTestCase(TestCase):
    """Test case for workspace import/export functionality with excluded fields"""
    
    def setUp(self):
        """Set up test environment"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        
        # Create basic dependencies
        self.basic_ai_model = BasicAiModel.objects.create(
            name='Test Basic Model',
            provider='OpenAI',
            description='Test model'
        )
        
        self.vector_db = VectorDb.objects.create(
            name='Test Vector DB',
            vect_type='Qdrant',
            host='localhost',
            port=6333
        )
        
        self.sql_db = SqlDb.objects.create(
            name='Test SQL DB',
            db_type='SQLite',
            db_name='test.db',
            vector_db=self.vector_db
        )
        
        self.ai_model = AiModel.objects.create(
            basic_model=self.basic_ai_model,
            specific_model='gpt-3.5-turbo',
            name='Test AI Model'
        )
        
        self.agent = Agent.objects.create(
            name='Test Agent',
            ai_model=self.ai_model,
            agent_type='DEFAULT'
        )
        
        self.setting = Setting.objects.create(
            name='Test Setting'
        )
        
        # Create a test workspace with some status fields set
        self.workspace = Workspace.objects.create(
            name='Test Workspace',
            level=Workspace.WorkspaceLevel.BASIC,
            description='Test workspace description',
            sql_db=self.sql_db,
            default_agent=self.agent,
            explanation_ai_model=self.ai_model,
            plotly_generator_ai_model=self.ai_model,
            setting=self.setting,
            # Set some status fields that should be excluded
            preprocessing_status=Workspace.PreprocessingStatus.RUNNING,
            task_id='test-task-123',
            last_preprocess_log='Test log content',
            table_comment_status=Workspace.PreprocessingStatus.COMPLETED,
            table_comment_task_id='table-task-456',
            column_comment_status=Workspace.PreprocessingStatus.FAILED
        )
        
        # Add users to the workspace
        self.workspace.users.add(self.user)
        
        # Set up admin
        self.site = AdminSite()
        self.admin = WorkspaceAdmin(Workspace, self.site)
        
    def test_excluded_fields_function(self):
        """Test that the excluded fields function returns correct fields"""
        excluded_fields = get_workspace_excluded_fields()
        
        # Check that all expected categories are included
        expected_fields = {
            # Comment generation fields
            'table_comment_status', 'table_comment_task_id', 'table_comment_log',
            'table_comment_start_time', 'table_comment_end_time',
            'column_comment_status', 'column_comment_task_id', 'column_comment_log',
            'column_comment_start_time', 'column_comment_end_time',
            # Preprocessing fields
            'preprocessing_status', 'task_id', 'last_preprocess_log',
            'preprocessing_start_time', 'preprocessing_end_time',
            # DateTime fields
            'last_preprocess', 'last_hint_load', 'last_sql_loaded',
            'created_at', 'updated_at'
        }
        
        self.assertEqual(excluded_fields, expected_fields)
        
    @patch('thoth_core.utilities.utils.ensure_exports_directory')
    def test_export_csv_excludes_fields(self, mock_ensure_dir):
        """Test that export_csv excludes the correct fields for Workspace"""
        # Mock the directory function
        mock_ensure_dir.return_value = (tempfile.gettempdir(), None)
        
        # Create a mock request
        request = HttpRequest()
        request.user = self.user
        request.session = {}
        request._messages = Mock()
        
        # Create queryset
        queryset = Workspace.objects.filter(id=self.workspace.id)
        
        # Mock the HttpResponseRedirect to avoid actual redirect
        with patch('thoth_core.utilities.utils.HttpResponseRedirect') as mock_redirect:
            mock_redirect.return_value = Mock()
            
            # Execute export
            export_csv(self.admin, request, queryset)
            
            # Check that the CSV file was created
            csv_path = os.path.join(tempfile.gettempdir(), 'workspace.csv')
            self.assertTrue(os.path.exists(csv_path))
            
            # Read the CSV and check headers
            with open(csv_path, 'r') as f:
                reader = csv.reader(f)
                headers = next(reader)
                
                # Check that excluded fields are not in headers
                excluded_fields = get_workspace_excluded_fields()
                for field in excluded_fields:
                    self.assertNotIn(field, headers, f"Excluded field '{field}' found in CSV headers")
                
                # Check that important fields are still included
                important_fields = ['name', 'level', 'description', 'sql_db', 'setting']
                for field in important_fields:
                    self.assertIn(field, headers, f"Important field '{field}' missing from CSV headers")
            
            # Clean up
            if os.path.exists(csv_path):
                os.remove(csv_path)
                
    @patch('thoth_core.utilities.utils.ensure_exports_directory')
    @patch('thoth_core.utilities.utils.default_storage')
    @patch.object(WorkspaceAdmin, 'message_user')
    def test_import_csv_resets_excluded_fields(self, mock_message_user, mock_storage, mock_ensure_dir):
        """Test that import_csv resets excluded fields to default values"""
        # Mock the directory function
        mock_ensure_dir.return_value = (tempfile.gettempdir(), None)
        
        # Create a temporary CSV file with workspace data
        csv_content = [
            ['id', 'name', 'level', 'description', 'sql_db', 'setting', 'users'],
            ['999', 'Imported Workspace', 'ADVANCED', 'Imported description', str(self.sql_db.id), str(self.setting.id), str(self.user.id)]
        ]
        
        csv_path = os.path.join(tempfile.gettempdir(), 'workspace.csv')
        with open(csv_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(csv_content)
        
        # Mock storage to return our CSV file
        mock_file = open(csv_path, 'r')
        mock_storage.exists.return_value = True
        mock_storage.open.return_value = mock_file
        
        # Create a mock request
        request = HttpRequest()
        request.user = self.user
        
        # Create empty queryset (not used in import)
        queryset = Workspace.objects.none()
        
        try:
            # Execute import
            import_csv(self.admin, request, queryset)
            
            # Check that the workspace was created with correct values
            imported_workspace = Workspace.objects.get(id=999)
            
            # Verify basic fields were imported correctly
            self.assertEqual(imported_workspace.name, 'Imported Workspace')
            self.assertEqual(imported_workspace.level, 'ADVANCED')
            self.assertEqual(imported_workspace.description, 'Imported description')
            self.assertEqual(imported_workspace.sql_db, self.sql_db)
            self.assertEqual(imported_workspace.setting, self.setting)
            
            # Verify excluded fields were reset to default values
            self.assertEqual(imported_workspace.preprocessing_status, Workspace.PreprocessingStatus.IDLE)
            self.assertIsNone(imported_workspace.task_id)
            self.assertIsNone(imported_workspace.last_preprocess_log)
            self.assertEqual(imported_workspace.table_comment_status, Workspace.PreprocessingStatus.IDLE)
            self.assertIsNone(imported_workspace.table_comment_task_id)
            self.assertEqual(imported_workspace.column_comment_status, Workspace.PreprocessingStatus.IDLE)
            self.assertIsNone(imported_workspace.column_comment_task_id)
            self.assertIsNone(imported_workspace.last_preprocess)
            self.assertIsNone(imported_workspace.last_hint_load)
            self.assertIsNone(imported_workspace.last_sql_loaded)
            
            # Verify many-to-many relationships were set
            self.assertIn(self.user, imported_workspace.users.all())
            
        finally:
            # Clean up
            mock_file.close()
            if os.path.exists(csv_path):
                os.remove(csv_path)
            # Clean up created workspace
            try:
                Workspace.objects.get(id=999).delete()
            except Workspace.DoesNotExist:
                pass
