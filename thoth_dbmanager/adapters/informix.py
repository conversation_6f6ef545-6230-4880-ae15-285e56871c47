"""
Informix adapter for Thoth SQL Database Manager.
"""

from typing import Any, Dict, List, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

from ..core.interfaces import DbAdapter


class InformixAdapter(DbAdapter):
    """Informix database adapter."""
    
    def __init__(self, connection_string: str, **kwargs: Any) -> None:
        """
        Initialize Informix adapter.
        
        Args:
            connection_string: Informix connection string
            **kwargs: Additional connection parameters
        """
        self.connection_string = connection_string
        self.engine = None
        self.connection_params = kwargs
        
    def connect(self) -> None:
        """Establish database connection."""
        try:
            self.engine = create_engine(
                self.connection_string,
                pool_pre_ping=True,
                **self.connection_params
            )
        except Exception as e:
            raise ConnectionError(f"Failed to connect to Informix: {e}")
    
    def disconnect(self) -> None:
        """Close database connection."""
        if self.engine:
            self.engine.dispose()
            self.engine = None
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a query and return results."""
        if not self.engine:
            self.connect()
            
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                return [dict(row._mapping) for row in result]
        except SQLAlchemyError as e:
            raise RuntimeError(f"Informix query failed: {e}")
    
    def execute_update(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """Execute an update query and return affected row count."""
        if not self.engine:
            self.connect()
            
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                conn.commit()
                return result.rowcount
        except SQLAlchemyError as e:
            raise RuntimeError(f"Informix update failed: {e}")
    
    def get_tables(self) -> List[str]:
        """Get list of tables in the database."""
        query = """
        SELECT tabname as name
        FROM systables
        WHERE tabtype = 'T'
        AND tabid > 99
        ORDER BY tabname
        """
        result = self.execute_query(query)
        return [row['name'] for row in result]
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get schema information for a specific table."""
        query = f"""
        SELECT 
            c.colname as name,
            t.typename as type,
            CASE WHEN c.nulls = 'N' THEN 'NO' ELSE 'YES' END as nullable,
            c.default as default_value,
            CASE WHEN i.idxtype = 'U' THEN 1 ELSE 0 END as is_primary_key
        FROM syscolumns c
        JOIN systables t ON c.tabid = t.tabid
        JOIN systypes ty ON c.coltype = ty.typeid
        LEFT JOIN sysindexes i ON t.tabid = i.tabid AND c.colno = i.part1
        WHERE t.tabname = '{table_name.lower()}'
        ORDER BY c.colno
        """
        
        columns = self.execute_query(query)
        
        schema = {
            'table_name': table_name,
            'columns': []
        }
        
        for col in columns:
            schema['columns'].append({
                'name': col['name'],
                'type': col['type'],
                'nullable': col['nullable'] == 'YES',
                'default': col['default_value'],
                'primary_key': bool(col['is_primary_key'])
            })
        
        return schema
    
    def get_indexes(self, table_name: str) -> List[Dict[str, Any]]:
        """Get index information for a table."""
        query = f"""
        SELECT 
            i.idxname as name,
            c.colname as column_name,
            CASE WHEN i.idxtype = 'U' THEN 1 ELSE 0 END as unique_index,
            i.idxtype as index_type
        FROM sysindexes i
        JOIN syscolumns c ON i.tabid = c.tabid AND i.part1 = c.colno
        JOIN systables t ON i.tabid = t.tabid
        WHERE t.tabname = '{table_name.lower()}'
        ORDER BY i.idxname
        """
        
        return self.execute_query(query)
    
    def get_foreign_keys(self, table_name: str) -> List[Dict[str, Any]]:
        """Get foreign key information for a table."""
        query = f"""
        SELECT 
            c.constrname as name,
            c.colname as column_name,
            t2.tabname as referenced_table,
            c2.colname as referenced_column
        FROM sysconstraints c
        JOIN systables t ON c.tabid = t.tabid
        JOIN syscolumns c1 ON c.tabid = c1.tabid AND c.colno = c1.colno
        JOIN systables t2 ON c.ptabid = t2.tabid
        JOIN syscolumns c2 ON c.ptabid = c2.tabid AND c.pcolno = c2.colno
        WHERE t.tabname = '{table_name.lower()}'
        AND c.constrtype = 'R'
        """
        
        return self.execute_query(query)
    
    def create_table(self, table_name: str, schema: Dict[str, Any]) -> None:
        """Create a new table with the given schema."""
        columns = []
        for col in schema.get('columns', []):
            col_def = f"{col['name']} {col['type']}"
            if not col.get('nullable', True):
                col_def += " NOT NULL"
            if col.get('default') is not None:
                col_def += f" DEFAULT {col['default']}"
            if col.get('primary_key'):
                col_def += " PRIMARY KEY"
            columns.append(col_def)
        
        query = f"CREATE TABLE {table_name} ({', '.join(columns)})"
        self.execute_update(query)
    
    def drop_table(self, table_name: str) -> None:
        """Drop a table."""
        query = f"DROP TABLE {table_name}"
        self.execute_update(query)
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        query = f"""
        SELECT COUNT(*) as count
        FROM systables
        WHERE tabname = '{table_name.lower()}'
        AND tabtype = 'T'
        """
        result = self.execute_query(query)
        return result[0]['count'] > 0
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information."""
        return {
            'type': 'informix',
            'connection_string': self.connection_string,
            'connected': self.engine is not None
        }
