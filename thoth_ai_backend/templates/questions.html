{% extends "vertical_base.html" %}
{% load static %}

{% block title %}Products{% endblock title %}

{% block extra_css %}
<link href="{% static 'css/thoth/datatables.min.css' %}" rel="stylesheet"  type="text/css"/>
{#    <link href="{% static 'css/vendor/dataTables.bootstrap5.min.css' %}" rel="stylesheet" type="text/css" />#}
<link href="{% static 'css/vendor/responsive.bootstrap5.min.css' %}" rel="stylesheet" type="text/css" />

{% endblock %}

{% block page_title %}
{% include "partials/page-title.html" with page_title='Questions' sub_title='SQL Generation Context' %}
{% endblock %}

{% block content %}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-sm-5">
                        {% if user_is_editor %}
                        {# Link to the create question view #}
                        <a href="{% url 'thoth_ai_backend:create_question' %}" class="btn btn-danger mb-2"><i
                                class="mdi mdi-plus-circle me-2"></i> Add Question</a>
                        {% endif %}
                    </div>
                    <div class="col-sm-7">
                        <div class="text-sm-end">
                            {% if user_is_editor %}
                            <form method="POST" action="{% url 'thoth_ai_backend:delete_all_questions' %}" style="display: inline-block; margin-right: 5px;" onsubmit="return confirm('Are you sure you want to delete ALL questions? This action cannot be undone.');">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger mb-2"><i class="mdi mdi-delete-sweep me-2"></i> Delete All Questions</button>
                            </form>
                            <form method="POST" action="{% url 'thoth_ai_backend:import_questions_server_csv' %}" style="display: inline-block; margin-right: 5px;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-info mb-2">Import Server CSV</button>
                            </form>
                            {% endif %}
                            <a href="{% url 'thoth_ai_backend:export_questions_csv' %}" class="btn btn-light mb-2">Export CSV</a>
                        </div>
                    </div><!-- end col-->
                </div>

                <div class="table-responsive">
                    <table class="table table-centered w-100 dt-responsive nowrap table-striped" id="questions-datatable">
                        <thead>
                            <tr>
                                {# Removed checkbox column #}
                                <th>Question</th>
                                <th>SQL</th>
                                <th>Hint</th>
                                {% if user_is_editor %}
                                <th class="action-column" style="width: 45px;">Action</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {# Loop through the questions from the context #}
                            {% for question in questions %}
                            <tr>
                                {# Removed checkbox cell #}
                                <td>{{ question.question|truncatewords:20 }}</td> {# Truncate long questions #}
                                <td><pre><code>{{ question.sql|truncatechars:100 }}</code></pre></td> {# Display SQL in pre/code, truncate #}
                                <td>{{ question.hint|truncatewords:20 }}</td>
                                {% if user_is_editor %}
                                <td class="table-action">
                                    {# Link to update view #}
                                    <a href="{% url 'thoth_ai_backend:update_question' question_id=question.id %}" class="action-icon"> <i class="mdi mdi-square-edit-outline"></i></a>
                                    {# Link to delete confirmation view #}
                                    <a href="{% url 'thoth_ai_backend:confirm_delete_question' question_id=question.id %}" class="action-icon"> <i class="mdi mdi-delete"></i></a>
                                </td>
                                {% endif %}
                            </tr>
                            {% empty %}
                            <tr>
                                {# Adjusted colspan to 3 since we removed the checkbox column #}
                                <td>
                                    {% if error %}
                                    <div class="alert alert-error">
                                        {{ error }}
                                    </div>
                                    {% else %}
                                    No questions found.
                                    {% endif %}
                                </td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col -->
</div>
<!-- end row -->

{% endblock %}

{% block extra_javascript %}

<!-- Third party js -->
<script src = "{% static 'js/thoth/datatables.min.js' %}"></script>
    <!-- Removed dataTables.checkboxes.min.js as it's no longer needed -->
<!-- Third party js ends -->

<!-- Init js -->
<script src="{% static 'js/thoth/vdb.questions.js' %}"></script>
<!-- Init js end -->

{% endblock %}
