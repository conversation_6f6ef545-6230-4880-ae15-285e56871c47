(o=>{function e(){this.$body=o("body")}e.prototype.init=function(){Dropzone.autoDiscover=!1,o('[data-plugin="dropzone"]').each(function(){var e=o(this).attr("action"),i=o(this).data("previewsContainer"),e={url:e},i=(i&&(e.previewsContainer=i),o(this).data("uploadPreviewTemplate"));i&&(e.previewTemplate=o(i).html()),o(this).dropzone(e)})},o.FileUpload=new e,o.FileUpload.Constructor=e})(window.jQuery),window.jQuery.FileUpload.init();