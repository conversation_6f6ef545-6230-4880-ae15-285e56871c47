DB_NAME_LOCAL="db.sqlite3"

SECRET_KEY=your_api_key_here (mandatory)
DEBUG=True
PROFILE='Dev'
IO_DIR='exports'

# API Keys and URLs for various services. At least one is mandatory
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_BASE=https://api.deepseek.com/v1

OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_API_BASE=https://openrouter.ai/api/v1

OLLAMA_API_BASE=http://127.0.0.1:11434

DJANGO_API_KEY=your_django_api_key_here (mandatory)
DJANGO_SERVER="http://localhost:8000"
DJANGO_ADMIN_URL="http://localhost:8000/admin/"

DB_ROOT_PATH="data"

TOKENIZERS_PARALLELISM=false
LOGFIRE_TOKEN=your_logfire_api_key_here (mandatory)