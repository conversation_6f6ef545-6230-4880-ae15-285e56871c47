# Database Support Matrix

## Supported Databases

| Database | Status | Manager Class | Adapter | Plugin | Test Coverage |
|----------|--------|---------------|---------|--------|---------------|
| SQLite | ✅ Complete | ThothSqliteManager | ✅ | ✅ | ✅ |
| PostgreSQL | ✅ Complete | ThothPgManager | ✅ | ✅ | ✅ |
| MySQL | ✅ Complete | ThothMySqlManager | ✅ | ❌ | ✅ |
| MariaDB | ✅ Complete | ThothMariaDbManager | ✅ | ❌ | ✅ |
| Oracle | ✅ Complete | ThothOracleManager | ✅ | ❌ | ✅ |
| SQL Server | ✅ Complete | ThothSqlServerManager | ✅ | ❌ | ✅ |
| Informix | ✅ Complete | ThothInformixManager | ✅ | ❌ | ✅ |

## Feature Support by Database

### Core Features
- **Connection Management**: All databases
- **Query Execution**: All databases
- **Transaction Support**: All databases
- **Schema Operations**: All databases

### LSH Features
- **Similarity Search**: All databases
- **MinHash Generation**: All databases
- **Index Management**: All databases

### Advanced Features
- **Bulk Operations**: All databases
- **Prepared Statements**: All databases
- **Connection Pooling**: PostgreSQL, MySQL, MariaDB

## Database-Specific Notes

### SQLite
- File-based database
- No network configuration required
- Best for development/testing

### PostgreSQL
- Full feature support
- Advanced indexing capabilities
- Production-ready

### MySQL/MariaDB
- Similar implementation
- Shared connection logic
- Good performance characteristics

### Oracle
- Requires cx_Oracle driver
- Complex connection strings
- Enterprise features supported

### SQL Server
- Requires pyodbc driver
- Windows authentication support
- Azure compatibility

### Informix
- Requires informixdb driver
- Legacy system support
- Specialized configuration
