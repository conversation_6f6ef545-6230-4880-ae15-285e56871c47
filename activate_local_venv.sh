#!/bin/bash
# <PERSON><PERSON>t to properly activate the local virtual environment
# This ensures pip installs packages in the correct location

# Deactivate any existing virtual environment
if [ -n "$VIRTUAL_ENV" ]; then
    echo "Deactivating current virtual environment: $VIRTUAL_ENV"
    deactivate 2>/dev/null || true
fi

# Clean up environment variables
unset VIRTUAL_ENV
unset PYTHONPATH

# Remove old virtual environment paths from PATH
export PATH=$(echo $PATH | tr ':' '\n' | grep -v "/Users/<USER>/PythonProjects/thoth_sqldb/.venv" | tr '\n' ':' | sed 's/:$//')

# Activate the local virtual environment
if [ -f ".venv/bin/activate" ]; then
    echo "Activating local virtual environment..."
    source .venv/bin/activate
    
    # Force correct environment variables
    export VIRTUAL_ENV="/Users/<USER>/PythonProjects/thoth_sqldb2/.venv"
    export PATH="/Users/<USER>/PythonProjects/thoth_sqldb2/.venv/bin:$PATH"
    
    # Reinstall pip to fix any shebang issues
    echo "Fixing pip installation..."
    python3 -m pip install --force-reinstall pip > /dev/null 2>&1
    
    echo "Virtual environment activated successfully!"
    echo "Python: $(which python3)"
    echo "Pip: $(which pip)"
    echo "Virtual Environment: $VIRTUAL_ENV"
else
    echo "Error: .venv/bin/activate not found in current directory"
    exit 1
fi
