#!/bin/sh

# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

set -e

echo "=== Starting Thoth Application ==="

# Wait for database to be ready
echo "Waiting for database to be ready..."
python -c "
import os
import time
import psycopg2
from psycopg2 import OperationalError

max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        conn = psycopg2.connect(
            host=os.environ.get('DB_HOST', 'db'),
            port=os.environ.get('DB_PORT', '5432'),
            user=os.environ.get('DB_USER', 'thoth_user'),
            password=os.environ.get('DB_PASSWORD', 'thoth_password'),
            database=os.environ.get('DB_NAME', 'postgres')
        )
        conn.close()
        print('Database is ready!')
        break
    except OperationalError:
        retry_count += 1
        print(f'Database not ready, retrying... ({retry_count}/{max_retries})')
        time.sleep(2)
else:
    print('Failed to connect to database after maximum retries')
    exit(1)
"

echo "Running makemigrations..."
python manage.py makemigrations

echo "Running migrate..."
python manage.py migrate

# Check migration status for debugging
echo "Checking migration status..."
python manage.py showmigrations thoth_core

echo "Running collectstatic..."
python manage.py collectstatic --noinput
echo "Collectstatic finished."

echo "Startup script completed successfully."
