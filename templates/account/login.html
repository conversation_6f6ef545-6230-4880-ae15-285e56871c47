{% extends "account/base.html" %}
{% load static i18n crispy_forms_tags %}

{% block title %}{% trans "Log In" %}{% endblock title %}

{% block body_extra %}
class="authentication-bg position-relative"
{% endblock %}

{% block page_content %}
<div class="position-absolute start-0 end-0 start-0 bottom-0 w-100 h-100">
    <svg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 800 800'>
        <g fill-opacity='0.22'>
            <circle style="fill: rgba(var(--ct-primary-rgb), 0.1);" cx='400' cy='400' r='600' />
            <circle style="fill: rgba(var(--ct-primary-rgb), 0.2);" cx='400' cy='400' r='500' />
            <circle style="fill: rgba(var(--ct-primary-rgb), 0.3);" cx='400' cy='400' r='300' />
            <circle style="fill: rgba(var(--ct-primary-rgb), 0.4);" cx='400' cy='400' r='200' />
            <circle style="fill: rgba(var(--ct-primary-rgb), 0.5);" cx='400' cy='400' r='100' />
        </g>
    </svg>
</div>
<div class="account-pages pt-2 pt-sm-5 pb-4 pb-sm-5 position-relative">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xxl-4 col-lg-5">
                <div class="card">

                    <!-- Logo -->
                    <div class="card-header py-4 text-center bg-primary">
                        <a href="{% url 'index' %}">
                            <span><img src="{% static 'images/logo.png' %}" alt="logo" height="22"></span>
                        </a>
                    </div>

                    <div class="card-body p-4">

                        <div class="text-center w-75 m-auto">
                            <h4 class="text-dark-50 text-center pb-0 fw-bold">Sign In</h4>
                            <p class="text-muted mb-4">Enter your email address and password to access admin
                                panel.</p>
                        </div>

                        <form method="POST" action="{% url 'account_login' %}" novalidate>
                            {% csrf_token %}
                            
                            <div class="mb-3">
                                <label for="id_login" class="form-label">{{ form.login.label }}</label>
                                <input type="text" name="login" placeholder="Username or e-mail" class="form-control" required id="id_login">
                                {% if form.login.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.login.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="id_password" class="form-label">{{ form.password.label }}</label>
                                <input type="password" name="password" placeholder="Password" class="form-control" required id="id_password">
                                {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}{{ error }}{% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" name="remember" id="id_remember" class="form-check-input">
                                <label for="id_remember" class="form-check-label">{% trans "Remember Me" %}</label>
                            </div>
                            
                            {% if redirect_field_value %}
                            <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
                            {% endif %}

                            <!--div class="mt-3 mb-2 mb-0 text-center">
                                <a href="{% url 'account_reset_password' %}" class="text-muted mt-1"><small>Forgot
                                        your
                                        password?</small></a>
                            </div -->

                            <div class="mb-3 mb-0 text-center">
                                <button class="btn btn-primary" type="submit"> Log In</button>
                            </div>

                        </form>
                    </div> <!-- end card-body -->
                </div>
                <!-- end card -->
            <!-- end row -->
            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>
    <!-- end container -->
</div>
<!-- end page -->

<footer class="footer footer-alt">
    2024 -
    <script>document.write(new Date().getFullYear())</script>
      ©TylConsulting.it
</footer>
{% endblock %}

{% block extra_javascript %}
<script>
    document.getElementById('id_login').value = "<EMAIL>"
    document.getElementById('id_password').value = "password"
</script>
{% endblock %}
