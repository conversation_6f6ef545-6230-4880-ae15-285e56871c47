# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from rest_framework.permissions import BasePermission
from django.conf import settings

class HasValidApiKey(BasePermission):
    """
    Custom permission to check if the request has a valid API key.
    """
    def has_permission(self, request, view):
        api_key = request.headers.get('X-API-KEY')
        if not api_key:
            return False
        return api_key == settings.API_KEY