# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.conf import settings

class ApiKeyAuthentication(BaseAuthentication):
    """
    Custom authentication class that authenticates requests based on API key.
    """
    def authenticate(self, request):
        api_key = request.headers.get('X-API-KEY')
        if not api_key:
            return None

        if api_key != settings.API_KEY:
            raise AuthenticationFailed('Invalid API key')

        # Ritorna una tupla (user, auth) dove user è None perché 
        # l'autenticazione è basata solo su API key
        return (None, True)
