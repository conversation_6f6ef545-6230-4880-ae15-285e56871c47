"""
Main interactive CLI for LSH similarity search evaluation.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config_loader import Config<PERSON>oader
from keyword_extractor import KeywordExtractor
from lsh_evaluator import LshEvaluator
from results_formatter import ResultsFormatter


class LshEvaluationTool:
    """Main CLI tool for LSH evaluation."""
    
    def __init__(self):
        """Initialize the evaluation tool."""
        self.config = ConfigLoader()
        self.formatter = ResultsFormatter()
        
        # Validate configuration
        if not self.config.validate():
            print("❌ Configuration validation failed. Please check config.yml")
            sys.exit(1)
        
        # Initialize components
        self.keyword_extractor = KeywordExtractor(
            api_key=self.config.get('OPENROUTER_API_KEY'),
            api_base=self.config.get('OPENROUTER_API_BASE')
        )
        
        lsh_path = self.config.get_lsh_path()
        self.lsh_evaluator = LshEvaluator(lsh_path)
        
        # Test API connection
        self.api_available = self.keyword_extractor.test_connection()
        if not self.api_available:
            print("⚠️  Warning: OpenRouter API not available. Using fallback keyword extraction.")
    
    def run(self):
        """Run the interactive CLI."""
        self._print_welcome()
        
        # Show database info
        db_info = self.lsh_evaluator.get_database_info()
        print(self.formatter.format_database_info(db_info))
        print()
        
        while True:
            try:
                question = input("🔍 Enter your question (or /exit to quit): ").strip()
                
                if question.lower() == '/exit':
                    print("👋 Goodbye!")
                    break
                
                if not question:
                    print("❌ Please enter a valid question.")
                    continue
                
                # Extract keywords
                if self.api_available:
                    print("🤖 Extracting keywords using Gemini...")
                    keywords = self.keyword_extractor.extract_keywords(question)
                else:
                    print("📝 Using fallback keyword extraction...")
                    keywords = self.keyword_extractor._fallback_extraction(question)
                
                print(f"🔑 Keywords extracted: {', '.join(keywords)}")
                print()
                
                # Evaluate keywords
                print("🔍 Searching LSH index...")
                results = self.lsh_evaluator.evaluate_keywords(keywords, self.config.config)
                
                # Display results
                print(self.formatter.format_results(results))
                
                # Ask for export
                export_choice = input("\n💾 Export results to JSON? (y/n): ").strip().lower()
                if export_choice == 'y':
                    filename = self.formatter.export_json(results)
                    print(f"✅ Results exported to {filename}")
                
                print("\n" + "=" * 80 + "\n")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                print("Please try again.\n")
    
    def _print_welcome(self):
        """Print welcome message."""
        print("\n" + "=" * 80)
        print("🎯 LSH SIMILARITY SEARCH EVALUATION TOOL")
        print("=" * 80)
        print()
        print("This tool helps you evaluate the efficacy of LSH similarity search")
        print("by asking questions and finding similar database columns and values.")
        print()
        print("Commands:")
        print("  - Type your question in natural language")
        print("  - Type '/exit' to quit")
        print()
        print("Example questions:")
        print("  - 'Find columns related to student performance'")
        print("  - 'What tables contain information about school demographics?'")
        print("  - 'Show me columns with test scores or grades'")
        print()


def main():
    """Main entry point."""
    try:
        tool = LshEvaluationTool()
        tool.run()
    except Exception as e:
        print(f"❌ Failed to start tool: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
