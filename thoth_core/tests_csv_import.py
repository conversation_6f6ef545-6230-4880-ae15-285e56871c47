# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.test import TestCase
from django.core.management import call_command
from django.contrib.auth.models import User, Group
from django.db import transaction
from thoth_core.models import (
    BasicAiModel, VectorDb, AiModel, SqlDb, Agent, Setting, 
    Workspace, SqlColumn, SqlTable, Relationship
)
import os
from django.conf import settings
import tempfile
import csv
from io import StringIO
import sys


class CSVImportTestCase(TestCase):
    """Test case for CSV import commands with dependency management"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_data_dir = os.path.join(settings.BASE_DIR, 'exports')
        self.original_stdout = sys.stdout
        
    def tearDown(self):
        """Clean up after tests"""
        sys.stdout = self.original_stdout
    
    def capture_command_output(self, command_name, *args, **kwargs):
        """Capture command output for testing"""
        captured_output = StringIO()
        sys.stdout = captured_output
        try:
            call_command(command_name, *args, **kwargs)
            output = captured_output.getvalue()
        finally:
            sys.stdout = self.original_stdout
        return output
    
    def test_level_1_imports_base_models(self):
        """Test Level 1: Base models with no dependencies"""
        print("\n=== Testing Level 1: Base Models ===")
        
        # Test import_users (existing command)
        if os.path.exists(os.path.join(self.test_data_dir, 'users.csv')):
            initial_count = User.objects.count()
            output = self.capture_command_output('import_users')
            final_count = User.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ Users: {final_count - initial_count} imported")
        
        # Test import_groups (existing command)
        if os.path.exists(os.path.join(self.test_data_dir, 'groups.csv')):
            initial_count = Group.objects.count()
            output = self.capture_command_output('import_groups')
            final_count = Group.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ Groups: {final_count - initial_count} imported")
        
        # Test import_basicaimodels
        if os.path.exists(os.path.join(self.test_data_dir, 'basicaimodel.csv')):
            initial_count = BasicAiModel.objects.count()
            output = self.capture_command_output('import_basicaimodels')
            final_count = BasicAiModel.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ BasicAiModels: {final_count - initial_count} imported")
        
        # Test import_vectordb
        if os.path.exists(os.path.join(self.test_data_dir, 'vectordb.csv')):
            initial_count = VectorDb.objects.count()
            output = self.capture_command_output('import_vectordb')
            final_count = VectorDb.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ VectorDbs: {final_count - initial_count} imported")
    
    def test_level_2_imports_first_dependencies(self):
        """Test Level 2: First level dependencies"""
        print("\n=== Testing Level 2: First Level Dependencies ===")
        
        # Ensure Level 1 is imported first
        self.test_level_1_imports_base_models()
        
        # Test import_aimodels (depends on BasicAiModel)
        if os.path.exists(os.path.join(self.test_data_dir, 'aimodel.csv')):
            initial_count = AiModel.objects.count()
            output = self.capture_command_output('import_aimodels')
            final_count = AiModel.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ AiModels: {final_count - initial_count} imported")
            
            # Verify dependencies
            for ai_model in AiModel.objects.all():
                self.assertIsNotNone(ai_model.basic_model)
    
    def test_level_3_imports_second_dependencies(self):
        """Test Level 3: Second level dependencies"""
        print("\n=== Testing Level 3: Second Level Dependencies ===")
        
        # Ensure previous levels are imported
        self.test_level_2_imports_first_dependencies()
        
        # Test import_agents (depends on AiModel)
        if os.path.exists(os.path.join(self.test_data_dir, 'agent.csv')):
            initial_count = Agent.objects.count()
            output = self.capture_command_output('import_agents')
            final_count = Agent.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ Agents: {final_count - initial_count} imported")
        
        # Test import_settings (depends on AiModel)
        if os.path.exists(os.path.join(self.test_data_dir, 'setting.csv')):
            initial_count = Setting.objects.count()
            output = self.capture_command_output('import_settings')
            final_count = Setting.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ Settings: {final_count - initial_count} imported")
    
    def test_level_4_imports_complex_dependencies(self):
        """Test Level 4: Complex dependencies"""
        print("\n=== Testing Level 4: Complex Dependencies ===")
        
        # Ensure previous levels are imported
        self.test_level_3_imports_second_dependencies()
        
        # Test import_workspaces (depends on multiple models)
        if os.path.exists(os.path.join(self.test_data_dir, 'workspace.csv')):
            initial_count = Workspace.objects.count()
            output = self.capture_command_output('import_workspaces')
            final_count = Workspace.objects.count()
            
            self.assertGreaterEqual(final_count, initial_count)
            print(f"✓ Workspaces: {final_count - initial_count} imported")
    
    def test_level_5_imports_database_structure(self):
        """Test Level 5: Database structure"""
        print("\n=== Testing Level 5: Database Structure ===")
        
        # Ensure previous levels are imported
        self.test_level_4_imports_complex_dependencies()
        
        # Test import_db_structure (imports SqlDb, SqlTable, SqlColumn, Relationship)
        if os.path.exists(os.path.join(self.test_data_dir, 'selected_dbs.csv')):
            initial_sqldb_count = SqlDb.objects.count()
            initial_sqlcolumn_count = SqlColumn.objects.count()
            initial_relationship_count = Relationship.objects.count()
            
            output = self.capture_command_output('import_db_structure')
            
            final_sqldb_count = SqlDb.objects.count()
            final_sqlcolumn_count = SqlColumn.objects.count()
            final_relationship_count = Relationship.objects.count()
            
            self.assertGreaterEqual(final_sqldb_count, initial_sqldb_count)
            self.assertGreaterEqual(final_sqlcolumn_count, initial_sqlcolumn_count)
            self.assertGreaterEqual(final_relationship_count, initial_relationship_count)
            
            print(f"✓ SqlDbs: {final_sqldb_count - initial_sqldb_count} imported")
            print(f"✓ SqlColumns: {final_sqlcolumn_count - initial_sqlcolumn_count} imported")
            print(f"✓ Relationships: {final_relationship_count - initial_relationship_count} imported")
    
    def test_complete_import_sequence(self):
        """Test the complete import sequence using import_all_csv"""
        print("\n=== Testing Complete Import Sequence ===")
        
        # Get initial counts
        initial_counts = {
            'users': User.objects.count(),
            'groups': Group.objects.count(),
            'basicaimodels': BasicAiModel.objects.count(),
            'vectordbs': VectorDb.objects.count(),
            'aimodels': AiModel.objects.count(),
            'sqldbs': SqlDb.objects.count(),
            'agents': Agent.objects.count(),
            'settings': Setting.objects.count(),
            'workspaces': Workspace.objects.count(),
            'sqlcolumns': SqlColumn.objects.count(),
            'relationships': Relationship.objects.count(),
        }
        
        # Run complete import
        output = self.capture_command_output('import_all_csv')
        
        # Get final counts
        final_counts = {
            'users': User.objects.count(),
            'groups': Group.objects.count(),
            'basicaimodels': BasicAiModel.objects.count(),
            'vectordbs': VectorDb.objects.count(),
            'aimodels': AiModel.objects.count(),
            'sqldbs': SqlDb.objects.count(),
            'agents': Agent.objects.count(),
            'settings': Setting.objects.count(),
            'workspaces': Workspace.objects.count(),
            'sqlcolumns': SqlColumn.objects.count(),
            'relationships': Relationship.objects.count(),
        }
        
        # Verify imports
        for model_name, initial_count in initial_counts.items():
            final_count = final_counts[model_name]
            imported_count = final_count - initial_count
            print(f"✓ {model_name.capitalize()}: {imported_count} imported (total: {final_count})")
        
        # Verify that the output indicates success
        self.assertIn('completed successfully', output.lower())
    
    def test_dependency_integrity(self):
        """Test that all dependencies are properly maintained"""
        print("\n=== Testing Dependency Integrity ===")
        
        # Run complete import first
        self.test_complete_import_sequence()
        
        # Check AiModel -> BasicAiModel dependencies
        ai_models_without_basic = AiModel.objects.filter(basic_model__isnull=True)
        self.assertEqual(ai_models_without_basic.count(), 0, 
                        "All AiModels should have a BasicAiModel")
        
        # Check SqlDb -> VectorDb dependencies (optional, so just verify no broken references)
        for sqldb in SqlDb.objects.filter(vector_db__isnull=False):
            self.assertIsNotNone(sqldb.vector_db)
        
        # Check Agent -> AiModel dependencies (optional)
        for agent in Agent.objects.filter(ai_model__isnull=False):
            self.assertIsNotNone(agent.ai_model)
        
        # Check Setting -> AiModel dependencies (optional)
        for setting in Setting.objects.filter(comment_model__isnull=False):
            self.assertIsNotNone(setting.comment_model)
        
        # Check Workspace dependencies
        for workspace in Workspace.objects.all():
            if workspace.sql_db:
                self.assertIsNotNone(workspace.sql_db)
            if workspace.setting:
                self.assertIsNotNone(workspace.setting)
        
        # Check SqlColumn -> SqlTable dependencies
        for column in SqlColumn.objects.all():
            self.assertIsNotNone(column.sql_table)
            self.assertIsNotNone(column.sql_table.sql_db)
        
        # Check Relationship dependencies
        for relationship in Relationship.objects.all():
            self.assertIsNotNone(relationship.source_table)
            self.assertIsNotNone(relationship.target_table)
            self.assertIsNotNone(relationship.source_column)
            self.assertIsNotNone(relationship.target_column)
        
        print("✓ All dependency integrity checks passed")
    
    def test_import_with_missing_files(self):
        """Test behavior when CSV files are missing"""
        print("\n=== Testing Import with Missing Files ===")
        
        # Test individual commands with missing files
        with tempfile.TemporaryDirectory() as temp_dir:
            # Temporarily change the exports directory
            original_base_dir = settings.BASE_DIR
            settings.BASE_DIR = temp_dir
            
            try:
                # This should handle missing files gracefully
                output = self.capture_command_output('import_basicaimodels')
                self.assertIn('not found', output)
                
            finally:
                settings.BASE_DIR = original_base_dir
        
        print("✓ Missing file handling test passed")
    
    def test_import_error_handling(self):
        """Test error handling during imports"""
        print("\n=== Testing Error Handling ===")
        
        # Create a CSV with invalid data
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
            writer = csv.writer(temp_file)
            writer.writerow(['name', 'description', 'provider'])
            writer.writerow(['Test Model', 'Test Description', 'INVALID_PROVIDER'])
            temp_file_path = temp_file.name
        
        try:
            # This should handle invalid data gracefully
            # Note: This is a simplified test - in practice you'd need to mock the file path
            print("✓ Error handling test structure verified")
            
        finally:
            os.unlink(temp_file_path)
    
    def test_cache_clearing(self):
        """Test that cache clearing works properly"""
        print("\n=== Testing Cache Clearing ===")
        
        # Run import with cache clearing enabled
        output = self.capture_command_output('import_all_csv')
        
        # Verify cache clearing messages appear
        self.assertIn('Cache cleared', output)
        print("✓ Cache clearing test passed")
    
    def test_selective_level_import(self):
        """Test importing specific levels only"""
        print("\n=== Testing Selective Level Import ===")
        
        # Test importing only level 1
        output = self.capture_command_output('import_all_csv', only_level=1)
        
        # Verify only level 1 was processed
        self.assertIn('LEVEL 1:', output)
        self.assertNotIn('LEVEL 2:', output)
        print("✓ Selective level import test passed")


class CSVImportPerformanceTestCase(TestCase):
    """Performance tests for CSV imports"""
    
    def test_import_performance(self):
        """Test that imports complete within reasonable time"""
        import time
        
        start_time = time.time()
        
        # Run a basic import
        call_command('import_basicaimodels')
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within 30 seconds for reasonable data sizes
        self.assertLess(duration, 30, "Import should complete within 30 seconds")
        print(f"✓ Performance test passed: {duration:.2f}s")


if __name__ == '__main__':
    # Run tests when executed directly
    import django
    from django.test.utils import get_runner
    from django.conf import settings
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["thoth_core.tests_csv_import"])
