"""
LSH evaluator for similarity search.
"""

import sys
import logging
import pickle
from pathlib import Path
from typing import Dict, Any, List
from collections import defaultdict
import time

# Add parent directory to path to import dbmanager
sys.path.insert(0, str(Path(__file__).parent.parent))


def load_db_lsh_flexible(lsh_path: Path, db_name: str):
    """Load LSH data with flexible file naming."""
    # Try different naming patterns
    lsh_patterns = [
        f"{db_name}_lsh.pkl",
        f"{db_name}_lsh_index.pkl"
    ]
    
    minhash_patterns = [
        f"{db_name}_minhashes.pkl",
        f"{db_name}_lsh_minhashes.pkl",
        f"{db_name}_minhash.pkl"
    ]
    
    lsh_file = None
    minhash_file = None
    
    # Find LSH file
    for pattern in lsh_patterns:
        candidate = lsh_path / pattern
        if candidate.exists():
            lsh_file = candidate
            break
    
    # Find minhash file
    for pattern in minhash_patterns:
        candidate = lsh_path / pattern
        if candidate.exists():
            minhash_file = candidate
            break
    
    if not lsh_file:
        raise FileNotFoundError(f"LSH file not found. Tried: {lsh_patterns}")
    
    if not minhash_file:
        raise FileNotFoundError(f"Minhash file not found. Tried: {minhash_patterns}")
    
    print(f"Loading LSH from: {lsh_file.name}")
    print(f"Loading minhashes from: {minhash_file.name}")
    
    with open(lsh_file, 'rb') as f:
        lsh = pickle.load(f)
    
    with open(minhash_file, 'rb') as f:
        minhashes = pickle.load(f)
    
    return lsh, minhashes


def _query_lsh_simple(lsh, minhashes, keyword, signature_size=30, n_gram=3, top_n=10):
    """Simple LSH query function."""
    from datasketch import MinHash
    
    # Create minhash for the keyword
    query_minhash = MinHash(num_perm=signature_size)
    for i in range(len(keyword) - n_gram + 1):
        query_minhash.update(keyword[i:i+n_gram].encode('utf8'))
    
    # Query LSH
    results = lsh.query(query_minhash)
    
    # Organize results by table and column
    organized_results = defaultdict(lambda: defaultdict(list))
    
    for result_key in results[:top_n]:
        if result_key in minhashes:
            minhash_obj, table_name, column_name, value = minhashes[result_key]
            organized_results[table_name][column_name].append(value)
    
    return dict(organized_results)


class LshEvaluator:
    """Evaluates LSH similarity search queries."""
    
    def __init__(self, lsh_path: Path):
        """Initialize the LSH evaluator."""
        self.lsh_path = Path(lsh_path)
        self.logger = logging.getLogger(__name__)
        
        # Get database name from path
        db_name = self.lsh_path.parent.name
        
        try:
            # Try to import from dbmanager first
            try:
                from dbmanager.helpers.search import _query_lsh, load_db_lsh
                self.lsh, self.minhashes = load_db_lsh(str(self.lsh_path.parent))
                self.query_func = _query_lsh
                print(f"✅ Using dbmanager LSH functions")
            except (ImportError, TypeError):
                # Fallback to our flexible loader
                self.lsh, self.minhashes = load_db_lsh_flexible(self.lsh_path, db_name)
                self.query_func = _query_lsh_simple
                print(f"✅ Using fallback LSH functions")
            
            print(f"✅ LSH data loaded successfully")
            print(f"   LSH index loaded: {self.lsh is not None}")
            print(f"   Minhashes loaded: {len(self.minhashes) if self.minhashes else 0} entries")
            
        except Exception as e:
            print(f"❌ Failed to load LSH data: {e}")
            raise
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get database information."""
        try:
            return {
                'database_path': str(self.lsh_path),
                'database_name': self.lsh_path.parent.name,
                'lsh_loaded': hasattr(self, 'lsh') and self.lsh is not None,
                'minhashes_count': len(self.minhashes) if hasattr(self, 'minhashes') and self.minhashes else 0,
                'status': 'available'
            }
        except Exception as e:
            return {
                'database_path': str(self.lsh_path) if hasattr(self, 'lsh_path') else 'unknown',
                'database_name': 'unknown',
                'lsh_loaded': False,
                'minhashes_count': 0,
                'status': 'error',
                'error': str(e)
            }
    
    def evaluate_keywords(self, keywords: List[str], config: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate multiple keywords and return combined results."""
        start_time = time.time()
        
        all_results = {}
        unique_tables = set()
        unique_columns = set()
        total_matches = 0
        
        for keyword in keywords:
            keyword_results = self._query_keyword(keyword, config)
            all_results[keyword] = keyword_results
            
            # Collect metadata
            for table_name, table_data in keyword_results.items():
                unique_tables.add(table_name)
                for column_name, values in table_data.items():
                    unique_columns.add(f"{table_name}.{column_name}")
                    total_matches += len(values)
        
        query_time = round(time.time() - start_time, 3)
        
        return {
            'keywords': keywords,
            'results': all_results,
            'metadata': {
                'query_time': query_time,
                'total_matches': total_matches,
                'unique_tables': list(unique_tables),
                'unique_columns': list(unique_columns)
            }
        }
    
    def _query_keyword(self, keyword: str, config: Dict[str, Any]) -> Dict[str, Dict[str, List[str]]]:
        """Query a single keyword."""
        try:
            signature_size = int(config.get('LSH_SIGNATURE_SIZE', 30))
            n_gram = int(config.get('LSH_N_GRAM', 3))
            top_n = int(config.get('LSH_TOP_N', 10))
            
            matches = self.query_func(
                self.lsh,
                self.minhashes,
                keyword,
                signature_size=signature_size,
                n_gram=n_gram,
                top_n=top_n
            )
            
            return matches
            
        except Exception as e:
            self.logger.error(f"Error querying keyword '{keyword}': {e}")
            return {}
