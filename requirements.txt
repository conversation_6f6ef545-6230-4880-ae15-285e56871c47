accelerate==1.6.0
annotated-types==0.7.0
anthropic==0.49.0
anthropic-haystack==2.4.0
anyio==4.9.0
argcomplete==3.6.2
asgiref==3.8.1
attrs==25.3.0
backoff==2.2.1
boto3==1.37.33
botocore==1.37.33
cachetools==5.5.2
certifi==2025.1.31
chardet==5.2.0
charset-normalizer==3.4.1
click==8.1.8
cohere==5.14.2
colorama==0.4.6
coloredlogs==15.0.1
construct==2.5.3
crispy-bootstrap5==2025.4
datasketch==1.6.5
Deprecated==1.2.18
distro==1.9.0
Django==5.2
django-allauth==65.7.0
django-browser-reload==1.18.0
django-crispy-forms==2.3
djangorestframework==3.16.0
eval_type_backport==0.2.2
executing==2.2.0
fastavro==1.10.0
fastembed==0.6.0
fastembed-haystack==1.4.1
filelock==3.18.0
flatbuffers==25.2.10
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2025.3.2
google-ai-generativelanguage==0.6.15
google-ai-haystack==5.1.0
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.4
googleapis-common-protos==1.69.2
griffe==1.7.2
groq==0.22.0
grpcio==1.71.0
grpcio-status==1.71.0
grpcio-tools==1.71.0
gunicorn==23.0.0
h11==0.14.0
h2==4.2.0
haystack==0.42
haystack-ai==2.12.2
haystack-experimental==0.9.0
hf-xet==1.1.5
hpack==4.1.0
httpcore==1.0.8
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.1
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
iniconfig==2.1.0
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
lazy_imports==0.4.0
logfire==3.14.0
logfire-api==3.14.0
loguru==0.7.3
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mcp==1.6.0
mdurl==0.1.2
mistral-haystack==0.1.1
mistralai==1.6.0
mmh3==5.1.0
monotonic==1.6
more-itertools==10.6.0
mpmath==1.3.0
networkx==3.4.2
numpy==2.2.4
ollama==0.4.7
ollama-haystack==2.4.0
onnxruntime==1.21.0
openai==1.75.0
opentelemetry-api==1.32.0
opentelemetry-exporter-otlp-proto-common==1.32.0
opentelemetry-exporter-otlp-proto-http==1.32.0
opentelemetry-instrumentation==0.53b0
opentelemetry-proto==1.32.0
opentelemetry-sdk==1.32.0
opentelemetry-semantic-conventions==0.53b0
packaging==24.2
pandas==2.2.3
pathlib==1.0.1
pefile==2024.8.26
pillow==11.1.0
pluggy==1.6.0
portalocker==2.10.1
posthog==3.25.0
prompt_toolkit==3.0.50
proto-plus==1.26.1
protobuf==5.29.4
psutil==7.0.0
psycopg2-binary==2.9.10
py_rust_stemmers==0.1.5
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.3
pydantic-ai==0.0.55
pydantic-ai-slim==0.0.55
pydantic-evals==0.0.55
pydantic-graph==0.0.55
pydantic-settings==2.8.1
pydantic_core==2.33.1
Pygments==2.19.1
pyparsing==3.2.3
pytest==8.4.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-ptrace==0.9.9
pytz==2025.2
PyYAML==6.0.2
qdrant-client==1.13.3
qdrant-haystack==9.1.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==14.0.0
rpds-py==0.24.0
rsa==4.9
s3transfer==0.11.4
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==4.0.2
setuptools==78.1.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.40
sqlparse==0.5.3
sse-starlette==2.2.1
starlette==0.46.2
sympy==1.13.1
tabulate==0.9.0
tenacity==9.1.2
thoth-sqldb==0.3.13
thoth-vdb==0.1.8
threadpoolctl==3.6.0
tokenizers==0.21.1
torch==2.6.0
tqdm==4.67.1
transformers==4.51.0
types-requests==2.32.0.20250328
typing==*******
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.4.0
uvicorn==0.34.1
wcwidth==0.2.13
wrapt==1.17.2
zipp==3.21.0
