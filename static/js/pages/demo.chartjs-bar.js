function hexToRGB(a,t){var e=parseInt(a.slice(1,3),16),o=parseInt(a.slice(3,5),16),a=parseInt(a.slice(5,7),16);return t?"rgba("+e+", "+o+", "+a+", "+t+")":"rgb("+e+", "+o+", "+a+")"}(e=>{function a(){this.$body=e("body"),this.charts=[],this.defaultColors=["#727cf5","#0acf97","#fa5c7c","#ffbc00"]}a.prototype.borderRadiusExample=function(){var a=document.getElementById("border-radius-example"),t=a.getAttribute("data-colors"),t=t?t.split(","):this.defaultColors,a=a.getContext("2d"),a=new Chart(a,{type:"bar",data:{labels:["Jan","Feb","March","April","May","June"],datasets:[{label:"Fully Rounded",data:[12,-19,14,-15,12,-14],borderColor:t[0],backgroundColor:hexToRGB(t[0],.3),borderWidth:2,borderRadius:Number.MAX_VALUE,borderSkipped:!1},{label:"Small Radius",data:[-10,19,-15,-8,12,-7],backgroundColor:hexToRGB(t[1],.3),borderColor:t[1],borderWidth:2,borderRadius:5,borderSkipped:!1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1,position:"top"}},scales:{x:{grid:{display:!1}},y:{grid:{display:!1}}}}});this.charts.push(a)},a.prototype.floatingBarExample=function(){var a=document.getElementById("floating-example"),t=a.getAttribute("data-colors"),t=t?t.split(","):this.defaultColors,a=a.getContext("2d"),a=new Chart(a,{type:"bar",data:{labels:["Jan","Feb","March","April","May","June"],datasets:[{label:"Fully Rounded",data:[12,-19,14,-15,12,-14],backgroundColor:t[0]},{label:"Small Radius",data:[-10,19,-15,-8,12,-7],backgroundColor:t[1]}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1,position:"top"}},scales:{x:{grid:{display:!1}},y:{grid:{display:!1}}}}});this.charts.push(a)},a.prototype.horizontalExample=function(){var a=document.getElementById("horizontal-example"),t=a.getAttribute("data-colors"),t=t?t.split(","):this.defaultColors,a=a.getContext("2d"),a=new Chart(a,{type:"bar",data:{labels:["Jan","Feb","March","April"],datasets:[{label:"Fully Rounded",data:[12,-19,14,-15],borderColor:t[0],backgroundColor:hexToRGB(t[0],.3),borderWidth:1},{label:"Small Radius",data:[-10,19,-15,-8],backgroundColor:hexToRGB(t[1],.3),borderColor:t[1],borderWidth:1}]},options:{indexAxis:"y",responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1,position:"top"}},scales:{x:{grid:{display:!1}},y:{grid:{display:!1}}}}});this.charts.push(a)},a.prototype.stackedExample=function(){var a=document.getElementById("stacked-example"),t=a.getAttribute("data-colors"),t=t?t.split(","):this.defaultColors,a=a.getContext("2d"),a=new Chart(a,{type:"bar",data:{labels:["Jan","Feb","March","April"],datasets:[{label:"Dataset 1",data:[12,-19,14,-15],backgroundColor:t[0]},{label:"Dataset 2",data:[-10,19,-15,-8],backgroundColor:t[1]},{label:"Dataset 3",data:[-10,19,-15,-8],backgroundColor:t[2]}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1,position:"top"}},scales:{x:{stacked:!0,grid:{display:!1}},y:{stacked:!0,grid:{display:!1}}}}});this.charts.push(a)},a.prototype.groupStackedExample=function(){var a=document.getElementById("group-stack-example"),t=a.getAttribute("data-colors"),t=t?t.split(","):this.defaultColors,a=a.getContext("2d"),a=new Chart(a,{type:"bar",data:{labels:["Jan","Feb","March","April"],datasets:[{label:"Dataset 1",data:[12,-19,14,-15],backgroundColor:t[0],stack:"Stack 0"},{label:"Dataset 2",data:[-10,19,-15,-8],backgroundColor:t[1],stack:"Stack 0"},{label:"Dataset 3",data:[-10,19,-15,-8],backgroundColor:t[2],stack:"Stack 1"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1,position:"top"}},scales:{x:{stacked:!0,grid:{display:!1}},y:{stacked:!0,grid:{display:!1}}}}});this.charts.push(a)},a.prototype.verticalExample=function(){var a=document.getElementById("vertical-example"),t=a.getAttribute("data-colors"),t=t?t.split(","):this.defaultColors,a=a.getContext("2d"),a=new Chart(a,{type:"bar",data:{labels:["Jan","Feb","March","April"],datasets:[{label:"Dataset 1",data:[12,-19,14,-15],backgroundColor:t[0]},{label:"Dataset 2",data:[-10,19,-15,-8],backgroundColor:t[1]}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1,position:"top"}},scales:{x:{grid:{display:!1}},y:{grid:{display:!1}}}}});this.charts.push(a)},a.prototype.init=function(){var t=this;Chart.defaults.font.family='-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',Chart.defaults.color="#8391a2",Chart.defaults.borderColor="rgba(133, 141, 152, 0.1)",this.borderRadiusExample(),this.floatingBarExample(),this.horizontalExample(),this.stackedExample(),this.groupStackedExample(),this.verticalExample(),e(window).on("resizeEnd",function(a){e.each(t.charts,function(a,t){try{t.destroy()}catch(a){}}),t.borderRadiusExample(),t.floatingBarExample(),t.horizontalExample(),t.stackedExample(),t.groupStackedExample(),t.verticalExample()}),e(window).resize(function(){this.resizeTO&&clearTimeout(this.resizeTO),this.resizeTO=setTimeout(function(){e(this).trigger("resizeEnd")},500)})},e.ChartJs=new a,e.ChartJs.Constructor=a})(window.jQuery),window.jQuery.ChartJs.init();