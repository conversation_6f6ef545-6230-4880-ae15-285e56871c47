{% extends "vertical_base.html" %}
{% load static %}

{% block title %}Thoth - Home{% endblock title %}

{% block extra_css %}
      <link href="{% static 'css/vendor/responsive.bootstrap5.min.css' %}" rel="stylesheet" type="text/css" />
      <style>
        .feature-card {
            height: 100%;
            transition: transform 0.3s ease;
            border-radius: 8px;
            overflow: hidden;
            /* background-color will be inherited or set by theme-specific CSS */
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .thoth-container {
            display: flex;
            margin-bottom: 20px;
        }
        .thoth-image-container {
            flex: 0 0 360px; /* Reduced by 10% from 400px */
            margin-left: 60px;
        }
        .thoth-image {
            max-width: 100%;
            max-height: 540px; /* Reduced by 10% from 600px */
            object-fit: contain;
        }
        .thoth-description {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            /* background-color will be inherited or set by theme-specific CSS */
        }
        .section-title {
            position: relative;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .section-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--ct-primary);
        }
        .card {
            margin-bottom: 0.5rem;
            /* background-color will be inherited or set by theme-specific CSS */
        }
        .card-body {
            padding: 1rem;
        }
        p {
            margin-bottom: 0.5rem;
        }
        .features-container {
            flex: 1 1 auto;
        }
      </style>
{% endblock %}

{% block page_title %}
    {% include "partials/page-title.html" with page_title='Welcome to Thoth' sub_title='Your SQL Natural Language Interface' %}
{% endblock %}

{% block content %}
<!-- Main Content -->
<div class="thoth-container">
    <!-- Thoth Image at the left -->
    <div class="thoth-image-container">
        <img src="{% static 'images/dio-thoth-low.png' %}" alt="Thoth - Egyptian God" class="thoth-image">
    </div>
    
    <!-- Content at the right of the image -->
    <div class="features-container">
        <!-- Thoth Description Box -->
        <div class="thoth-description">
            <h2>Thoth - The Egyptian God of Wisdom</h2>
            <p>In ancient Egyptian mythology, Thoth was the god of wisdom, writing, hieroglyphs, science, magic, art, and judgment. He was depicted as a man with the head of an ibis or a baboon, animals sacred to him.</p>
            <p>As the scribe of the gods, Thoth was credited with the invention of writing and Egyptian hieroglyphs. He was considered the heart and tongue of Ra, the sun god, as well as the means by which Ra's will was translated into speech.</p>
        </div>
        
        <!-- Application Features -->
        <div class="card feature-card mb-2">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="mdi mdi-database-search text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h4 class="card-title">Natural Language Queries</h4>
                        <p class="card-text">Ask questions in plain English or any other language and get SQL queries and results without writing a single line of code.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card feature-card mb-2">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="mdi mdi-chart-bar text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h4 class="card-title">Data Visualization</h4>
                        <p class="card-text">Automatically generate insightful visualizations from your query results to understand your data better.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card feature-card mb-2">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="mdi mdi-database-check text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h4 class="card-title">Vectorial and Relational Database Management</h4>
                        <p class="card-text">Define parameters, create metadata, and manage your database structure with ease.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Getting Started Section -->
<div class="row" id="how-it-works">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2 mb-md-0 text-center">
                        <div class="rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border: 2px solid var(--ct-primary);">
                            <h3 class="m-0">1</h3>
                        </div>
                        <h5 class="mt-2">Preprocess the Database</h5>
                        <p>Prepares your database for natural language querying through the creation of LSH signatures (Locality-Sensitive Hashing) and the generation of contextual vectors that are stored in the vector database to improve semantic understanding.</p>
                    </div>
                    <div class="col-md-3 mb-2 mb-md-0 text-center">
                        <div class="rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border: 2px solid var(--ct-primary);">
                            <h3 class="m-0">2</h3>
                        </div>
                        <h5 class="mt-2">Create Columns Descriptions</h5>
                        <p>Using column names, available descriptions, and field contents, ask the AI to create meaningful column descriptions to help further the SQL generation process.</p>
                    </div>
                    <div class="col-md-3 mb-2 mb-md-0 text-center">
                        <div class="rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border: 2px solid var(--ct-primary);">
                            <h3 class="m-0">3</h3>
                        </div>
                        <h5 class="mt-2">Define Hints and Examples</h5>
                        <p>Explain how to interpret terms, idioms, and values representing complex concepts. Describe the data model and store any known good question-SQL pair.
                        </p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border: 2px solid var(--ct-primary);">
                            <h3 class="m-0">4</h3>
                        </div>
                        <h5 class="mt-2">Get Results</h5>
                        <p>Write a question to be answered through a query to your database and receive from Thoth the data you requested and the SQL used for the query performed.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
