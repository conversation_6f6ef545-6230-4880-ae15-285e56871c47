# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager
from django.shortcuts import render, redirect, get_object_or_404 # Added get_object_or_404
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin # Added for CBV authentication
from django.contrib.auth.decorators import login_required # Added for FBV authentication
from vdbmanager.ThothVectorStore import SqlDocument, ColumnNameDocument, HintDocument
from vdbmanager.impl.QdrantHaystackStore import QdrantHaystackStore

# Import the custom decorators for Editor group permission checking
from .decorators import require_editor_group, require_editor_group_with_redirect

# Import SqlDocument along with others
from .forms import HintForm, SqlDocumentForm, ColumnForm # Import the new forms
# Import Http404 for error handling if hint not found
from django.http import Http404, HttpResponseRedirect, HttpResponse
# Import decorators for restricting HTTP methods
from django.views.decorators.http import require_http_methods, require_POST
# Import reverse for redirecting
from django.urls import reverse
# Import messages framework (optional, for user feedback)
from django.contrib import messages
# Import vector store utility
from .backend_utils.vector_store_utils import (
    get_vector_store,
    export_hints_to_csv_file, export_columns_to_csv_file, export_questions_to_csv_file,
    import_hints_from_csv_file, import_columns_from_csv_file, import_questions_from_csv_file,
    delete_all_hints_from_vector_store, delete_all_columns_from_vector_store, delete_all_questions_from_vector_store
)
from .backend_utils.session_utils import get_current_workspace
import os
import logging
# Removed QdrantHaystackStore import as it's not directly used for type checking in the new view

from thoth_core.models import SqlDb, Setting, Workspace
from thoth_ai_backend.backend_utils.vectordb_config_utils import get_vectordb_config
from thoth_ai_backend.preprocessing.preprocess import preprocess
from thoth_ai_backend.preprocessing.upload_hints import upload_hints_to_vectordb
from thoth_ai_backend.preprocessing.upload_questions import upload_questions_to_vectordb
from .preprocessing.update_database_columns_direct import update_database_columns_description
from .async_tasks import run_preprocessing_task
import threading
from django.http import JsonResponse

logger = logging.getLogger(__name__)

class QuestionsView(LoginRequiredMixin, TemplateView):
    template_name = 'questions.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            # Try to get the vector store for the current workspace
            try:
                vector_store = get_vector_store(self.request)
                sql_documents = vector_store.get_all_sql_documents()

                # Transform SQL documents into a list of dictionaries for the template
                questions = []
                for doc in sql_documents:
                    if isinstance(doc, SqlDocument):
                        questions.append({
                            'id': getattr(doc, 'id', None), # Include id if available and needed
                            'question': doc.question,
                            'sql': doc.sql,
                            'hint': doc.hint
                        })
                context['questions'] = questions
                
            except ValueError as e:
                # Log the error
                logger.error(f"Error reading Vector Store: {e}", exc_info=True)
                # Add error message to context
                context['error'] = f"Error reading Vectore Store: {e}"
                context['questions'] = []
        except Exception as e:
            # Log the error
            logger.error(f"Error fetching questions: {e}", exc_info=True)
            # Add error message to context
            context['error'] = f"Error fetching questions: {e}"
            context['questions'] = []
            
        return context
class ColumnsView(LoginRequiredMixin, TemplateView):
    template_name = 'columns.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            # Try to get the vector store for the current workspace
            try:
                vector_store = get_vector_store(self.request)
                
                workspace = get_current_workspace(self.request)
                
                # Get columns from the vector store
                column_documents = vector_store.get_all_column_documents()
                
                # Transform documents into a list of dictionaries for the template
                columns = []
                for doc in column_documents:
                    if isinstance(doc, ColumnNameDocument):
                        # Ensure the ID attribute from the vector store document is included
                        columns.append({
                            'id': doc.id, # Access the id attribute directly
                            'table_name': doc.table_name,
                            'original_column_name': doc.original_column_name,
                            'column_description': doc.column_description,
                            'value_description': doc.value_description
                            # Add other fields if needed for the list template (columns.html)
                        })
                
                context['columns'] = columns
                
            except ValueError as e:
                # If no workspace found in session, use hardcoded collection as fallback
                
                # Use hardcoded collection name for fallback
                hardcoded_store = QdrantHaystackStore(
                    collection="california_qdrant",
                    host="localhost",
                    port=6333
                )
                
                # Get columns from hardcoded store
                hardcoded_columns = hardcoded_store.get_all_column_documents()
                
                # Transform documents into a list of dictionaries for the template
                hardcoded_results = []
                for doc in hardcoded_columns:
                    if isinstance(doc, ColumnNameDocument):
                        hardcoded_results.append({
                            'id': doc.id,
                            'table_name': doc.table_name,
                            'original_column_name': doc.original_column_name,
                            'column_description': doc.column_description,
                            'value_description': doc.value_description
                        })
                
                context['columns'] = hardcoded_results
        except Exception as e:
            # Log the error
            logger.error(f"Error fetching columns: {e}", exc_info=True)
            # Add error message to context
            context['error'] = f"Error fetching columns: {e}"
            context['columns'] = []
            
        return context

class HintsView(LoginRequiredMixin, TemplateView):
    template_name = 'hints.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            # Try to get the vector store for the current workspace
            try:
                vector_store = get_vector_store(self.request)
                
                workspace = get_current_workspace(self.request)
                
                hint_documents = vector_store.get_all_hint_documents()

                # Transform hint documents into a list of dictionaries for the template
                hints = []
                for doc in hint_documents:
                    if isinstance(doc, HintDocument):
                        hints.append({
                            'id': doc.id,  # Include id in case it's needed later
                            'hint': doc.hint
                        })

                context['hints'] = hints
                
            except ValueError as e:
                # If no workspace found in session, use hardcoded collection as fallback
                
                # Use hardcoded collection name for fallback
                hardcoded_store = QdrantHaystackStore(
                    collection="california_qdrant",
                    host="localhost",
                    port=6333
                )
                
                # Get hints from hardcoded store
                hardcoded_hints = hardcoded_store.get_all_hint_documents()
                
                # Transform documents into a list of dictionaries for the template
                hardcoded_results = []
                for doc in hardcoded_hints:
                    if isinstance(doc, HintDocument):
                        hardcoded_results.append({
                            'id': doc.id,
                            'hint': doc.hint
                        })
                
                context['hints'] = hardcoded_results
        except Exception as e:
            # Log the error
            logger.error(f"Error fetching hints: {e}", exc_info=True)
            # Add error message to context
            context['error'] = f"Error fetching hints: {e}"
            context['hints'] = []
            
        return context

class PreprocessView(LoginRequiredMixin, TemplateView):
    template_name = 'preprocess.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            # Try to get the vector store for the current workspace
            try:
                vector_store = get_vector_store(self.request) 
                workspace = get_current_workspace(self.request)
                db = workspace.sql_db
                
                # Add data to context
                context['vector_store'] = vector_store
                context['workspace'] = workspace
                context['db'] = db
                
                # Add additional information that might be useful for the template
                context['db_name'] = db.db_name if db else "No database found"
                context['workspace_name'] = workspace.name if workspace else "No workspace found"
                context['vector_store_type'] = type(vector_store).__name__ if vector_store else "No vector store found"
                context['last_preprocess'] = workspace.last_preprocess if workspace else None
                context['last_hint_load'] = workspace.last_hint_load if workspace else None
                context['last_sql_loaded'] = workspace.last_sql_loaded if workspace else None
                
                # Add collection name if available
                if hasattr(vector_store, 'collection'):
                    context['collection_name'] = vector_store.collection
                else:
                    context['collection_name'] = "Unknown"
                
            except ValueError as e:
                logger.error(f"Error fetching workspace: {e}", exc_info=True)
                # Add error message to context
                context['error'] = f"Error fetching workspace data: {e}"
                
        except Exception as e:
            # Log the error
            logger.error(f"Error in PreprocessView: {e}", exc_info=True)
            # Add error message to context
            context['error'] = f"Error in preprocessing view: {e}"
            
        return context

@login_required
def view_columns(request, column_id):
    """
    Displays the details of a specific ColumnNameDocument from the vector store.
    """
    try:
        vector_store = get_vector_store(request)
        column = vector_store.get_columns_document_by_id(column_id)
        
        if not column or not isinstance(column, ColumnNameDocument):
            raise Http404("Column document not found in vector store.")
        
        # Create a form instance with the column data (for display only)
        form = ColumnForm(initial={
            'table_name': column.table_name,
            'original_column_name': column.original_column_name,
            'column_name': getattr(column, 'column_name', ''),
            'column_description': column.column_description,
            'value_description': column.value_description,
        })
        
        # Make the form read-only since this is a view page
        for field in form.fields.values():
            field.widget.attrs['readonly'] = True
            field.widget.attrs['disabled'] = 'disabled'
        
        context = {
            'column': column,
            'form': form,
        }
        
        return render(request, 'view_columns.html', context)

    except Exception as e:
        logger.error(f"Error fetching column document {column_id}: {e}", exc_info=True)
        messages.error(request, f"Error accessing vector database or finding column: {e}")
        # Redirect to the main columns list view on error
        return redirect('thoth_ai_backend:columns')

# CRUD Views for HintDocument
@login_required
@require_editor_group
def manage_hint(request, hint_id=None):
    """
    View to handle creating a new hint or updating an existing one.
    """
    try:
        # Get the vector store for the current workspace
        vector_store = get_vector_store(request)
        instance = None
        if hint_id:
            # Update case: Try to fetch the existing hint document directly by ID
            try:
                # Use the specific method to get the hint document by its ID
                instance = vector_store.get_hint_document_by_id(hint_id)
                if not instance:
                    logger.warning(f"Hint with ID {hint_id} not found.")
                    raise Http404("Hint not found")
                # Ensure the fetched document is actually a HintDocument (optional, depends on method guarantees)
                if not isinstance(instance, HintDocument):
                    logger.error(f"Document found for ID {hint_id} is not a HintDocument.")
                    raise Http404("Invalid document type found for hint ID")
            except Http404: # Re-raise Http404 specifically if caught below
                 raise
            except Exception as e:
                # Catch other potential errors during fetch (e.g., connection issues)
                logger.error(f"Error fetching hint {hint_id}: {e}", exc_info=True)
                raise Http404("Error fetching hint") # Raise Http404 for consistency on failure
    except Exception as e:
        logger.error(f"Error getting vector store: {e}", exc_info=True)
        messages.error(request, f"Error accessing vector database: {e}")
        return redirect('thoth_ai_backend:hints')

    if request.method == 'POST':
        form = HintForm(request.POST) # Pass initial data for update if needed, but POST overrides
        if form.is_valid():
            hint_text = form.cleaned_data['text']
            try:
                if instance: # Update existing hint
                    # Update the hint text
                    instance.hint = hint_text
                    # Assuming an update method exists in the vector store
                    # vector_store.update_hint(instance)
                    # If update_hint doesn't exist, we might need delete + add
                    # This depends heavily on the vector store implementation.
                    # Let's assume delete + add for now if update isn't directly supported
                    vector_store.delete_document(instance.id) # Assuming delete_hint exists
                    vector_store.add_hint(HintDocument(id=instance.id, hint=hint_text)) # Re-add with same ID if possible, or let it generate new
                    logger.info(f"Hint {instance.id} updated.")
                else: # Create new hint
                    new_hint_doc = HintDocument(hint=hint_text)
                    vector_store.add_hint(new_hint_doc)
                    logger.info(f"New hint created.")

                return redirect('thoth_ai_backend:hints') # Redirect after success

            except Exception as e:
                # Log the error e
                logger.error(f"Error saving hint (ID: {hint_id}): {e}", exc_info=True)
                # Add error to form or context if needed
                form.add_error(None, f"An error occurred while saving the hint. {e}") # Generic error

    else: # GET request
        if instance: # Populate form for update
            form = HintForm(initial={'text': instance.hint})
        else: # Empty form for create
            form = HintForm()

    # Render the template for GET or POST with errors
    # We'll rename 'add_hint.html' to 'manage_hint.html' later
    context = {
        'form': form,
        'hint_id': hint_id # Pass hint_id to template to adjust UI (e.g., button text)
    }
    return render(request, 'manage_hint.html', context) # Use a new template name

@login_required
@require_editor_group
@require_http_methods(["GET"])
def confirm_delete_hint(request, hint_id):
    """
    Displays a confirmation page before deleting a hint.
    """
    try:
        # Get the vector store for the current workspace
        vector_store = get_vector_store(request)
        try:
            hint = vector_store.get_hint_document_by_id(hint_id)
            if not hint:
                raise Http404("Hint not found")
            if not isinstance(hint, HintDocument):
                 raise Http404("Invalid document type found for hint ID")

        except Exception as e:
            logger.error(f"Error fetching hint {hint_id} for deletion confirmation: {e}", exc_info=True)
            messages.error(request, "Error finding the hint to delete.")
            return redirect('thoth_ai_backend:hints')

        context = {'hint': hint}
        return render(request, 'confirm_delete_hint.html', context)
    except Exception as e:
        logger.error(f"Error getting vector store: {e}", exc_info=True)
        messages.error(request, f"Error accessing vector database: {e}")
        return redirect('thoth_ai_backend:hints')

@login_required
@require_editor_group
@require_http_methods(["POST"])
def delete_hint_confirmed(request, hint_id):
    """
    Handles the actual deletion of a hint after confirmation.
    """
    try:
        # Get the vector store for the current workspace
        vector_store = get_vector_store(request)
        try:
            # Optional: Verify hint exists before attempting delete, though delete_document might handle not found gracefully
            # hint = vector_store.get_hint_document_by_id(hint_id)
            # if not hint:
            #     raise Http404("Hint not found")

            vector_store.delete_document(hint_id)
            messages.success(request, f"Hint successfully deleted.") # Optional user feedback
            logger.info(f"Hint {hint_id} deleted successfully.") # Logging

        except Exception as e:
            logger.error(f"Error deleting hint {hint_id}: {e}", exc_info=True)
            messages.error(request, "An error occurred while deleting the hint.") # Optional user feedback
    except Exception as e:
        logger.error(f"Error getting vector store: {e}", exc_info=True)
        messages.error(request, f"Error accessing vector database: {e}")

    # Redirect back to the hints list regardless of success/error for simplicity here
    # In a real app, you might handle errors differently
    return HttpResponseRedirect(reverse('thoth_ai_backend:hints'))

# CRUD Views for SqlDocument (Questions)

@login_required
@require_editor_group
def manage_question(request, question_id=None):
    """
    View to handle creating a new question or updating an existing one.
    """
    try:
        # Get the vector store for the current workspace
        vector_store = get_vector_store(request)
        instance = None
        if question_id:
            # Update case: Try to fetch the existing question document by ID
            try:
                instance = vector_store.get_sql_document_by_id(question_id)
                if not instance:
                    logger.warning(f"SqlDocument with ID {question_id} not found.")
                    raise Http404("Question not found")
                if not isinstance(instance, SqlDocument):
                    logger.error(f"Document found for ID {question_id} is not a SqlDocument.")
                    raise Http404("Invalid document type found for question ID")
            except Http404:
                 raise
            except Exception as e:
                logger.error(f"Error fetching question {question_id}: {e}", exc_info=True)
                raise Http404("Error fetching question")
    except Exception as e:
        logger.error(f"Error getting vector store: {e}", exc_info=True)
        messages.error(request, f"Error accessing vector database: {e}")
        return redirect('thoth_ai_backend:questions')

    if request.method == 'POST':
        form = SqlDocumentForm(request.POST)
        if form.is_valid():
            question_text = form.cleaned_data['question']
            sql_query = form.cleaned_data['sql']
            hint_text = form.cleaned_data['hint']
            try:
                if instance: # Update existing question
                    instance.question = question_text
                    instance.sql = sql_query
                    instance.hint = hint_text
                    # Use explicit delete + add pattern, mirroring manage_hint/manage_doc
                    vector_store.delete_document(instance.id)
                    vector_store.add_sql(SqlDocument(id=instance.id, question=question_text, sql=sql_query, hint=hint_text))
                    messages.success(request, f"Question updated successfully.")
                    logger.info(f"Question {instance.id} updated.")
                else: # Create new question
                    new_question = SqlDocument(question=question_text, sql=sql_query, hint=hint_text)
                    vector_store.add_sql(new_question)
                    messages.success(request, f"Question created successfully.")
                    logger.info(f"New question created.")

                return redirect('thoth_ai_backend:questions') # Redirect to the questions list view

            except Exception as e:
                logger.error(f"Error saving question (ID: {question_id}): {e}", exc_info=True)
                messages.error(request, f"An error occurred while saving the question: {e}")
                form.add_error(None, f"An error occurred while saving the question. {e}")

    else: # GET request
        if instance: # Populate form for update
            form = SqlDocumentForm(initial={'question': instance.question, 'sql': instance.sql, 'hint': instance.hint})
        else: # Empty form for create
            form = SqlDocumentForm()

    context = {
        'form': form,
        'question_id': question_id # Pass question_id to template
    }
    # Need to create 'manage_question.html' template
    return render(request, 'manage_question.html', context)


@login_required
@require_editor_group
@require_http_methods(["GET"])
def confirm_delete_question(request, question_id):
    """
    Displays a confirmation page before deleting a question.
    """
    try:
        # Get the vector store for the current workspace
        vector_store = get_vector_store(request)
        try:
            question = vector_store.get_sql_document_by_id(question_id)
            if not question:
                raise Http404("Question not found")
            if not isinstance(question, SqlDocument):
                 raise Http404("Invalid document type found for question ID")

        except Exception as e:
            logger.error(f"Error fetching question {question_id} for deletion confirmation: {e}", exc_info=True)
            messages.error(request, "Error finding the question to delete.")
            return redirect('thoth_ai_backend:questions') # Redirect back if error

        context = {'question': question}
        # Need to create 'confirm_delete_question.html' template
        return render(request, 'confirm_delete_question.html', context)
    except Exception as e:
        logger.error(f"Error getting vector store: {e}", exc_info=True)
        messages.error(request, f"Error accessing vector database: {e}")
        return redirect('thoth_ai_backend:questions')

@login_required
@require_editor_group
@require_http_methods(["POST"])
def delete_question_confirmed(request, question_id):
    """
    Handles the actual deletion of a question after confirmation.
    """
    try:
        # Get the vector store for the current workspace
        vector_store = get_vector_store(request)
        try:
            vector_store.delete_document(question_id) # Assuming generic delete works
            messages.success(request, f"Question successfully deleted.")
            logger.info(f"Question {question_id} deleted successfully.") # Logging

        except Exception as e:
            logger.error(f"Error deleting question {question_id}: {e}", exc_info=True)
            messages.error(request, "An error occurred while deleting the question.")
    except Exception as e:
        logger.error(f"Error getting vector store: {e}", exc_info=True)
        messages.error(request, f"Error accessing vector database: {e}")

    return HttpResponseRedirect(reverse('thoth_ai_backend:questions')) # Redirect to questions list

@login_required
@require_http_methods(["POST"])
def run_preprocessing(request, workspace_id):
    """
    Starts the preprocessing task in a background thread and returns a polling template.
    """
    workspace = get_object_or_404(Workspace, id=workspace_id)

    # Prevent starting a new task if one is already running
    if workspace.preprocessing_status == Workspace.PreprocessingStatus.RUNNING:
        messages.warning(request, "A preprocessing task is already running for this workspace.")
        # Return the polling template to continue monitoring the existing task
        return render(request, 'partials/preprocessing_polling.html', {
            'workspace': workspace,
            'hx_indicator_id': 'spinner',
            'spinner_text': 'Preprocessing already in progress...'
        })

    # Start the background task
    task = threading.Thread(target=run_preprocessing_task, args=(workspace.id,))
    task.start()

    # Update workspace status
    workspace.preprocessing_status = Workspace.PreprocessingStatus.RUNNING
    workspace.task_id = str(task.ident) # Store thread identifier
    workspace.save()

    # Return the polling template to the user
    return render(request, 'partials/preprocessing_polling.html', {
        'workspace': workspace,
        'hx_indicator_id': 'spinner',
        'spinner_text': 'Preprocessing started...'
    })


@login_required
@require_http_methods(["GET"])
def check_preprocessing_status(request, workspace_id):
    """
    Checks the status of the preprocessing task and returns the appropriate template.
    """
    workspace = get_object_or_404(Workspace, id=workspace_id)
    status = workspace.preprocessing_status

    if status == Workspace.PreprocessingStatus.RUNNING:
        # Task is still running, continue polling
        return render(request, 'partials/preprocessing_polling.html', {
            'workspace': workspace,
            'hx_indicator_id': 'spinner',
            'spinner_text': 'Preprocessing in progress...'
        })
    else:
        # Task is completed or failed, return the final status button
        context = {
            'container_id': 'preprocessing-container',
            'hx_url': reverse('thoth_ai_backend:run_preprocessing', args=[workspace.id]),
            'hx_indicator_id': 'spinner',
            'button_text': 'Run Preprocessing',
            'last_run_text': 'Last run on',
            'never_run_text': 'Never run',
            'spinner_text': 'Processing in progress...',
            'icon_class': 'mdi mdi-refresh',
            'workspace': workspace,
            'last_run': workspace.last_preprocess,
        }
        if status == Workspace.PreprocessingStatus.COMPLETED:
            context['success_message'] = workspace.last_preprocess_log
        elif status == Workspace.PreprocessingStatus.FAILED:
            context['error_message'] = workspace.last_preprocess_log
        
        # Reset status to IDLE so it can be run again
        workspace.preprocessing_status = Workspace.PreprocessingStatus.IDLE
        workspace.save()
        
        return render(request, 'partials/operation_status_button.html', context)

@login_required
@require_POST
def upload_hints(request, workspace_id):
    """
    View function to handle the HTMX request for uploading hints to the vector database.
    """
    workspace = get_object_or_404(Workspace, id=workspace_id)
    context = {
        'container_id': 'hints-container',
        'hx_url': reverse('thoth_ai_backend:upload_hints', args=[workspace.id]),
        'hx_indicator_id': 'hints-spinner',
        'button_text': 'Load Hints',
        'last_run_text': 'Last upload on',
        'never_run_text': 'Hints not yet uploaded',
        'spinner_text': 'Uploading hints...',
        'icon_class': 'mdi mdi-database-refresh',
        'workspace': workspace,
        'last_run': workspace.last_hint_load,
    }

    try:
        successful_uploads = upload_hints_to_vectordb(workspace_id)
        workspace.refresh_from_db()

        if successful_uploads > 0:
            context['success_message'] = f"Successfully uploaded {successful_uploads} hints."
        else:
            context['special_message'] = "No new hints were found to upload."
        
        context['last_run'] = workspace.last_hint_load

    except (ValueError, IOError) as e:
        logger.error(f"Error uploading hints for workspace {workspace_id}: {e}", exc_info=True)
        context['error_message'] = str(e)
    except Exception as e:
        logger.error(f"Unexpected error uploading hints for workspace {workspace_id}: {e}", exc_info=True)
        context['error_message'] = f"An unexpected error occurred: {e}"

    return render(request, 'partials/operation_status_button.html', context)


@login_required
@require_POST
def upload_questions(request, workspace_id):
    """
    View function to handle the HTMX request for uploading questions to the vector database.
    """
    workspace = get_object_or_404(Workspace, id=workspace_id)
    context = {
        'container_id': 'questions-container',
        'hx_url': reverse('thoth_ai_backend:upload_questions', args=[workspace.id]),
        'hx_indicator_id': 'questions-spinner',
        'button_text': 'Load Questions',
        'last_run_text': 'Last upload on',
        'never_run_text': 'Questions not yet uploaded',
        'spinner_text': 'Uploading questions...',
        'icon_class': 'mdi mdi-database-refresh',
        'workspace': workspace,
        'last_run': workspace.last_sql_loaded,
    }

    try:
        successful_uploads = upload_questions_to_vectordb(workspace_id)
        workspace.refresh_from_db()

        if successful_uploads > 0:
            context['success_message'] = f"Successfully uploaded {successful_uploads} questions."
        else:
            context['special_message'] = "No new questions were found to upload."
        
        context['last_run'] = workspace.last_sql_loaded

    except (ValueError, IOError) as e:
        logger.error(f"Error uploading questions for workspace {workspace_id}: {e}", exc_info=True)
        context['error_message'] = str(e)
    except Exception as e:
        logger.error(f"Unexpected error uploading questions for workspace {workspace_id}: {e}", exc_info=True)
        context['error_message'] = f"An unexpected error occurred: {e}"

    return render(request, 'partials/operation_status_button.html', context)


@login_required
@require_POST
def update_database_columns(request, workspace_id):
    """
    View to handle the HTMX request for updating database columns.
    """
    workspace = get_object_or_404(Workspace, id=workspace_id)
    context = {
        'container_id': 'update-columns-container',
        'hx_url': reverse('thoth_ai_backend:update_database_columns', args=[workspace.id]),
        'hx_indicator_id': 'columns-spinner',
        'button_text': 'Update Columns Descriptions',
        'last_run_text': 'Last update on',
        'never_run_text': 'Columns not yet updated',
        'spinner_text': 'Updating columns...',
        'icon_class': 'mdi mdi-database-refresh',
        'workspace': workspace,
        'db': workspace.sql_db,
        'last_run': workspace.sql_db.last_columns_update if workspace.sql_db else None,
    }

    try:
        if not workspace.sql_db:
            raise ValueError(f"Workspace '{workspace.name}' has no SQL database configured.")
        
        update_database_columns_description(workspace_id=workspace_id)
        
        workspace.refresh_from_db()
        
        context['success_message'] = 'Database columns updated successfully!'
        context['db'] = workspace.sql_db
        context['last_run'] = workspace.sql_db.last_columns_update

    except Exception as e:
        logger.error(f"Error updating database columns for workspace {workspace_id}: {e}", exc_info=True)
        context['error_message'] = str(e)

    return render(request, 'partials/operation_status_button.html', context)

# Initialize logger for this module if not already done globally
@login_required
def export_hints_csv(request):
    try:
        vector_store = get_vector_store(request)
        
        # Call the utility function to handle file saving and get CSV content
        # Pass the request parameter for new path resolution
        saved_filepath, csv_content = export_hints_to_csv_file(vector_store, request)
        
        # Prepare HTTP response for download
        response = HttpResponse(csv_content, content_type='text/csv')
        # Filename for download, no timestamp as per requirement
        response['Content-Disposition'] = 'attachment; filename="hints_export.csv"' 
        
        messages.success(request, f"Hints exported successfully. Saved on server at: {saved_filepath}. Download started.")
        return response

    except Exception as e:
        logger.error(f"Error in export_hints_csv view: {e}", exc_info=True)
        messages.error(request, f"An error occurred while exporting hints: {e}")
        return HttpResponseRedirect(reverse('thoth_ai_backend:hints'))

@login_required
def export_columns_csv(request):
    try:
        vector_store = get_vector_store(request)
        
        # Pass the request parameter for new path resolution
        saved_filepath, csv_content = export_columns_to_csv_file(vector_store, request)
        
        response = HttpResponse(csv_content, content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="columns_export.csv"' 
        
        messages.success(request, f"Columns exported successfully. Saved on server at: {saved_filepath}. Download started.")
        return response

    except Exception as e:
        logger.error(f"Error in export_columns_csv view: {e}", exc_info=True)
        messages.error(request, f"An error occurred while exporting columns: {e}")
        return HttpResponseRedirect(reverse('thoth_ai_backend:columns'))

@login_required
def export_questions_csv(request):
    try:
        vector_store = get_vector_store(request)
        
        # Pass the request parameter for new path resolution
        saved_filepath, csv_content = export_questions_to_csv_file(vector_store, request)
        
        response = HttpResponse(csv_content, content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="questions_export.csv"' 
        
        messages.success(request, f"Questions exported successfully. Saved on server at: {saved_filepath}. Download started.")
        return response

    except Exception as e:
        logger.error(f"Error in export_questions_csv view: {e}", exc_info=True)
        messages.error(request, f"An error occurred while exporting questions: {e}")
        return HttpResponseRedirect(reverse('thoth_ai_backend:questions'))

# --- CSV Import Views ---

@login_required
@require_editor_group
@require_POST
def import_hints_server_csv(request):
    try:
        vector_store = get_vector_store(request)
        # Pass the request parameter for new path resolution
        result = import_hints_from_csv_file(vector_store, request)
        
        for msg_type, text in [(messages.SUCCESS if "Successfully" in m else messages.ERROR if "Error" in m else messages.WARNING, m) for m in result.get("messages", [])]:
            messages.add_message(request, msg_type, text)
            
    except Exception as e:
        logger.error(f"Error in import_hints_server_csv view: {e}", exc_info=True)
        messages.error(request, f"An unexpected error occurred during hints import: {e}")
    return HttpResponseRedirect(reverse('thoth_ai_backend:hints'))

@login_required
@require_editor_group
@require_POST
def import_columns_server_csv(request):
    try:
        vector_store = get_vector_store(request)
        # Pass the request parameter for new path resolution
        result = import_columns_from_csv_file(vector_store, request)

        for msg_type, text in [(messages.SUCCESS if "Successfully" in m else messages.ERROR if "Error" in m else messages.WARNING, m) for m in result.get("messages", [])]:
            messages.add_message(request, msg_type, text)

    except Exception as e:
        logger.error(f"Error in import_columns_server_csv view: {e}", exc_info=True)
        messages.error(request, f"An unexpected error occurred during columns import: {e}")
    return HttpResponseRedirect(reverse('thoth_ai_backend:columns'))

@login_required
@require_editor_group
@require_POST
def import_questions_server_csv(request):
    try:
        vector_store = get_vector_store(request)
        # Pass the request parameter for new path resolution
        result = import_questions_from_csv_file(vector_store, request)

        for msg_type, text in [(messages.SUCCESS if "Successfully" in m else messages.ERROR if "Error" in m else messages.WARNING, m) for m in result.get("messages", [])]:
            messages.add_message(request, msg_type, text)
            
    except Exception as e:
        logger.error(f"Error in import_questions_server_csv view: {e}", exc_info=True)
        messages.error(request, f"An unexpected error occurred during questions import: {e}")
    return HttpResponseRedirect(reverse('thoth_ai_backend:questions'))

# --- DELETE ALL VIEWS ---

@login_required
@require_editor_group
@require_POST
def delete_all_hints(request):
    """
    Deletes all hints from the vector store for the current workspace.
    """
    try:
        vector_store = get_vector_store(request)
        delete_all_hints_from_vector_store(vector_store)
        messages.success(request, "All hints have been successfully deleted.")
    except NotImplementedError as e:
        logger.error(f"NotImplementedError in delete_all_hints view: {e}", exc_info=True)
        messages.error(request, f"Could not delete hints: The delete operation is not implemented correctly. {e}")
    except Exception as e:
        logger.error(f"Error in delete_all_hints view: {e}", exc_info=True)
        messages.error(request, f"An error occurred while deleting all hints: {e}")
    return HttpResponseRedirect(reverse('thoth_ai_backend:hints'))

@login_required
@require_editor_group
@require_POST
def delete_all_columns(request):
    """
    Deletes all columns from the vector store for the current workspace.
    """
    try:
        vector_store = get_vector_store(request)
        delete_all_columns_from_vector_store(vector_store)
        messages.success(request, "All columns have been successfully deleted.")
    except NotImplementedError as e:
        logger.error(f"NotImplementedError in delete_all_columns view: {e}", exc_info=True)
        messages.error(request, f"Could not delete columns: The delete operation is not implemented correctly. {e}")
    except Exception as e:
        logger.error(f"Error in delete_all_columns view: {e}", exc_info=True)
        messages.error(request, f"An error occurred while deleting all columns: {e}")
    return HttpResponseRedirect(reverse('thoth_ai_backend:columns'))

@login_required
@require_POST
def delete_all_questions(request):
    """
    Deletes all questions from the vector store for the current workspace.
    """
    try:
        vector_store = get_vector_store(request)
        delete_all_questions_from_vector_store(vector_store)
        messages.success(request, "All questions have been successfully deleted.")
    except NotImplementedError as e:
        logger.error(f"NotImplementedError in delete_all_questions view: {e}", exc_info=True)
        messages.error(request, f"Could not delete questions: The delete operation is not implemented correctly. {e}")
    except Exception as e:
        logger.error(f"Error in delete_all_questions view: {e}", exc_info=True)
        messages.error(request, f"An error occurred while deleting all questions: {e}")
    return HttpResponseRedirect(reverse('thoth_ai_backend:questions'))
