# Common Issues and Solutions

## Database Connection Issues

### SQLite
**Issue**: Database locked errors
**Solution**: Ensure proper connection closing, use timeout parameter

### PostgreSQL
**Issue**: Connection refused
**Solution**: Check PostgreSQL service, verify connection parameters

### MySQL/MariaDB
**Issue**: Access denied for user
**Solution**: Check user permissions, verify password

### Oracle
**Issue**: ORA-12154: TNS could not resolve service name
**Solution**: Check tnsnames.ora configuration, verify service name

### SQL Server
**Issue**: Login failed for user
**Solution**: Check SQL Server authentication mode, verify credentials

### Informix
**Issue**: Connection string format errors
**Solution**: Verify INFORMIXDIR and connection string format

## LSH-Related Issues

### MinHash Generation
**Issue**: Memory errors with large datasets
**Solution**: Use batch processing, increase system memory

### Similarity Search
**Issue**: Poor similarity results
**Solution**: Adjust LSH parameters, increase number of permutations

### Index Storage
**Issue**: Large index files
**Solution**: Use compression, optimize storage format

## Development Issues

### Import Errors
**Issue**: ModuleNotFoundError for database drivers
**Solution**: Install required dependencies:
```bash
pip install psycopg2-binary  # PostgreSQL
pip install mysqlclient      # MySQL
pip install cx_Oracle        # Oracle
pip install pyodbc          # SQL Server
```

### Test Failures
**Issue**: Database-specific test failures
**Solution**: Check database configuration, verify test database exists

### Factory Registration Issues
**Issue**: Manager not found in registry
**Solution**: Ensure proper registration in factory.py

## Performance Issues

### Connection Pooling
**Issue**: Connection exhaustion
**Solution**: Implement connection pooling, set appropriate pool sizes

### Query Performance
**Issue**: Slow queries
**Solution**: Add indexes, optimize query structure

### Memory Usage
**Issue**: High memory consumption
**Solution**: Use streaming for large result sets, implement pagination

## Configuration Issues

### Environment Variables
**Issue**: Missing environment variables
**Solution**: Set required environment variables:
```bash
export THOTH_DB_HOST=localhost
export THOTH_DB_PORT=5432
export THOTH_DB_NAME=thoth_test
```

### Connection Strings
**Issue**: Invalid connection string format
**Solution**: Use proper format for each database:
- PostgreSQL: `postgresql://user:pass@host:port/db`
- MySQL: `mysql://user:pass@host:port/db`
- SQLite: `sqlite:///path/to/database.db`
