services:
  app:
    build:
      context: .
    image: thoth-be:v10
    container_name: thoth-be
    restart: always
    volumes:
      - ./setup_csv:/app/setup_csv
      - static-data:/vol/static
      - ./exports:/app/exports
      - thoth-shared-data:/app/data
      - ./logs:/app/logs
    environment:
      - DEBUG=False
      - IS_DOCKER=True
      - HOST_IP=host.docker.internal
    env_file:
      - _env
    networks:
      - thotnet
    depends_on:
      - db
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "host:host-gateway"

  # Servizio temporaneo per inizializzare i dati (usato solo con docker-compose run)
  data-init:
    image: alpine:latest
    volumes:
      - ./data:/source
      - thoth-shared-data:/destination
    command: sh -c "apk add --no-cache rsync && [ -d /source ] && [ -n "$(ls -A /source 2>/dev/null)" ] && rsync -av /source/ /destination/ || echo 'No data to copy or source directory empty'"
    profiles:
      - init-only
    networks:
      - thotnet

  qdrant:
    image: qdrant/qdrant:latest
    container_name: thoth-qdrant
    restart: always
    ports:
      - "6333:6333"
    networks:
      - thotnet

  proxy:
    build:
      context: ./proxy
    image: thoth-be-proxy:v2
    container_name: thoth-be-proxy
    restart: always
    volumes:
      - static-data:/vol/static
      - thoth-shared-data:/app/data
    ports:
      - 8040:80
    depends_on:
      - app
    networks:
      - thotnet

  db:
    build:
      context: ./postgres
    container_name: thoth-db
    restart: always
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - thoth-shared-data:/app/data
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=thoth_user
      - POSTGRES_PASSWORD=thoth_password
    ports:
      - "5443:5432"
    networks:
      - thotnet

volumes:
  static-data:
  postgres-data:
  thoth-shared-data:
    name: thoth-shared-data

networks:
  thotnet:
    name: thotnet
    driver: bridge
