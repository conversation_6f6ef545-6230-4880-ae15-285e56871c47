{% extends 'vertical_base.html' %}

{% block title %}Confirm Delete Question{% endblock title %}

{% block content %}
<div class="container-fluid">

    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Confirm Delete Question</h4>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <p>Are you sure you want to delete the following question?</p>
                    
                    <div class="mb-3">
                        <strong>Question:</strong>
                        <pre>{{ question.question }}</pre> {# Display question text #}
                    </div>
                    <div class="mb-3">
                        <strong>SQL:</strong>
                        <pre>{{ question.sql }}</pre> {# Display SQL query #}
                    </div>

                    <form method="post" action="{% url 'thoth_ai_backend:delete_question_confirmed' question_id=question.id %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">Yes, Delete</button>
                        <a href="{% url 'thoth_ai_backend:questions' %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div> <!-- end card-body -->
            </div> <!-- end card -->
        </div><!-- end col -->
    </div><!-- end row -->

</div> <!-- container -->
{% endblock content %}
