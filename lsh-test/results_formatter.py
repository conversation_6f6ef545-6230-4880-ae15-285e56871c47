"""
Results formatter for LSH evaluation tool.
"""

import json
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path


class ResultsFormatter:
    """Formats and displays LSH evaluation results."""
    
    def format_database_info(self, db_info: Dict[str, Any]) -> str:
        """Format database information for display."""
        try:
            if db_info.get('status') == 'error':
                return f"❌ Database not available: {db_info.get('error', 'Unknown error')}"
            
            lines = [
                "📊 DATABASE INFORMATION",
                f"   Database: {db_info.get('database_name', 'Unknown')}",
                f"   Path: {db_info.get('database_path', 'Unknown')}",
                f"   LSH Status: {'✅ Loaded' if db_info.get('lsh_loaded') else '❌ Not loaded'}",
                f"   Minhashes: {db_info.get('minhashes_count', 0)} entries"
            ]
            return "\n".join(lines)
        except Exception as e:
            return f"❌ Error formatting database info: {e}"
    
    def format_results(self, results: Dict[str, Any]) -> str:
        """Format search results for display."""
        try:
            if not results or not results.get('results'):
                return "❌ No results found"
            
            lines = ["🔍 SEARCH RESULTS"]
            lines.append("=" * 50)
            
            metadata = results.get('metadata', {})
            lines.append(f"⏱️  Query time: {metadata.get('query_time', 'N/A')}s")
            lines.append(f"📊 Total matches: {metadata.get('total_matches', 0)}")
            lines.append(f"🗂️  Tables found: {len(metadata.get('unique_tables', []))}")
            lines.append(f"📋 Columns found: {len(metadata.get('unique_columns', []))}")
            lines.append("")
            
            # Format results by keyword
            for keyword, keyword_results in results['results'].items():
                lines.append(f"🔑 Keyword: '{keyword}'")
                
                if not keyword_results:
                    lines.append("   No matches found")
                    lines.append("")
                    continue
                
                for table_name, table_data in keyword_results.items():
                    lines.append(f"   📊 Table: {table_name}")
                    
                    for column_name, values in table_data.items():
                        lines.append(f"      📋 Column: {column_name}")
                        
                        for value in values[:5]:  # Show first 5 values
                            lines.append(f"         • {value}")
                        
                        if len(values) > 5:
                            lines.append(f"         ... and {len(values) - 5} more")
                        
                        lines.append("")
                
                lines.append("")
            
            return "\n".join(lines)
            
        except Exception as e:
            return f"❌ Error formatting results: {e}"
    
    def export_json(self, results: Dict[str, Any], filename: str = None) -> str:
        """Export results to JSON file."""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"lsh_results_{timestamp}.json"
            
            # Add export metadata
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'results': results
            }
            
            filepath = Path(filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            return str(filepath)
            
        except Exception as e:
            return f"❌ Export failed: {e}"
