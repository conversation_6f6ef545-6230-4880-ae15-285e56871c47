import unittest
import os
import django
from pathlib import Path

# Add project root to sys.path
project_root = Path(__file__).resolve().parents[2]
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Thoth.settings')
django.setup()

from unittest.mock import patch, MagicMock
from thoth_ai_backend.preprocessing.preprocess import preprocess
from dbmanager.ThothDbManager import ThothDbManager
from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager
from vdbmanager.ThothHaystackVectorStore import ThothHaystackVectorStore

class TestDbManagerPreprocess(unittest.TestCase):

    @patch('thoth_ai_backend.preprocessing.preprocess.make_db_lsh')
    @patch('thoth_ai_backend.preprocessing.preprocess.make_db_context_vec_db')
    def test_preprocess(self, mock_make_db_context, mock_make_db_lsh):
        # Setup
        mock_db = MagicMock(spec=ThothDbManager)
        mock_document_store = MagicMock(spec=ThothHaystackVectorStore)
        db_params = {
            'db_name': 'test_db',
            'db_mode': 'dev'
        }
        setting = {
            'signature_size': 128,
            'n_grams': 2,
            'threshold': 0.5,
            'verbose': True,
            'use_value_description': True
        }

        # Execute
        preprocess(mock_db, mock_document_store, db_params, setting)

        # Assert
        mock_make_db_lsh.assert_called_once()
        mock_make_db_context.assert_called_once()

if __name__ == '__main__':
    unittest.main()
