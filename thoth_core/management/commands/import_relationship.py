# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.core.management.base import BaseCommand
from django.utils import timezone
from thoth_core.models import Relationship, SqlTable
import csv
import os
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Import Relationship data from CSV, preserving original IDs'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Starting Relationship CSV import'))
        
        csv_path = os.path.join(settings.BASE_DIR, 'setup_csv', 'relationship.csv')
        
        if not os.path.exists(csv_path):
            self.stdout.write(self.style.ERROR(f'Relationship CSV file not found at {csv_path}'))
            return
        
        imported_count = 0
        updated_count = 0
        error_count = 0
        
        with open(csv_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                try:
                    # Estrai l'ID dal CSV
                    model_id = row.get('id')
                    
                    # O<PERSON><PERSON> le tabelle SQL associate
                    parent_table = None
                    child_table = None
                    
                    if row.get('parent_table'):
                        try:
                            parent_table = SqlTable.objects.get(id=row['parent_table'])
                        except (SqlTable.DoesNotExist, ValueError):
                            try:
                                # Prova a ottenere per nome
                                parent_table = SqlTable.objects.filter(name=row['parent_table']).first()
                            except Exception:
                                self.stdout.write(self.style.WARNING(
                                    f"Parent SqlTable '{row['parent_table']}' not found for Relationship"
                                ))
                                continue  # Salta questa riga se non troviamo la tabella padre
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Missing parent_table reference for Relationship"
                        ))
                        continue  # Salta questa riga se non c'è riferimento alla tabella padre
                    
                    if row.get('child_table'):
                        try:
                            child_table = SqlTable.objects.get(id=row['child_table'])
                        except (SqlTable.DoesNotExist, ValueError):
                            try:
                                # Prova a ottenere per nome
                                child_table = SqlTable.objects.filter(name=row['child_table']).first()
                            except Exception:
                                self.stdout.write(self.style.WARNING(
                                    f"Child SqlTable '{row['child_table']}' not found for Relationship"
                                ))
                                continue  # Salta questa riga se non troviamo la tabella figlio
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Missing child_table reference for Relationship"
                        ))
                        continue  # Salta questa riga se non c'è riferimento alla tabella figlio
                    
                    if not model_id:
                        self.stdout.write(self.style.WARNING(
                            f"Missing ID in row, using parent and child tables as key"
                        ))
                        # Se non c'è ID, usa le tabelle padre e figlio come chiave
                        obj, created = Relationship.objects.update_or_create(
                            parent_table=parent_table,
                            child_table=child_table,
                            defaults={
                                'parent_column': row.get('parent_column', ''),
                                'child_column': row.get('child_column', ''),
                                'relationship_type': row.get('relationship_type', 'ONE_TO_MANY')
                            }
                        )
                        if created:
                            imported_count += 1
                            self.stdout.write(f"Created Relationship: {parent_table.name} -> {child_table.name}")
                        else:
                            updated_count += 1
                            self.stdout.write(f"Updated Relationship: {parent_table.name} -> {child_table.name}")
                    else:
                        # Se c'è l'ID, cerca di aggiornare l'oggetto esistente o creane uno nuovo con quell'ID
                        try:
                            obj = Relationship.objects.get(id=model_id)
                            # Aggiorna i campi
                            obj.parent_table = parent_table
                            obj.child_table = child_table
                            obj.parent_column = row.get('parent_column', '')
                            obj.child_column = row.get('child_column', '')
                            obj.relationship_type = row.get('relationship_type', 'ONE_TO_MANY')
                            obj.save()
                            updated_count += 1
                            self.stdout.write(f"Updated Relationship with ID {model_id}: {parent_table.name} -> {child_table.name}")
                        except Relationship.DoesNotExist:
                            # Crea un nuovo oggetto con l'ID specificato
                            obj = Relationship.objects.create(
                                id=model_id,
                                parent_table=parent_table,
                                child_table=child_table,
                                parent_column=row.get('parent_column', ''),
                                child_column=row.get('child_column', ''),
                                relationship_type=row.get('relationship_type', 'ONE_TO_MANY')
                            )
                            imported_count += 1
                            self.stdout.write(f"Created Relationship with ID {model_id}: {parent_table.name} -> {child_table.name}")
                except Exception as e:
                    error_count += 1
                    self.stdout.write(self.style.ERROR(f"Error processing row {row}: {str(e)}"))
        
        self.stdout.write(self.style.SUCCESS(f'Import completed. Imported: {imported_count}, Updated: {updated_count}, Errors: {error_count}'))
