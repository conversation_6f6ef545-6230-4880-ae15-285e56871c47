{% extends 'vertical_base.html' %}

{% block title %}{% if hint_id %}Update Hint{% else %}Add Hint{% endif %}{% endblock title %}

{% block content %}
<div class="container-fluid">

    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% if hint_id %}Update Hint{% else %}Add New Hint{% endif %}</h4>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {# Determine form action URL based on whether it's create or update #}
                    {% if hint_id %}
                        <form method="post" action="{% url 'thoth_ai_backend:update_hint' hint_id=hint_id %}">
                    {% else %}
                        <form method="post" action="{% url 'thoth_ai_backend:create_hint' %}">
                    {% endif %}
                        {% csrf_token %}

                        {# Display non-field errors (e.g., general save errors) #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="{{ form.text.id_for_label }}" class="form-label">{{ form.text.label }}</label>
                            {{ form.text }}
                            {% if form.text.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.text.errors|striptags }}
                                </div>
                            {% endif %}
                        </div>
                        <button type="submit" class="btn btn-primary">{% if hint_id %}Update Hint{% else %}Save Hint{% endif %}</button>
                        <a href="{% url 'thoth_ai_backend:hints' %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div> <!-- end card-body -->
            </div> <!-- end card -->
        </div><!-- end col -->
    </div><!-- end row -->

</div> <!-- container -->

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function() {
                alert('Processing saving ...');
                // Optionally, you could disable the submit button here to prevent multiple submissions
                // form.querySelector('button[type="submit"]').disabled = true;
            });
        }
    });
</script>
{% endblock content %}
