# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.urls import path
from . import views
# Import the new delete views along with existing ones
from . import views # Keep this for general views access
from .views import (
    ColumnsView, HintsView, QuestionsView, PreprocessView,
)

app_name = 'thoth_ai_backend'

urlpatterns = [
    path('hints/', HintsView.as_view(), name='hints'),
    path('hints/create/', views.manage_hint, name='create_hint'), # Route create to manage_hint
    path('hints/update/<str:hint_id>/', views.manage_hint, name='update_hint'), # Route update to manage_hint with ID
    # Add URL pattern for the confirmation page (GET)
    path('hints/delete/<str:hint_id>/confirm/', views.confirm_delete_hint, name='confirm_delete_hint'),
    # Add URL pattern for the actual deletion action (POST)
    path('hints/delete/<str:hint_id>/execute/', views.delete_hint_confirmed, name='delete_hint_confirmed'),
    # New URL for exporting hints
    path('hints/export_csv/', views.export_hints_csv, name='export_hints_csv'),
    path('hints/import_server_csv/', views.import_hints_server_csv, name='import_hints_server_csv'), # New import URL
    path('vector_store/hints/delete_all/', views.delete_all_hints, name='delete_all_hints'), # New delete all hints URL
    
    path('questions/', QuestionsView.as_view(), name='questions'),
    path('questions/export_csv/', views.export_questions_csv, name='export_questions_csv'), # New export URL
    path('questions/import_server_csv/', views.import_questions_server_csv, name='import_questions_server_csv'), # New import URL
    path('vector_store/questions/delete_all/', views.delete_all_questions, name='delete_all_questions'), # New delete all questions URL
    path('columns/', ColumnsView.as_view(), name='columns'),
    path('columns/export_csv/', views.export_columns_csv, name='export_columns_csv'), # New export URL
    path('columns/import_server_csv/', views.import_columns_server_csv, name='import_columns_server_csv'), # New import URL
    path('vector_store/columns/delete_all/', views.delete_all_columns, name='delete_all_columns'), # New delete all columns URL
    # URL for viewing a specific column document's details (using string ID)
    path('columns/view/<str:column_id>/', views.view_columns, name='view_columns'),
    # URL for handling the deletion of a column document (POST request, using string ID)

    # Question (SqlDocument) CRUD URLs
    path('questions/create/', views.manage_question, name='create_question'),
    path('questions/update/<str:question_id>/', views.manage_question, name='update_question'),
    path('questions/delete/<str:question_id>/confirm/', views.confirm_delete_question, name='confirm_delete_question'),
    path('questions/delete/<str:question_id>/execute/', views.delete_question_confirmed, name='delete_question_confirmed'),

    path('preprocess/', PreprocessView.as_view(), name='preprocess'),
    # New URL pattern for preprocessing
    path('run_preprocessing/<int:workspace_id>/', views.run_preprocessing, name='run_preprocessing'),
    path('check_preprocessing_status/<int:workspace_id>/', views.check_preprocessing_status, name='check_preprocessing_status'),
    
    # Add this new URL pattern
    path('workspace/<int:workspace_id>/upload-hints/', views.upload_hints, name='upload_hints'),
    path('workspace/<int:workspace_id>/upload-questions/', views.upload_questions, name='upload_questions'),
    path('update-database-columns/<int:workspace_id>/', views.update_database_columns, name='update_database_columns'),
]
