"""
SQL Server adapter for Thoth SQL Database Manager.
"""

from typing import Any, Dict, List, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

from ..core.interfaces import DbAdapter


class SQLServerAdapter(DbAdapter):
    """SQL Server database adapter."""
    
    def __init__(self, connection_string: str, **kwargs: Any) -> None:
        """
        Initialize SQL Server adapter.
        
        Args:
            connection_string: SQL Server connection string
            **kwargs: Additional connection parameters
        """
        self.connection_string = connection_string
        self.engine = None
        self.connection_params = kwargs
        
    def connect(self) -> None:
        """Establish database connection."""
        try:
            self.engine = create_engine(
                self.connection_string,
                pool_pre_ping=True,
                **self.connection_params
            )
        except Exception as e:
            raise ConnectionError(f"Failed to connect to SQL Server: {e}")
    
    def disconnect(self) -> None:
        """Close database connection."""
        if self.engine:
            self.engine.dispose()
            self.engine = None
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a query and return results."""
        if not self.engine:
            self.connect()
            
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                return [dict(row._mapping) for row in result]
        except SQLAlchemyError as e:
            raise RuntimeError(f"SQL Server query failed: {e}")
    
    def execute_update(self, query: str, params: Optional[Dict[str, Any]] = None) -> int:
        """Execute an update query and return affected row count."""
        if not self.engine:
            self.connect()
            
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                conn.commit()
                return result.rowcount
        except SQLAlchemyError as e:
            raise RuntimeError(f"SQL Server update failed: {e}")
    
    def get_tables(self) -> List[str]:
        """Get list of tables in the database."""
        query = """
        SELECT TABLE_NAME as name
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """
        result = self.execute_query(query)
        return [row['name'] for row in result]
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get schema information for a specific table."""
        query = f"""
        SELECT 
            COLUMN_NAME as name,
            DATA_TYPE as type,
            IS_NULLABLE as nullable,
            COLUMN_DEFAULT as default_value,
            CASE WHEN COLUMNPROPERTY(OBJECT_ID(TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 1 THEN 1 ELSE 0 END as is_identity,
            CASE WHEN EXISTS (
                SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = '{table_name}' 
                AND COLUMN_NAME = c.COLUMN_NAME 
                AND CONSTRAINT_NAME LIKE 'PK_%'
            ) THEN 1 ELSE 0 END as is_primary_key
        FROM INFORMATION_SCHEMA.COLUMNS c
        WHERE TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
        """
        
        columns = self.execute_query(query)
        
        schema = {
            'table_name': table_name,
            'columns': []
        }
        
        for col in columns:
            schema['columns'].append({
                'name': col['name'],
                'type': col['type'],
                'nullable': col['nullable'] == 'YES',
                'default': col['default_value'],
                'primary_key': bool(col['is_primary_key']),
                'auto_increment': bool(col['is_identity'])
            })
        
        return schema
    
    def get_indexes(self, table_name: str) -> List[Dict[str, Any]]:
        """Get index information for a table."""
        query = f"""
        SELECT 
            i.name as index_name,
            c.name as column_name,
            i.is_unique as unique_index,
            i.type_desc as index_type
        FROM sys.indexes i
        JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
        JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        JOIN sys.tables t ON i.object_id = t.object_id
        WHERE t.name = '{table_name}'
        ORDER BY i.name, ic.key_ordinal
        """
        
        return self.execute_query(query)
    
    def get_foreign_keys(self, table_name: str) -> List[Dict[str, Any]]:
        """Get foreign key information for a table."""
        query = f"""
        SELECT 
            fk.name as constraint_name,
            c.name as column_name,
            OBJECT_NAME(fk.referenced_object_id) as referenced_table,
            rc.name as referenced_column
        FROM sys.foreign_keys fk
        JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
        JOIN sys.columns c ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id = c.column_id
        JOIN sys.columns rc ON fkc.referenced_object_id = rc.object_id AND fkc.referenced_column_id = rc.column_id
        JOIN sys.tables t ON fk.parent_object_id = t.object_id
        WHERE t.name = '{table_name}'
        """
        
        return self.execute_query(query)
    
    def create_table(self, table_name: str, schema: Dict[str, Any]) -> None:
        """Create a new table with the given schema."""
        columns = []
        for col in schema.get('columns', []):
            col_def = f"[{col['name']}] {col['type']}"
            if not col.get('nullable', True):
                col_def += " NOT NULL"
            if col.get('default') is not None:
                col_def += f" DEFAULT {col['default']}"
            if col.get('primary_key'):
                col_def += " PRIMARY KEY"
            if col.get('auto_increment'):
                col_def += " IDENTITY(1,1)"
            columns.append(col_def)
        
        query = f"CREATE TABLE [{table_name}] ({', '.join(columns)})"
        self.execute_update(query)
    
    def drop_table(self, table_name: str) -> None:
        """Drop a table."""
        query = f"DROP TABLE IF EXISTS [{table_name}]"
        self.execute_update(query)
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        query = f"""
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = '{table_name}'
        AND TABLE_TYPE = 'BASE TABLE'
        """
        result = self.execute_query(query)
        return result[0]['count'] > 0
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information."""
        return {
            'type': 'sqlserver',
            'connection_string': self.connection_string,
            'connected': self.engine is not None
        }
