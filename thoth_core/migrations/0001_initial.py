# Generated by Django 5.2 on 2025-07-12 14:40

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AiModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('specific_model', models.Char<PERSON>ield(default='', max_length=100)),
                ('name', models.CharField(max_length=100, null=True)),
                ('api_key', models.CharField(blank=True, max_length=255, null=True, verbose_name='API Key')),
                ('url', models.URLField(blank=True, null=True)),
                ('temperature_allowed', models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=True)),
                ('temperature', models.DecimalField(decimal_places=2, default=0.8, help_text='\n        What sampling temperature to use, between 0 and 2. \n        Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. \n        We generally recommend altering this or top_p, not both simultaneously.\n        ', max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(2)], verbose_name='Temperature [0.00 .. 2.00] - (0.80)')),
                ('top_p', models.DecimalField(decimal_places=2, default=0.9, help_text='\n        Top-P changes how the model selects tokens for output. Tokens are selected from the most (see top-K) to least probable until the sum of their probabilities equals the top-P value. For example, if tokens A, B, and C have a probability of 0.3, 0.2, and 0.1 and the top-P value is 0.5, then the model will select either A or B as the next token by using temperature and excludes C as a candidate.\n        Specify a lower value for less random responses and a higher value for more random responses.\n        ', max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1)], verbose_name='Top P [0.00 .. 1.00] - (0.90)')),
                ('max_tokens', models.IntegerField(default=1280, help_text='\n        Maximum number of tokens that can be generated in the response. \n        A token is approximately four characters. 100 tokens correspond to roughly 60-80 words.\n        Specify a lower value for shorter responses and a higher value for potentially longer responses.', validators=[django.core.validators.MinValueValidator(128), django.core.validators.MaxValueValidator(16000)], verbose_name='Max Tokens [-2 .. 16000] (128)')),
                ('timeout', models.FloatField(default=45.0, help_text='\n        Timeouts take place if this threshold, expressed in seconds, is exceeded:', validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(3600.0)], verbose_name='Timeout [1.00 .. 3600.00] - (5.00)')),
            ],
        ),
        migrations.CreateModel(
            name='BasicAiModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('provider', models.CharField(choices=[('OPENAI', 'OpenAI'), ('ANTHROPIC', 'Anthropic'), ('CODESTRAL', 'Codestral'), ('DEEPSEEK', 'DeepSeek'), ('META', 'LLama'), ('MISTRAL', 'Mistral'), ('OLLAMA', 'Ollama'), ('OPENROUTER', 'OpenRouter'), ('GEMINI', 'Gemini')], default='ANTHROPIC', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='SqlDb',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('db_host', models.CharField(blank=True, max_length=255)),
                ('db_type', models.CharField(choices=[('MariaDB', 'MariaDB'), ('MySQL', 'MySQL'), ('Oracle', 'Oracle'), ('PostgreSQL', 'PostgreSQL'), ('SQLServer', 'SQLServer'), ('SQLite', 'SQLite')], default='PostgreSQL', max_length=255)),
                ('db_name', models.CharField(max_length=255)),
                ('db_port', models.IntegerField(blank=True, null=True)),
                ('schema', models.CharField(blank=True, max_length=255)),
                ('user_name', models.CharField(blank=True, max_length=255)),
                ('password', models.CharField(blank=True, max_length=255)),
                ('db_mode', models.CharField(choices=[('dev', 'dev'), ('test', 'test'), ('prod', 'prod')], default='dev', max_length=255)),
                ('language', models.CharField(blank=True, default='English', max_length=50)),
                ('scope', models.TextField(blank=True, null=True)),
                ('last_columns_update', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='VectorDb',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('vect_type', models.CharField(choices=[('ChromaDB', 'ChromaDB'), ('Elasticsearch', 'Elasticsearch'), ('FAISS', 'FAISS'), ('Milvus', 'Milvus'), ('MongoDB', 'MongoDB'), ('Pinecone', 'Pinecone'), ('PGVector', 'PGVector'), ('Qdrant', 'Qdrant'), ('Weaviate', 'Weaviate')], default='Qdrant', max_length=255)),
                ('host', models.CharField(blank=True, max_length=255)),
                ('port', models.IntegerField(blank=True)),
                ('username', models.CharField(blank=True, max_length=255)),
                ('password', models.CharField(blank=True, max_length=255)),
                ('api_key', models.CharField(blank=True, max_length=255)),
                ('tenant', models.CharField(blank=True, max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='Agent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('agent_type', models.CharField(choices=[('GENERATESQLBASIC', 'Generate SQL Basic'), ('DEFAULT', 'Default'), ('EXTRACTKEYWORDS', 'Extract Keywords'), ('SELECTCOLUMNS', 'Select Columns'), ('SQLBASIC', 'Sql - Basic'), ('SQLADVANCED', 'Sql - Advanced'), ('SQLEXPERT', 'Sql - Expert'), ('SQLTITAN', 'Sql - Titan'), ('TESTGENERATOR', 'Test Generator'), ('TESTEXECUTOR', 'Test Executor'), ('ASKFORHUMANHELP', 'Ask For Human Help')], default='EXTRACTKEYWORDS', max_length=255)),
                ('temperature', models.DecimalField(decimal_places=2, default=0.8, help_text='\n        What sampling temperature to use, between 0 and 2. \n        Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. \n        We generally recommend altering this or top_p, not both simultaneously.\n        ', max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(2)], verbose_name='Temperature [0.00 .. 2.00] - (0.80)')),
                ('top_p', models.DecimalField(decimal_places=2, default=0.95, help_text='\n        Top-P changes how the model selects tokens for output.', max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1)])),
                ('max_tokens', models.IntegerField(default=1280, help_text='\n        The maximum number of tokens to generate.')),
                ('timeout', models.FloatField(default=45.0, help_text='\n        Timeouts take place if this threshold, expressed in seconds, is exceeded:\n        ', validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(3600.0)], verbose_name='Timeout [1.00 .. 3600.00] - (45.00)')),
                ('system_prompt', models.TextField(blank=True)),
                ('user_prompt', models.TextField(blank=True)),
                ('retries', models.IntegerField(default=1)),
                ('ai_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='agents', to='thoth_core.aimodel')),
            ],
        ),
        migrations.AddField(
            model_name='aimodel',
            name='basic_model',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ai_models', to='thoth_core.basicaimodel'),
        ),
        migrations.CreateModel(
            name='GroupProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('show_sql', models.BooleanField(default=False, help_text='Show SQL queries to group members')),
                ('show_keywords', models.BooleanField(default=True, help_text='Show keywords extraction to group members')),
                ('show_hints', models.BooleanField(default=True, help_text='Show hints to group members')),
                ('show_process_info', models.BooleanField(default=False, help_text='Show process information to group members')),
                ('show_sql_generation', models.BooleanField(default=True, help_text='Show SQL generation process to group members')),
                ('explain_generated_query', models.BooleanField(default=True, help_text='Explain generated SQL query to group members')),
                ('group', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to='auth.group')),
            ],
            options={
                'verbose_name': 'Group Profile',
                'verbose_name_plural': 'Group Profiles',
            },
        ),
        migrations.CreateModel(
            name='Setting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('theme', models.CharField(blank=True, max_length=50, null=True)),
                ('language', models.CharField(max_length=50)),
                ('example_rows_for_comment', models.PositiveIntegerField(default=5, help_text='Number of example rows to use for comment generation')),
                ('system_prompt', models.TextField(blank=True, null=True)),
                ('signature_size', models.IntegerField(default=30, help_text='Size of the signature - LSH')),
                ('n_grams', models.IntegerField(default=3, help_text='Number of n-grams - LSH')),
                ('threshold', models.FloatField(default=0.01, help_text='Threshold value for similarity comparison - LSH')),
                ('verbose', models.BooleanField(default=True, help_text='Enable verbose mode for LHS preprocessing')),
                ('use_value_description', models.BooleanField(default=True, help_text='Enable verbose mode for similarity comparison - LSH')),
                ('comment_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='setting_comment_models', to='thoth_core.aimodel')),
            ],
        ),
        migrations.CreateModel(
            name='SqlTable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('generated_comment', models.TextField(blank=True)),
                ('sql_db', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tables', to='thoth_core.sqldb')),
            ],
        ),
        migrations.CreateModel(
            name='SqlColumn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_column_name', models.CharField(max_length=255)),
                ('column_name', models.CharField(blank=True, max_length=255)),
                ('data_format', models.CharField(choices=[('INT', 'INT'), ('FLOAT', 'FLOAT'), ('DOUBLE', 'DOUBLE'), ('DECIMAL', 'DECIMAL'), ('VARCHAR', 'VARCHAR'), ('CHAR', 'CHAR'), ('DATE', 'DATE'), ('TIME', 'TIME'), ('TIMESTAMP', 'TIMESTAMP'), ('BOOLEAN', 'BOOLEAN'), ('ENUM', 'ENUM')], default='VARCHAR', max_length=255)),
                ('column_description', models.TextField(blank=True)),
                ('generated_comment', models.TextField(blank=True)),
                ('value_description', models.TextField(blank=True)),
                ('pk_field', models.TextField(blank=True)),
                ('fk_field', models.TextField(blank=True)),
                ('sql_table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='columns', to='thoth_core.sqltable')),
            ],
        ),
        migrations.CreateModel(
            name='Relationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_column', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_columns', to='thoth_core.sqlcolumn')),
                ('target_column', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_columns', to='thoth_core.sqlcolumn')),
                ('source_table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_tables', to='thoth_core.sqltable')),
                ('target_table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_tables', to='thoth_core.sqltable')),
            ],
        ),
        migrations.AddField(
            model_name='sqldb',
            name='vector_db',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='thoth_core.vectordb'),
        ),
        migrations.CreateModel(
            name='Workspace',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('level', models.CharField(choices=[('BASIC', 'Basic'), ('ADVANCED', 'Advanced'), ('EXPERT', 'Expert'), ('TITANIC', 'Titanic')], default='BASIC', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('last_preprocess', models.DateTimeField(blank=True, null=True)),
                ('last_hint_load', models.DateTimeField(blank=True, null=True)),
                ('last_sql_loaded', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('preprocessing_status', models.CharField(choices=[('IDLE', 'Idle'), ('RUNNING', 'Running'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='IDLE', max_length=20)),
                ('task_id', models.CharField(blank=True, max_length=255, null=True)),
                ('last_preprocess_log', models.TextField(blank=True, null=True)),
                ('ask_human_help_agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_ask_human', to='thoth_core.agent')),
                ('default_agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_default', to='thoth_core.agent')),
                ('default_workspace', models.ManyToManyField(blank=True, related_name='default_workspaces', to=settings.AUTH_USER_MODEL)),
                ('explanation_ai_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='explanation_workspaces', to='thoth_core.aimodel')),
                ('kw_sel_agent_1', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_kw1', to='thoth_core.agent')),
                ('kw_sel_agent_2', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_kw2', to='thoth_core.agent')),
                ('plotly_generator_ai_model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='plotly_workspaces', to='thoth_core.aimodel')),
                ('sel_columns_agent_1', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sel_col_1', to='thoth_core.agent')),
                ('sel_columns_agent_2', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sel_col_2', to='thoth_core.agent')),
                ('setting', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces', to='thoth_core.setting')),
                ('sql_advanced_agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sql_advanced', to='thoth_core.agent')),
                ('sql_basic_agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sql_basic', to='thoth_core.agent')),
                ('sql_db', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces', to='thoth_core.sqldb')),
                ('sql_expert_agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sql_expert', to='thoth_core.agent')),
                ('sql_titan_agent_1', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sql_titan_1', to='thoth_core.agent')),
                ('sql_titan_agent_2', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sql_titan_2', to='thoth_core.agent')),
                ('test_exec_agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_test_exec', to='thoth_core.agent')),
                ('test_gen_agent_1', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_test_gen_1', to='thoth_core.agent')),
                ('test_gen_agent_2', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_test_gen_2', to='thoth_core.agent')),
                ('users', models.ManyToManyField(related_name='workspaces', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
