"""
Oracle plugin for Thoth SQL Database Manager.
"""

from typing import Any, Dict, List, Optional
from ..core.interfaces import DbPlugin
from ..adapters.oracle import OracleAdapter


class OraclePlugin(DbPlugin):
    """Oracle database plugin."""
    
    def __init__(self) -> None:
        """Initialize Oracle plugin."""
        self.name = "oracle"
        self.display_name = "Oracle"
        self.description = "Oracle database plugin"
        self.version = "1.0.0"
        
    def get_adapter(self, connection_string: str, **kwargs: Any) -> OracleAdapter:
        """
        Get Oracle adapter instance.
        
        Args:
            connection_string: Oracle connection string
            **kwargs: Additional connection parameters
            
        Returns:
            OracleAdapter instance
        """
        return OracleAdapter(connection_string, **kwargs)
    
    def get_connection_string_template(self) -> str:
        """Get connection string template for Oracle."""
        return "oracle+cx_oracle://{username}:{password}@{host}:{port}/{service_name}"
    
    def get_default_port(self) -> int:
        """Get default Oracle port."""
        return 1521
    
    def get_required_parameters(self) -> List[str]:
        """Get required connection parameters."""
        return ["host", "service_name", "username", "password"]
    
    def get_optional_parameters(self) -> Dict[str, Any]:
        """Get optional connection parameters."""
        return {
            "port": 1521,
            "encoding": "UTF-8",
            "nencoding": "UTF-8"
        }
    
    def validate_connection_string(self, connection_string: str) -> bool:
        """Validate Oracle connection string format."""
        return connection_string.startswith("oracle+cx_oracle://")
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get Oracle database information."""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "version": self.version,
            "features": [
                "transactions",
                "foreign_keys",
                "indexes",
                "views",
                "stored_procedures",
                "triggers",
                "sequences",
                "partitions",
                "materialized_views"
            ],
            "data_types": [
                "NUMBER", "BINARY_INTEGER", "PLS_INTEGER",
                "VARCHAR2", "NVARCHAR2", "CHAR", "NCHAR", "CLOB", "NCLOB", "BLOB", "BFILE",
                "DATE", "TIMESTAMP", "INTERVAL",
                "RAW", "LONG", "LONG RAW",
                "ROWID", "UROWID"
            ]
        }
    
    def get_sample_connection_config(self) -> Dict[str, Any]:
        """Get sample connection configuration."""
        return {
            "host": "localhost",
            "port": 1521,
            "service_name": "ORCL",
            "username": "user",
            "password": "password"
        }
