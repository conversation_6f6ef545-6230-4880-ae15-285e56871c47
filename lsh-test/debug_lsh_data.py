#!/usr/bin/env python3
"""
Debug LSH data contents.
"""

import pickle
from pathlib import Path
from config_loader import Config<PERSON><PERSON><PERSON>

def debug_lsh_data():
    """Debug what's actually in the LSH files."""
    print("🔍 Debugging LSH Data Contents")
    print("=" * 50)
    
    try:
        config = ConfigLoader()
        lsh_path = config.get_lsh_path()
        db_name = config.get('DB_NAME')
        
        # Load and inspect LSH file
        lsh_file = lsh_path / f"{db_name}_lsh.pkl"
        print(f"📄 Loading LSH from: {lsh_file}")
        
        with open(lsh_file, 'rb') as f:
            lsh = pickle.load(f)
        
        print(f"   LSH type: {type(lsh)}")
        print(f"   LSH threshold: {getattr(lsh, 'threshold', 'N/A')}")
        print(f"   LSH num_perm: {getattr(lsh, 'num_perm', 'N/A')}")
        
        # Load and inspect minhashes file
        minhash_file = lsh_path / f"{db_name}_lsh_minhashes.pkl"
        print(f"\n📄 Loading minhashes from: {minhash_file}")
        
        with open(minhash_file, 'rb') as f:
            minhashes = pickle.load(f)
        
        print(f"   Minhashes type: {type(minhashes)}")
        print(f"   Minhashes count: {len(minhashes)}")
        
        # Show sample minhash entries
        print(f"\n📋 Sample minhash entries:")
        for i, (key, value) in enumerate(list(minhashes.items())[:5]):
            print(f"   {i+1}. Key: {key}")
            if isinstance(value, tuple) and len(value) >= 4:
                minhash_obj, table_name, column_name, actual_value = value
                print(f"      Table: {table_name}")
                print(f"      Column: {column_name}")
                print(f"      Value: {actual_value}")
                print(f"      MinHash type: {type(minhash_obj)}")
            else:
                print(f"      Value: {value}")
            print()
        
        # Load unique values for comparison
        unique_values_file = lsh_path / f"{db_name}_unique_values.pkl"
        if unique_values_file.exists():
            print(f"📄 Loading unique values from: {unique_values_file}")
            with open(unique_values_file, 'rb') as f:
                unique_values = pickle.load(f)
            
            print(f"   Unique values type: {type(unique_values)}")
            print(f"   Tables in unique values: {list(unique_values.keys()) if isinstance(unique_values, dict) else 'Not a dict'}")
            
            if isinstance(unique_values, dict):
                for table, columns in unique_values.items():
                    print(f"   Table '{table}': {len(columns)} columns")
                    for column, values in columns.items():
                        print(f"     Column '{column}': {len(values)} values")
                        if values:
                            print(f"       Sample: {values[:3]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_lsh_data()