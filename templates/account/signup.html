{% extends "account/base.html" %}
{% load static i18n crispy_forms_tags %}

{% block title %}{% trans "Register" %}{% endblock title %}

{% block body_extra %}
class="authentication-bg position-relative"
{% endblock %}

{% block page_content %}
        <div class="position-absolute start-0 end-0 start-0 bottom-0 w-100 h-100">
        <svg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 800 800'>
            <g fill-opacity='0.22'>
                <circle style="fill: rgba(var(--ct-primary-rgb), 0.1);" cx='400' cy='400' r='600'/>
                <circle style="fill: rgba(var(--ct-primary-rgb), 0.2);" cx='400' cy='400' r='500'/>
                <circle style="fill: rgba(var(--ct-primary-rgb), 0.3);" cx='400' cy='400' r='300'/>
                <circle style="fill: rgba(var(--ct-primary-rgb), 0.4);" cx='400' cy='400' r='200'/>
                <circle style="fill: rgba(var(--ct-primary-rgb), 0.5);" cx='400' cy='400' r='100'/>
            </g>
        </svg>
    </div>

<div class="account-pages pt-2 pt-sm-5 pb-4 pb-sm-5 position-relative">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-xxl-4 col-lg-5">
        <div class="card">
          <!-- Logo-->
          <div class="card-header pt-4 pb-4 text-center bg-primary">
            <a href="{% url 'dashboard:index' %}">
              <span><img src="{% static 'images/logo.png' %}" alt="" height="18"></span>
            </a>
          </div>

          <div class="card-body p-4">

            <div class="text-center w-75 m-auto">
              <h4 class="text-dark-50 text-center mt-0 fw-bold">Free Sign Up</h4>
              <p class="text-muted mb-4">Don't have an account? Create your account, it takes less than a minute </p>
            </div>

            <form id="signup_form" method="post" action="{% url 'account_signup' %}" novalidate>
              {% csrf_token %}
              {{ form|crispy }}
              {% if redirect_field_value %}
              <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
              {% endif %}

              <div class="mb-3 mt-3 text-center">
                <button class="btn btn-primary" type="submit"> {% trans "Sign Up" %} </button>
              </div>
            </form>

          </div> <!-- end card-body -->
        </div>
        <!-- end card -->

        <div class="row mt-3">
          <div class="col-12 text-center">
            <p class="text-muted">Already have account? <a href="{% url 'account_login' %}" class="text-muted ms-1"><b>Log
                  In</b></a></p>
          </div> <!-- end col-->
        </div>
        <!-- end row -->

      </div> <!-- end col -->
    </div>
    <!-- end row -->
  </div>
  <!-- end container -->
</div>
<!-- end page -->

<footer class="footer footer-alt">
  2018 - 2021 © Hyper - Coderthemes.com
</footer>

{% endblock %}