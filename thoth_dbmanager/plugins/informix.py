"""
Informix plugin for Thoth SQL Database Manager.
"""

from typing import Any, Dict, List, Optional
from ..core.interfaces import DbPlugin
from ..adapters.informix import InformixAdapter


class InformixPlugin(DbPlugin):
    """Informix database plugin."""
    
    def __init__(self) -> None:
        """Initialize Informix plugin."""
        self.name = "informix"
        self.display_name = "Informix"
        self.description = "IBM Informix database plugin"
        self.version = "1.0.0"
        
    def get_adapter(self, connection_string: str, **kwargs: Any) -> InformixAdapter:
        """
        Get Informix adapter instance.
        
        Args:
            connection_string: Informix connection string
            **kwargs: Additional connection parameters
            
        Returns:
            InformixAdapter instance
        """
        return InformixAdapter(connection_string, **kwargs)
    
    def get_connection_string_template(self) -> str:
        """Get connection string template for Informix."""
        return "informix+informixdb://{username}:{password}@{host}:{port}/{database}"
    
    def get_default_port(self) -> int:
        """Get default Informix port."""
        return 9088
    
    def get_required_parameters(self) -> List[str]:
        """Get required connection parameters."""
        return ["host", "database", "username", "password"]
    
    def get_optional_parameters(self) -> Dict[str, Any]:
        """Get optional connection parameters."""
        return {
            "port": 9088,
            "server": "ol_server",
            "protocol": "onsoctcp"
        }
    
    def validate_connection_string(self, connection_string: str) -> bool:
        """Validate Informix connection string format."""
        return "informix+informixdb://" in connection_string
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get Informix database information."""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "version": self.version,
            "features": [
                "transactions",
                "foreign_keys",
                "indexes",
                "views",
                "stored_procedures",
                "triggers",
                "sequences"
            ],
            "data_types": [
                "INTEGER", "SMALLINT", "BIGINT", "DECIMAL", "MONEY",
                "CHAR", "VARCHAR", "LVARCHAR", "TEXT", "BYTE", "BLOB",
                "DATE", "DATETIME", "INTERVAL",
                "BOOLEAN"
            ]
        }
    
    def get_sample_connection_config(self) -> Dict[str, Any]:
        """Get sample connection configuration."""
        return {
            "host": "localhost",
            "port": 9088,
            "database": "mydatabase",
            "username": "user",
            "password": "password",
            "server": "ol_server"
        }
