# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.urls import path
from . import views
from .views import TableColumnsDetailView

urlpatterns = [
    path('', views.index, name='index'),
    path('api/login', views.api_login, name='api_login'),
    path('api/test_token', views.test_token),
    path('api/workspaces', views.get_user_workspaces),
    path('api/sqldb/<str:db_name>/tables/', views.TableListByDbNameView.as_view(), name='table-list-by-db-name'),
    path('api/sqldb/<str:db_name>/table/<str:table_name>/columns/', TableColumnsDetailView.as_view(), name='table-columns-detail'),
    # URL for setting the workspace in the session via HTMX
    path('set-workspace/', views.set_workspace_session, name='set_workspace'),
]
