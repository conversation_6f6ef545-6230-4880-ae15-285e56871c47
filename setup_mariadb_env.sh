#!/bin/bash

# Script to set up MariaDB Connector/C environment variables
# This is needed for installing the Python mariadb package

export PATH="/opt/homebrew/opt/mariadb-connector-c/bin:$PATH"
export LDFLAGS="-L/opt/homebrew/opt/mariadb-connector-c/lib"
export CPPFLAGS="-I/opt/homebrew/opt/mariadb-connector-c/include"
export PKG_CONFIG_PATH="/opt/homebrew/opt/mariadb-connector-c/lib/pkgconfig"

echo "MariaDB Connector/C environment variables have been set:"
echo "PATH: $PATH"
echo "LDFLAGS: $LDFLAGS"
echo "CPPFLAGS: $CPPFLAGS"
echo "PKG_CONFIG_PATH: $PKG_CONFIG_PATH"
echo ""
echo "You can now install the mariadb Python package with:"
echo "pip install mariadb"
