# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import logging
import os
from django.utils import timezone
from thoth_core.models import Workspace
from .preprocessing.preprocess import preprocess
from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager
from vdbmanager.impl.QdrantHaystackStore import QdrantHaystackStore
from .backend_utils.vectordb_config_utils import get_vectordb_config

logger = logging.getLogger(__name__)

def run_preprocessing_task(workspace_id):
    """
    The actual preprocessing function that will be run in a background thread.
    """
    try:
        workspace = Workspace.objects.get(id=workspace_id)

        # Set status to RUNNING
        workspace.preprocessing_status = Workspace.PreprocessingStatus.RUNNING
        workspace.last_preprocess_log = "Preprocessing started..."
        workspace.save()

        sql_db_obj = workspace.sql_db
        if not sql_db_obj:
            raise ValueError(f"No database associated with workspace: {workspace.name}")

        sql_db_params = {
            "id": sql_db_obj.id,
            "name": sql_db_obj.name, "db_host": sql_db_obj.db_host, "db_type": sql_db_obj.db_type,
            "db_name": sql_db_obj.db_name, "db_port": sql_db_obj.db_port, "schema": sql_db_obj.schema,
            "user_name": sql_db_obj.user_name, "password": sql_db_obj.password, "db_mode": str(sql_db_obj.db_mode),
            "language": sql_db_obj.language,
        }

        if not sql_db_obj.vector_db:
            raise ValueError(f"No vector database associated with {sql_db_obj.name}")

        vector_db_obj = sql_db_obj.vector_db
        sql_db_params["vector_db"] = {
            "name": vector_db_obj.name, "vect_type": vector_db_obj.vect_type, "host": vector_db_obj.host, "port": vector_db_obj.port,
            "username": vector_db_obj.username, "password": vector_db_obj.password, "api_key": vector_db_obj.api_key,
            "tenant": vector_db_obj.tenant,
        }

        if sql_db_params["db_type"] == "PostgreSQL":
            sql_db = ThothPgManager(
                host=sql_db_params["db_host"], port=sql_db_params["db_port"], schema=sql_db_params["schema"],
                dbname=sql_db_params["db_name"], user=sql_db_params["user_name"], password=sql_db_params["password"],
                db_root_path=os.getenv("DB_ROOT_PATH"), db_mode=str(sql_db_params["db_mode"]),
            )
        elif sql_db_params["db_type"] == "SQLite":
            sql_db = ThothSqliteManager(
                db_id=sql_db_params["db_name"], db_root_path=os.getenv("DB_ROOT_PATH"),
                db_mode=str(sql_db_params["db_mode"]),
            )
        else:
            raise ValueError(f"Unsupported database type: {sql_db_params['db_type']}")

        if not workspace.setting:
            raise ValueError(f"No settings configured for workspace: {workspace.name}")

        setting_obj = workspace.setting
        setting = {
            "signature_size": setting_obj.signature_size, "n_grams": setting_obj.n_grams,
            "threshold": setting_obj.threshold, "verbose": setting_obj.verbose,
            "use_value_description": setting_obj.use_value_description,
        }

        vector_db_config = get_vectordb_config(sql_db_params)
        if vector_db_config["vector_db_type"] == "Qdrant":
            vector_db = QdrantHaystackStore(
                host=vector_db_config["host"], port=vector_db_config["port"],
                collection=vector_db_config.get('collection_name')
            )
        else:
            raise ValueError(f"Unsupported vector database type: {vector_db_config['vector_db_type']}")

        # Run the actual preprocessing
        preprocess(sql_db, vector_db, sql_db_params, setting)

        # If successful, update status and timestamp
        workspace.preprocessing_status = Workspace.PreprocessingStatus.COMPLETED
        workspace.last_preprocess = timezone.now()
        workspace.last_preprocess_log = "Preprocessing completed successfully."
        workspace.task_id = None  # Clear task ID
        workspace.save()
        logger.info(f"Preprocessing completed for workspace {workspace_id}")

    except Exception as e:
        logger.error(f"Error during background preprocessing for workspace {workspace_id}: {e}", exc_info=True)
        # Update status to FAILED and save the error message
        if 'workspace' in locals():
            workspace.preprocessing_status = Workspace.PreprocessingStatus.FAILED
            workspace.last_preprocess_log = f"Error during preprocessing: {e}"
            workspace.task_id = None  # Clear task ID
            workspace.save()
