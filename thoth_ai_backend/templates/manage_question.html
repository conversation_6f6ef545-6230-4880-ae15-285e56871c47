{% extends 'vertical_base.html' %}

{% block title %}{% if question_id %}Update Question{% else %}Add Question{% endif %}{% endblock title %}

{% block content %}
<div class="container-fluid">

    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{% if question_id %}Update Question{% else %}Add New Question{% endif %}</h4>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {# Determine form action URL based on whether it's create or update #}
                    {% if question_id %}
                        <form method="post" action="{% url 'thoth_ai_backend:update_question' question_id=question_id %}">
                    {% else %}
                        <form method="post" action="{% url 'thoth_ai_backend:create_question' %}">
                    {% endif %}
                        {% csrf_token %}

                        {# Display non-field errors #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# Question field #}
                        <div class="mb-3">
                            <label for="{{ form.question.id_for_label }}" class="form-label">{{ form.question.label }}</label>
                            {{ form.question }}
                            {% if form.question.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.question.errors|striptags }}
                                </div>
                            {% endif %}
                        </div>

                        {# SQL field #}
                        <div class="mb-3">
                            <label for="{{ form.sql.id_for_label }}" class="form-label">{{ form.sql.label }}</label>
                            {{ form.sql }}
                            {% if form.sql.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.sql.errors|striptags }}
                                </div>
                            {% endif %}
                        </div>

                        {# Hint field #}
                        <div class="mb-3">
                            <label for="{{ form.hint.id_for_label }}" class="form-label">{{ form.hint.label }}</label>
                            {{ form.hint }}
                            {% if form.hint.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.hint.errors|striptags }}
                                </div>
                            {% endif %}
                        </div>

                        <button type="submit" class="btn btn-primary">{% if question_id %}Update Question{% else %}Save Question{% endif %}</button>
                        <a href="{% url 'thoth_ai_backend:questions' %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div> <!-- end card-body -->
            </div> <!-- end card -->
        </div><!-- end col -->
    </div><!-- end row -->

</div> <!-- container -->
{% endblock content %}
