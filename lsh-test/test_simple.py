#!/usr/bin/env python3
"""
Simple test script for LSH evaluation.
"""

from config_loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from lsh_evaluator import LshEvaluator

def test_lsh():
    """Test LSH functionality."""
    print("🧪 Testing LSH Evaluation")
    print("=" * 40)
    
    try:
        # Load config
        config = ConfigLoader()
        print("✅ Config loaded")
        
        # Initialize LSH evaluator
        lsh_path = config.get_lsh_path()
        evaluator = LshEvaluator(lsh_path)
        print("✅ LSH evaluator initialized")
        
        # Show database info
        db_info = evaluator.get_database_info()
        print(f"\n📊 Database Info:")
        for key, value in db_info.items():
            print(f"   {key}: {value}")
        
        # Test with a simple keyword
        test_keywords = ["school", "student", "california"]
        
        print(f"\n🔍 Testing keywords: {test_keywords}")
        results = evaluator.evaluate_keywords(test_keywords, config.config)
        
        print(f"\n📈 Results Summary:")
        metadata = results['metadata']
        print(f"   Query time: {metadata['query_time']}s")
        print(f"   Total matches: {metadata['total_matches']}")
        print(f"   Unique tables: {len(metadata['unique_tables'])}")
        print(f"   Unique columns: {len(metadata['unique_columns'])}")
        
        # Show some sample results
        print(f"\n📋 Sample Results:")
        for keyword, keyword_results in results['results'].items():
            print(f"   Keyword '{keyword}':")
            if keyword_results:
                for table, columns in list(keyword_results.items())[:2]:  # Show first 2 tables
                    print(f"     Table: {table}")
                    for column, values in list(columns.items())[:2]:  # Show first 2 columns
                        print(f"       Column: {column}")
                        for value in values[:3]:  # Show first 3 values
                            print(f"         - {value}")
            else:
                print(f"       No matches found")
        
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_lsh()