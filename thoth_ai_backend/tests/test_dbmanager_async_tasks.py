import unittest
import os
import django
from pathlib import Path

# Add project root to sys.path
project_root = Path(__file__).resolve().parents[2]
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Thoth.settings')
django.setup()

from unittest.mock import patch, MagicMock
from thoth_ai_backend.async_tasks import run_preprocessing_task
from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager
from thoth_core.models import Workspace

class TestDbManagerAsyncTasks(unittest.TestCase):

    @patch('thoth_ai_backend.async_tasks.ThothPgManager')
    @patch('thoth_ai_backend.async_tasks.ThothSqliteManager')
    @patch('thoth_ai_backend.async_tasks.Workspace.objects.get')
    @patch('thoth_ai_backend.async_tasks.preprocess')
    def test_run_preprocessing_task_postgres(self, mock_preprocess, mock_workspace_get, mock_sqlite_manager, mock_pg_manager):
        # Setup
        mock_workspace = MagicMock()
        mock_workspace.id = 1
        mock_workspace.sql_db = MagicMock()
        mock_workspace.sql_db.db_type = 'PostgreSQL'
        mock_workspace.sql_db.db_host = 'localhost'
        mock_workspace.sql_db.db_port = 5432
        mock_workspace.sql_db.db_name = 'test_db'
        mock_workspace.sql_db.user_name = 'user'
        mock_workspace.sql_db.password = 'pass'
        mock_workspace.sql_db.schema = 'public'
        mock_workspace.sql_db.vector_db = MagicMock()
        mock_workspace.setting = MagicMock()
        mock_workspace.setting.signature_size = 128
        mock_workspace.setting.n_grams = 2
        mock_workspace.setting.threshold = 0.5
        mock_workspace.setting.verbose = True
        mock_workspace.setting.use_value_description = True

        mock_workspace_get.return_value = mock_workspace

        # Execute
        run_preprocessing_task(1)

        # Assert
        mock_pg_manager.assert_called_once_with(
            host='localhost',
            port=5432,
            schema='public',
            dbname='test_db',
            user='user',
            password='pass',
            db_root_path=None,  # This would be set by os.getenv
            db_mode='dev'  # Default mode
        )
        mock_preprocess.assert_called_once()

    @patch('thoth_ai_backend.async_tasks.ThothPgManager')
    @patch('thoth_ai_backend.async_tasks.ThothSqliteManager')
    @patch('thoth_ai_backend.async_tasks.Workspace.objects.get')
    @patch('thoth_ai_backend.async_tasks.preprocess')
    def test_run_preprocessing_task_sqlite(self, mock_preprocess, mock_workspace_get, mock_sqlite_manager, mock_pg_manager):
        # Setup
        mock_workspace = MagicMock()
        mock_workspace.id = 1
        mock_workspace.sql_db = MagicMock()
        mock_workspace.sql_db.db_type = 'SQLite'
        mock_workspace.sql_db.db_name = 'test_db'
        mock_workspace.setting = MagicMock()
        mock_workspace.setting.signature_size = 128
        mock_workspace.setting.n_grams = 2
        mock_workspace.setting.threshold = 0.5
        mock_workspace.setting.verbose = True
        mock_workspace.setting.use_value_description = True

        mock_workspace_get.return_value = mock_workspace

        # Execute
        run_preprocessing_task(1)

        # Assert
        mock_sqlite_manager.assert_called_once_with(
            db_id='test_db',
            db_root_path=None,  # This would be set by os.getenv
            db_mode='dev'  # Default mode
        )
        mock_preprocess.assert_called_once()

if __name__ == '__main__':
    unittest.main()
