# Thoth - ThothAI Backend Application

Thoth is the backend component of ThothAI, an application that enables querying relational databases using natural language. 

![docs/assets/home_Thoth_nocomments.png](docs/assets/home_Thoth_nocomments.png)

ThothAI relies on a Django backend and a frontend composed of a set of AI Agents.

Overall, ThothAI produces:

- **SQL Queries** generated by AI, which, if correct, are automatically executed.
- **Results** of the executed SQL queries, viewable and downloadable in CSV format.
- **Explanations** in non-technical language of the generated SQL (optional).
- **Workflow Details** that allow users to follow the step-by-step process of SQL generation starting from the input question. The specific steps to display are at the user's discretion.

The frontend component is available at [https://github.com/mptyl/ThothSL.git](https://github.com/mptyl/ThothSL.git)

## 1. Preliminary Activities
ThothAI is a fairly complex and articulated application. It requires some setup effort, which we have tried to mitigate through extensive use of default values.

### 1.1 Prerequisites
- Docker and Docker Compose
- Git

To simplify and speed up the installation, the standard procedure to follow is based on Docker. Therefore, if you wish to follow the standard instructions, ensure that <PERSON><PERSON> is installed and that you can successfully execute the `docker-compose` command.

Git is required to clone the application and, if desired, to keep the software updated with `git pull` commands whenever patches or enhancements are released.

The application source code, released under the MIT license, is available on GitHub at the following addresses: [https://github.com/mptyl/Thoth.git](https://github.com/mptyl/Thoth.git) and [https://github.com/mptyl/ThothSL.git](https://github.com/mptyl/ThothSL.git).

**Note**:  
The application can also be compiled and run locally in a typical Python virtual environment (e.g., `venv` or `conda`). 
We do not recommend this solution unless you want to study or customize the code, or if you wish to contribute to its development. 
For standard usage, we strongly recommend using the Docker-based installation as it provides a more reliable and consistent environment with all dependencies properly configured.

## 2. System Architecture

The application consists of four services, each available as a Docker container. The `docker-compose.yml` file defines the configuration of these Docker containers.

The standard configuration assigns the following names to the services within the Docker environment:

### 2.1 thoth-be (Django Backend)
Manages the system's configuration and metadata, which include:
- SQL databases to be queried;
- Vector databases collections (in a 1:1 relationship with SQL databases) containing metadata of the associated relational databases, particularly hints, question/SQL pairs usable as few-shots, and descriptions of tables and columns;
- LLM models usable by ThothAI (OpenAI, Mistral, Anthropic, etc.). Only models capable of interacting with external tools can be used. Most recently released models have this capability;
- Agents (PydanticAI based) used in the SQL generation workflow, each associated with an LLM. Refer to the PydanticAI documentation ([https://ai.pydantic.dev/](https://ai.pydantic.dev/)) for details on the characteristics of their Agents;
- Authorized users and their association with one or more Groups to define access permissions and frontend activity defaults;
- Workspaces, which link a user to a SQL-vector database pair and the agents to be used in the workflow.

### 2.2 thoth-db (PostgreSQL)
This is the internal PostgreSQL database manager of the system. It is used to manage test and example databases sourced from BIRD or other benchmarks. It is accessible at the following addresses:
- **Internal**: `thoth-db:5432`
- **External**: `localhost:5443`
- **Credentials**: The example databases provided have credentials `thoth_user` / `thoth_password`.  
All these parameters can be modified in the `docker-compose.yml` file.

### 2.3 thoth-be-proxy (Nginx)
A proxy for using the Django application in production mode. It handles static files and allows browser access to `thoth-be` at [http://localhost:8040](http://localhost:8040). The port can be changed in the `docker-compose.yml` file.

### 2.4 thoth-qdrant (Vector Database)
Stores metadata of the database that the AI needs to query:
- Hints to be used in the SQL generation process to 'explain' specific terms to the AI;
- Predefined and verified question-to-SQL associations;
- Descriptions of tables and fields, partly extracted from the database schema and partly generated as comments via AI;
- Generic semantic documentation providing high-level guidance on the database and its content;

## 3. Installation

For complete installation instructions, quick start guide, and comprehensive usage documentation, please visit our official documentation at: **[https://mptyl.github.io/ThothDocs/](https://mptyl.github.io/ThothDocs/)**

The documentation includes:
- **Step-by-step installation guide** with detailed setup procedures
- **Quick start tutorial** to get you up and running immediately
- **Complete user manual** covering all ThothAI features and capabilities
- **Configuration examples** and best practices
- **Troubleshooting guide** for common issues

This README provides a basic overview, but the full documentation contains all the detailed information needed to successfully install, configure, and use ThothAI in production environments.

## 4. Useful Links
- **Backend Home Page**: [http://localhost:8040](http://localhost:8040)
- **Admin Panel**: [http://localhost:8040/admin](http://localhost:8040/admin)
- **Frontend Page**: [http://localhost:8501](http://localhost:8501)
- **Qdrant Dashboard**: [http://localhost:6333/dashboard](http://localhost:6333/dashboard)
- **Database from External**: `localhost:5443`