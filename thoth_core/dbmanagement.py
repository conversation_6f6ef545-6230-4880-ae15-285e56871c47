# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import logging
from django.db import IntegrityError
from django.contrib import messages
from .models import SqlTable, SqlColumn, ColumnDataTypes, Relationship

from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager

# Get a logger for this module
logger = logging.getLogger(__name__)

def get_db_manager(sqldb):
    """
    Factory function to get the appropriate ThothDbManager instance based on db_type.
    """
    db_type = sqldb.db_type
    if db_type == 'PostgreSQL':
        return ThothPgManager.get_instance(
            host=sqldb.db_host,
            port=sqldb.db_port,
            dbname=sqldb.db_name,
            user=sqldb.user_name,
            password=sqldb.password,
            db_root_path='data', # Assuming a default path for db_root_path
            db_mode='dev', # Assuming a default mode
            schema=sqldb.schema,
            language='English' # Assuming a default language
        )
    elif db_type == 'SQLite':
        return ThothSqliteManager.get_instance(
            db_id=sqldb.db_name, # For SQLite, db_name can be used as db_id
            db_root_path='data', # Assuming a default path for db_root_path
            db_mode='dev', # Assuming a default mode
            language='English' # Assuming a default language
        )
    else:
        raise NotImplementedError(f"Database type '{db_type}' is not yet supported. To be implemented.")

def map_data_type(data_type):
    data_type = data_type.upper()
    if data_type.startswith('VARCHAR') or data_type.startswith('CHARACTER VARYING'):
        return ColumnDataTypes.VARCHAR
    elif data_type.startswith('CHAR') or data_type.startswith('CHARACTER'):
        return ColumnDataTypes.CHAR
    elif data_type in ['INT', 'INTEGER', 'BIGINT', 'SMALLINT']:
        return ColumnDataTypes.INT
    elif data_type in ['FLOAT', 'REAL']:
        return ColumnDataTypes.FLOAT
    elif data_type == 'DOUBLE PRECISION':
        return ColumnDataTypes.DOUBLE
    elif data_type == 'DECIMAL' or data_type.startswith('NUMERIC'):
        return ColumnDataTypes.DECIMAL
    elif data_type == 'DATE':
        return ColumnDataTypes.DATE
    elif data_type == 'TIME':
        return ColumnDataTypes.TIME
    elif data_type in ['TIMESTAMP', 'TIMESTAMP WITHOUT TIME ZONE', 'TIMESTAMP WITH TIME ZONE']:
        return ColumnDataTypes.TIMESTAMP
    elif data_type == 'BOOLEAN':
        return ColumnDataTypes.BOOLEAN
    elif data_type == 'ENUM':
        return ColumnDataTypes.ENUM
    else:
        return ColumnDataTypes.VARCHAR  # Default to VARCHAR for unknown types

def get_column_names_and_comments(sqldb, table_name):
    try:
        db_manager = get_db_manager(sqldb)
        columns_info = db_manager.get_columns(table_name)

        result = []
        for col_data in columns_info:
            column_name = col_data['name']
            data_type = col_data['data_type']
            comment = col_data.get('comment', '')
            is_pk = col_data.get('is_pk', False)
            result.append((column_name, data_type, comment, is_pk))

        if not result:
            logger.warning(f"No columns found for table {table_name}")

        return result

    except NotImplementedError as e:
        logger.error(f"Database type not supported: {e}")
        raise Exception(f"Database type '{sqldb.db_type}' is not supported")
    except Exception as e:
        logger.exception(f"Error retrieving column names and comments for table {table_name}: {str(e)}")
        raise Exception(f"Connection error: {str(e)}")

def create_sql_columns(sql_table, column_info):
    created_columns = []
    skipped_columns = []
    
    for column_data in column_info:
        column_name = column_data[0]
        data_type = column_data[1]
        comment = column_data[2] if len(column_data) > 2 else None
        is_pk = column_data[3] if len(column_data) > 3 else False
        
        # Verifica se la colonna esiste già
        if SqlColumn.objects.filter(sql_table=sql_table, original_column_name=column_name).exists():
            skipped_columns.append((column_name, data_type, comment))
            continue
        
        # Mappa il tipo di dati
        mapped_data_type = map_data_type(data_type)
        
        # Crea la colonna
        column = SqlColumn(
            sql_table=sql_table,
            original_column_name=column_name,
            column_name=column_name,
            data_format=mapped_data_type,
            column_description=comment or '',
            pk_field='PK' if is_pk else ''  # Imposta 'PK' se la colonna è una chiave primaria
        )
        column.save()
        
        created_columns.append((column_name, data_type, comment))
    
    return created_columns, skipped_columns

def create_columns(modeladmin, request, queryset):
    total_tables = queryset.count()
    total_success = 0
    total_failed = 0
    failed_tables = []
    
    for sql_table in queryset:
        try:
            logger.info(f"SqlTable: {sql_table.name}")
            logger.info(f"SqlDb: {sql_table.sql_db.name}")

            # Get column names and comments
            column_info = get_column_names_and_comments(sql_table.sql_db, sql_table.name)

            if not column_info:
                error_msg = f"No columns found or error occurred for table '{sql_table.name}' in database '{sql_table.sql_db.name}'"
                messages.error(request, error_msg)
                failed_tables.append((sql_table.name, sql_table.sql_db.name, "No columns found or connection error"))
                total_failed += 1
                continue

            logger.info("Columns found:")
            for column, data_type, comment, is_pk in column_info:
                logger.info(f"- {column} ({data_type}) (Comment: {comment or 'None'}) (PK: {'Yes' if is_pk else 'No'})")

            # Create SqlColumn records
            created_columns, skipped_columns = create_sql_columns(sql_table, column_info)

            logger.info("Columns created:")
            for column, data_type, comment in created_columns:
                logger.info(f"- {column} (Comment: {comment or 'None'})")

            logger.info("Columns skipped (already existing):")
            for column, data_type, comment in skipped_columns:
                logger.info(f"- {column} (Comment: {comment or 'None'})")

            logger.info("--------------------")
            
            # Success message
            messages.success(
                request,
                f"Successfully processed table '{sql_table.name}' in database '{sql_table.sql_db.name}': {len(created_columns)} columns created, {len(skipped_columns)} columns skipped"
            )
            total_success += 1
            
        except Exception as e:
            error_msg = f"Failed to process table '{sql_table.name}' in database '{sql_table.sql_db.name}': {str(e)}"
            logger.error(error_msg)
            messages.error(request, error_msg)
            failed_tables.append((sql_table.name, sql_table.sql_db.name, str(e)))
            total_failed += 1
    
    # Summary message
    if total_failed > 0:
        messages.warning(
            request,
            f"Task completed with {total_success} successes and {total_failed} failures out of {total_tables} tables"
        )
    else:
        messages.success(
            request,
            f"Successfully processed all {total_tables} tables"
        )

create_columns.short_description = "Create columns for selected tables"

def get_table_names_and_comments(sqldb):
    try:
        db_manager = get_db_manager(sqldb)
        tables_info = db_manager.get_tables()

        result = []
        for table_data in tables_info:
            table_name = table_data['name']
            comment = table_data.get('comment', '')
            result.append((table_name, comment))

        return result

    except NotImplementedError as e:
        logger.error(f"Database type not supported: {e}")
        raise Exception(f"Database type '{sqldb.db_type}' is not supported")
    except Exception as e:
        logger.exception(f"Error retrieving table names and comments: {str(e)}")
        raise Exception(f"Connection error: {str(e)}")

def create_sql_tables(sqldb, table_info):
    created_tables = []
    skipped_tables = []

    for table_name, description in table_info:
        try:
            sql_table, created = SqlTable.objects.get_or_create(
                name=table_name,
                sql_db=sqldb,
                defaults={'description': description or ''}
            )
            if created:
                created_tables.append((table_name, description))
            else:
                # Update the comment if the table already exists
                if sql_table.description != description:
                    sql_table.description = description
                    sql_table.save()
                skipped_tables.append((table_name, description))
        except IntegrityError:
            skipped_tables.append((table_name, description))

    return created_tables, skipped_tables

def create_relationships(modeladmin, request, queryset):
    if not hasattr(queryset, '__iter__'):
        sqldb_list = [queryset]
    else:
        sqldb_list = queryset
    
    total_databases = len(sqldb_list)
    total_success = 0
    total_failed = 0
    failed_databases = []
    total_relationships_created = 0
    
    for sqldb in sqldb_list:
        try:
            db_manager = get_db_manager(sqldb)
            relationships_info = db_manager.get_foreign_keys()
            
            if not relationships_info:
                messages.warning(
                    request,
                    f"No foreign key relationships found in database '{sqldb.name}'"
                )
                total_success += 1
                continue

            relationships_created = 0
            relationships_failed = 0
            
            for rel_data in relationships_info:
                source_table_name = rel_data['source_table_name']
                source_column_name = rel_data['source_column_name']
                target_table_name = rel_data['target_table_name']
                target_column_name = rel_data['target_column_name']

                try:
                    source_table = SqlTable.objects.get(name=source_table_name, sql_db=sqldb)
                    target_table = SqlTable.objects.get(name=target_table_name, sql_db=sqldb)
                    source_column = SqlColumn.objects.get(original_column_name=source_column_name, sql_table=source_table)
                    target_column = SqlColumn.objects.get(original_column_name=target_column_name, sql_table=target_table)

                    relationship, created = Relationship.objects.get_or_create(
                        source_table=source_table,
                        target_table=target_table,
                        source_column=source_column,
                        target_column=target_column
                    )
                    if created:
                        relationships_created += 1
                    logger.info(f"Created/Updated relationship: {source_table_name}.{source_column_name} -> {target_table_name}.{target_column_name}")
                except SqlTable.DoesNotExist:
                    error_msg = f"Table not found in database '{sqldb.name}' - Source: {source_table_name} or Target: {target_table_name}"
                    logger.error(error_msg)
                    relationships_failed += 1
                except SqlColumn.DoesNotExist:
                    error_msg = f"Column not found in database '{sqldb.name}' - Source: {source_column_name} or Target: {target_column_name}"
                    logger.error(error_msg)
                    relationships_failed += 1
                except Exception as e:
                    error_msg = f"Error creating relationship in database '{sqldb.name}': {str(e)}"
                    logger.error(error_msg)
                    relationships_failed += 1

            Relationship.update_pk_fk_fields()
            logger.info("Relationships created and pk_field/fk_field updated.")
            
            # Success message
            messages.success(
                request,
                f"Successfully processed relationships for database '{sqldb.name}': {relationships_created} relationships created"
            )
            total_relationships_created += relationships_created
            total_success += 1
            
        except NotImplementedError as e:
            error_msg = f"Database type not supported for '{sqldb.name}': {str(e)}"
            logger.error(error_msg)
            messages.error(request, error_msg)
            failed_databases.append((sqldb.name, str(e)))
            total_failed += 1
        except Exception as e:
            error_msg = f"Error retrieving foreign key relationships for database '{sqldb.name}': {str(e)}"
            logger.error(error_msg)
            messages.error(request, error_msg)
            failed_databases.append((sqldb.name, str(e)))
            total_failed += 1
    
    # Summary message
    if total_failed > 0:
        messages.warning(
            request,
            f"Task completed with {total_success} successes and {total_failed} failures out of {total_databases} databases. Total relationships created: {total_relationships_created}"
        )
    else:
        messages.success(
            request,
            f"Successfully processed all {total_databases} databases. Total relationships created: {total_relationships_created}"
        )

def create_tables(modeladmin, request, queryset):
    total_databases = queryset.count()
    total_success = 0
    total_failed = 0
    failed_databases = []
    
    for sqldb in queryset:
        try:
            logger.info(f"SqlDb: {sqldb.name}")
            logger.info(f"  Host: {sqldb.db_host}")
            logger.info(f"  Type: {sqldb.db_type}")
            logger.info(f"  Database Name: {sqldb.db_name}")
            logger.info(f"  Port: {sqldb.db_port}")
            logger.info(f"  Schema: {sqldb.schema}")
            logger.info(f"  Username: {sqldb.user_name}")
            logger.info(f"  Password: {'*' * len(sqldb.password)}")
            logger.info(f"  Vector DB: {sqldb.vector_db}")

            # Retrieve table names and comments
            table_info = get_table_names_and_comments(sqldb)

            if not table_info:
                error_msg = f"Failed to retrieve tables from database '{sqldb.name}' - connection error or no tables found"
                messages.error(request, error_msg)
                failed_databases.append((sqldb.name, "Connection error or no tables found"))
                total_failed += 1
                continue

            logger.info("  Tables found:")
            for table, comment in table_info:
                logger.info(f"    - {table} (Comment: {comment or 'None'})")
            logger.info("--------------------")

            # Create SqlTable records
            created_tables, skipped_tables = create_sql_tables(sqldb, table_info)

            logger.info("  Tables created:")
            for table, comment in created_tables:
                logger.info(f"    - {table} (Comment: {comment or 'None'})")

            logger.info("  Tables skipped (already existing):")
            for table, comment in skipped_tables:
                logger.info(f"    - {table} (Comment: {comment or 'None'})")

            logger.info("--------------------")
            logger.info("====================")
            
            # Success message
            messages.success(
                request,
                f"Successfully processed database '{sqldb.name}': {len(created_tables)} tables created, {len(skipped_tables)} tables skipped"
            )
            total_success += 1
            
        except Exception as e:
            error_msg = f"Failed to process database '{sqldb.name}': {str(e)}"
            logger.error(error_msg)
            messages.error(request, error_msg)
            failed_databases.append((sqldb.name, str(e)))
            total_failed += 1
    
    # Summary message
    if total_failed > 0:
        messages.warning(
            request,
            f"Task completed with {total_success} successes and {total_failed} failures out of {total_databases} databases"
        )
    else:
        messages.success(
            request,
            f"Successfully processed all {total_databases} databases"
        )

def create_db_elements(modeladmin, request, queryset):
    total_databases = queryset.count()
    total_success = 0
    total_failed = 0
    failed_databases = []
    
    for sqldb in queryset:
        try:
            logger.info(f"Processing SqlDb: {sqldb.name}")
            
            # Step 1: Create all tables
            logger.info("Step 1: Creating tables")
            table_info = get_table_names_and_comments(sqldb)
            if not table_info:
                raise Exception("Failed to retrieve table information")
                
            created_tables, skipped_tables = create_sql_tables(sqldb, table_info)
            
            logger.info("Tables created:")
            for table, comment in created_tables:
                logger.info(f"  - {table} (Comment: {comment or 'None'})")
            logger.info("Tables skipped (already existing):")
            for table, comment in skipped_tables:
                logger.info(f"  - {table} (Comment: {comment or 'None'})")
            
            # Step 2: Create columns for each table
            logger.info("\nStep 2: Creating columns for each table")
            tables_processed = 0
            tables_failed = 0
            
            for sql_table in SqlTable.objects.filter(sql_db=sqldb):
                try:
                    logger.info(f"Processing table: {sql_table.name}")
                    column_info = get_column_names_and_comments(sqldb, sql_table.name)
                    if not column_info:
                        logger.warning(f"No columns found for table {sql_table.name}")
                        continue
                        
                    created_columns, skipped_columns = create_sql_columns(sql_table, column_info)
                    tables_processed += 1
                except Exception as e:
                    logger.error(f"Error processing table {sql_table.name}: {str(e)}")
                    tables_failed += 1
            
            # Step 3: Create foreign key relationships
            logger.info("\nStep 3: Creating foreign key relationships")
            try:
                db_manager = get_db_manager(sqldb)
                relationships_info = db_manager.get_foreign_keys()
                
                relationships_created = 0
                for rel_data in relationships_info:
                    source_table_name = rel_data['source_table_name']
                    source_column_name = rel_data['source_column_name']
                    target_table_name = rel_data['target_table_name']
                    target_column_name = rel_data['target_column_name']

                    try:
                        source_table = SqlTable.objects.get(name=source_table_name, sql_db=sqldb)
                        target_table = SqlTable.objects.get(name=target_table_name, sql_db=sqldb)
                        source_column = SqlColumn.objects.get(original_column_name=source_column_name, sql_table=source_table)
                        target_column = SqlColumn.objects.get(original_column_name=target_column_name, sql_table=target_table)

                        relationship, created = Relationship.objects.get_or_create(
                            source_table=source_table,
                            target_table=target_table,
                            source_column=source_column,
                            target_column=target_column
                        )
                        if created:
                            relationships_created += 1
                    except Exception as e:
                        logger.warning(f"Error creating relationship: {str(e)}")

                Relationship.update_pk_fk_fields()
                logger.info("Relationships created and pk_field/fk_field updated.")
                
                # Success message
                messages.success(
                    request,
                    f"Successfully processed database '{sqldb.name}': {len(created_tables)} tables, {tables_processed} tables with columns, {relationships_created} relationships"
                )
                total_success += 1
                
            except Exception as e:
                raise Exception(f"Error creating relationships: {str(e)}")
                
        except Exception as e:
            error_msg = f"Failed to process database '{sqldb.name}': {str(e)}"
            logger.error(error_msg)
            messages.error(request, error_msg)
            failed_databases.append((sqldb.name, str(e)))
            total_failed += 1
    
    # Summary message
    if total_failed > 0:
        messages.warning(
            request,
            f"Task completed with {total_success} successes and {total_failed} failures out of {total_databases} databases"
        )
        for db_name, error in failed_databases:
            messages.error(request, f"  - {db_name}: {error}")
    else:
        messages.success(
            request,
            f"Successfully processed all {total_databases} databases"
        )

create_db_elements.short_description = "Create all the database elements (tables, columns, and relationships)"
