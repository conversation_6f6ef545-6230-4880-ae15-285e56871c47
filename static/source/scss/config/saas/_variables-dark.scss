// Dark color mode variables
//
// Custom variables for the `[data-bs-theme="dark"]` theme. Use this as a starting point for your own custom color modes by creating a new theme-specific file like `_variables-dark.scss` and adding the variables you need.

//
// Global Colors
//

// scss-docs-start sass-dark-mode-vars
$primary-text-emphasis-dark:                 tint-color($primary, 40%);
$secondary-text-emphasis-dark:               tint-color($secondary, 40%);
$success-text-emphasis-dark:                 tint-color($success, 40%);
$info-text-emphasis-dark:                    tint-color($info, 40%);
$warning-text-emphasis-dark:                 tint-color($warning, 40%);
$danger-text-emphasis-dark:                  tint-color($danger, 40%);
$light-text-emphasis-dark:                   $gray-100;
$dark-text-emphasis-dark:                    $gray-300;
// scss-docs-end theme-text-dark-variables

// scss-docs-start theme-bg-subtle-dark-variables
$primary-bg-subtle-dark:            rgba($primary, 15%);
$secondary-bg-subtle-dark:          rgba($gray-700, 15%);
$success-bg-subtle-dark:            rgba($success, 15%);
$info-bg-subtle-dark:               rgba($info, 15%);
$warning-bg-subtle-dark:            rgba($warning, 15%);
$danger-bg-subtle-dark:             rgba($danger, 15%);
$light-bg-subtle-dark:              rgba(var(--#{$prefix}light-rgb), .15);
$dark-bg-subtle-dark:               rgba(var(--#{$prefix}dark-rgb), .15);
// scss-docs-end theme-bg-subtle-dark-variables

// scss-docs-start theme-border-subtle-dark-variables
$primary-border-subtle-dark:        shade-color($primary, 40%);
$secondary-border-subtle-dark:      $gray-700;
$success-border-subtle-dark:        shade-color($success, 40%);
$info-border-subtle-dark:           shade-color($info, 40%);
$warning-border-subtle-dark:        shade-color($warning, 40%);
$danger-border-subtle-dark:         shade-color($danger, 40%);
$light-border-subtle-dark:          $gray-700;
$dark-border-subtle-dark:           $gray-500;
// scss-docs-end theme-border-subtle-dark-variables

$body-color-dark:                   #aab8c5;
$body-bg-dark:                      $gray-800;
$body-secondary-color-dark:         #8391a2;
$body-secondary-bg-dark:            #37404a;
$body-tertiary-color-dark:          #f1f1f1;
$body-tertiary-bg-dark:             #404954;
$body-emphasis-color-dark:          #dee2e6;
$border-color-dark:                 #464f5b;
$border-color-translucent-dark:     #8391a2;
$headings-color-dark:               $body-color-dark;
$link-color-dark:                   tint-color($primary, 40%);
$link-hover-color-dark:             shift-color($link-color-dark, -$link-shade-percentage);
$code-color-dark:                   tint-color($code-color, 40%);
$mark-color-dark:                   $body-color-dark;
$mark-bg-dark:                      $yellow-800;


//
// Forms
//

$form-select-indicator-color-dark:  $body-color-dark;
$form-select-indicator-dark:        url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color-dark}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>");

$form-switch-color-dark:            rgba($white, .25);
$form-switch-bg-image-dark:         url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color-dark}'/></svg>");

// scss-docs-start form-validation-colors-dark
$form-valid-color-dark:             $green-300;
$form-valid-border-color-dark:      $green-300;
$form-invalid-color-dark:           $red-300;
$form-invalid-border-color-dark:    $red-300;
// scss-docs-end form-validation-colors-dark


//
// Accordion
//

$accordion-icon-color-dark:         $primary-text-emphasis-dark;
$accordion-icon-active-color-dark:  $primary-text-emphasis-dark;

$accordion-button-icon-dark:         url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color-dark}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>");
$accordion-button-active-icon-dark:  url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color-dark}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>");
// scss-docs-end sass-dark-mode-vars
