# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.contrib.auth.models import User, Group
from .models import  Workspace, SqlDb, VectorDb, AiModel, BasicAiModel, SqlTable, SqlColumn, Setting, Agent, GroupProfile
from rest_framework import serializers
from thoth_core.models import GroupProfile

class GroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = Group
        fields = ['id', 'name']

class UserSerializer(serializers.ModelSerializer):
    groups = serializers.SerializerMethodField()
    group_profiles = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'groups', 'group_profiles']
        # Escludiamo campi sensibili come la password

    def get_groups(self, obj):
        return list(obj.groups.values_list('name', flat=True))
    
    def get_group_profiles(self, obj):
        profiles = []
        for group in obj.groups.all():
            # Refresh the group profile from the database to ensure we get the latest data
            try:
                # Get a fresh instance of the profile directly from the database
                profile = GroupProfile.objects.get(group=group)
                profiles.append({
                    'group_id': group.id,
                    'group_name': group.name,
                    'show_sql': profile.show_sql,
                    'show_keywords': profile.show_keywords,
                    'show_hints': profile.show_hints,
                    'show_process_info': profile.show_process_info,
                    'show_sql_generation': profile.show_sql_generation,
                    'explain_generated_query': profile.explain_generated_query,
                })
            except GroupProfile.DoesNotExist:
                # Handle the case where a group doesn't have a profile
                pass
        return profiles

class VectorDbSerializer(serializers.ModelSerializer):
    class Meta:
        model = VectorDb
        fields = '__all__'

class SqlDbSerializer(serializers.ModelSerializer):
    vector_db = VectorDbSerializer()

    class Meta:
        model = SqlDb
        fields = '__all__'

class BasicAiModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = BasicAiModel
        fields = '__all__'

class AiModelSerializer(serializers.ModelSerializer):
    basic_model = BasicAiModelSerializer()

    class Meta:
        model = AiModel
        fields = '__all__'

class AgentSerializer(serializers.ModelSerializer):
    ai_model = AiModelSerializer()

    class Meta:
        model = Agent
        fields = ['id', 'name', 'agent_type', 'ai_model', 'temperature', 'top_p', 'max_tokens', 'timeout', 'system_prompt', 'user_prompt', 'retries']

class SettingSerializer(serializers.ModelSerializer):
    comment_model = AiModelSerializer()

    class Meta:
        model = Setting
        fields = '__all__'

class WorkspaceSerializer(serializers.ModelSerializer):
    users = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    sql_db = SqlDbSerializer()
    default_workspace = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    default_agent = AgentSerializer()
    question_validator = AgentSerializer()
    kw_sel_agent_1 = AgentSerializer()
    kw_sel_agent_2 = AgentSerializer()
    sel_columns_agent_1 = AgentSerializer()
    sel_columns_agent_2 = AgentSerializer()
    sql_basic_agent = AgentSerializer()
    sql_advanced_agent = AgentSerializer()
    sql_expert_agent = AgentSerializer()
    sql_titan_agent_1 = AgentSerializer()  # Changed from sql_titan_agent to sql_titan_agent_1
    sql_titan_agent_2 = AgentSerializer()  # Added new field
    sql_titan_agent_3 = AgentSerializer()  # Added new field
    test_gen_agent_1 = AgentSerializer()
    test_gen_agent_2 = AgentSerializer()
    test_exec_agent = AgentSerializer()
    explain_sql_agent = AgentSerializer()
    ask_human_help_agent = AgentSerializer()
    explanation_ai_model = AiModelSerializer()
    plotly_generator_ai_model = AiModelSerializer()
    setting = SettingSerializer()

    class Meta:
        model = Workspace
        fields = [
            'id',
            'name',
            'level',
            'description',
            'sql_db',
            'setting',
            'default_agent',
            'question_validator',
            'kw_sel_agent_1',
            'kw_sel_agent_2',
            'sel_columns_agent_1',
            'sel_columns_agent_2',
            'sql_basic_agent',
            'sql_advanced_agent',
            'sql_expert_agent',
            'sql_titan_agent_1',  # Changed from sql_titan_agent to sql_titan_agent_1
            'sql_titan_agent_2',  # Added new field
            'sql_titan_agent_3',  # Added new field
            'test_gen_agent_1',
            'test_gen_agent_2',
            'test_exec_agent',
            'explain_sql_agent',
            'ask_human_help_agent',
            'explanation_ai_model',
            'plotly_generator_ai_model',
            'default_workspace',
            'users',
            'created_at',
            'updated_at'
        ]


class SqlTableSerializer(serializers.ModelSerializer):
    class Meta:
        model = SqlTable
        fields = '__all__'

class SqlColumnSerializer(serializers.ModelSerializer):
    class Meta:
        model = SqlColumn
        fields = ['id', 'original_column_name', 'column_description', 'data_format', 'generated_comment','value_description', 'pk_field', 'fk_field']


class SqlFullTableSerializer(serializers.ModelSerializer):
    columns = SqlColumnSerializer(many=True, read_only=True, source='sqlcolumn_set')

    class Meta:
        model = SqlTable
        fields = ['id', 'name', 'generated_comment', 'columns']


class SqlTableUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = SqlTable
        fields = ['generated_comment']

class SqlColumnUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    generated_comment = serializers.CharField(allow_blank=True)

class SqlColumnBulkUpdateSerializer(serializers.Serializer):
    columns = SqlColumnUpdateSerializer(many=True)

class GroupProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = GroupProfile
        fields = ['show_sql', 'show_keywords', 'show_hints', 'show_process_info', 
                 'show_sql_generation', 'explain_generated_query']
