{% extends "account/base.html" %}

{% load i18n %}
{% load crispy_forms_tags %}

{% block head_title %}{% trans "Change Password" %}{% endblock %}

{% block page_content %}
    <h1>{% trans "Change Password" %}</h1>

    <form method="POST" action="{% url 'account_change_password' %}" class="password_change" novalidate>
        {% csrf_token %}
        {{ form|crispy }}
        <button class="btn btn-primary" type="submit" name="action">{% trans "Change Password" %}</button>
    </form>
{% endblock %}
