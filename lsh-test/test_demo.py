"""
Simple demo script to test the LSH evaluation tool without interactive mode.
"""

import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config_loader import Config<PERSON>oader
from keyword_extractor import KeywordExtractor
from lsh_evaluator import LshEvaluator
from results_formatter import ResultsF<PERSON>atter


def run_demo():
    """Run a simple demo of the LSH evaluation tool."""
    print("🎯 LSH Evaluation Tool Demo")
    print("=" * 40)
    
    try:
        # Load configuration
        config = ConfigLoader()
        if not config.validate():
            print("❌ Configuration validation failed")
            return
        
        # Initialize components
        keyword_extractor = KeywordExtractor(
            api_key=config.get('OPENROUTER_API_KEY'),
            api_base=config.get('OPENROUTER_API_BASE')
        )
        
        lsh_path = config.get_lsh_path()
        lsh_evaluator = LshEvaluator(lsh_path)
        formatter = ResultsFormatter()
        
        # Show database info
        db_info = lsh_evaluator.get_database_info()
        print(formatter.format_database_info(db_info))
        print()
        
        # Test with a sample question
        sample_questions = [
            "Find columns related to student performance and grades",
            "Show me contact information for schools",
            "List demographic information columns"
        ]
        
        for question in sample_questions:
            print(f"📝 Question: {question}")
            
            # Extract keywords
            keywords = keyword_extractor.extract_keywords(question)
            print(f"🔑 Keywords: {', '.join(keywords)}")
            
            # Evaluate
            results = lsh_evaluator.evaluate_keywords(keywords, config.config)
            
            # Show summary
            formatter.print_summary(results)
            print()
        
        print("✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    run_demo()
