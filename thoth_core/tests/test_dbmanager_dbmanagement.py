import unittest
import os
import django
from pathlib import Path

# Add project root to sys.path
project_root = Path(__file__).resolve().parents[2]
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Thoth.settings')
django.setup()

from unittest.mock import patch, MagicMock
from thoth_core.dbmanagement import get_db_manager, get_column_names_and_comments, get_table_names_and_comments, create_relationships
from dbmanager.impl.ThothPgManager import ThothPgManager
from dbmanager.impl.ThothSqliteManager import ThothSqliteManager
from thoth_core.models import SqlDb, SqlTable, SqlColumn, Relationship

class TestDbManagerDbManagement(unittest.TestCase):

    @patch('thoth_core.dbmanagement.ThothPgManager.get_instance')
    def test_get_db_manager_postgres(self, mock_pg_manager_get_instance):
        # Setup
        mock_sqldb = MagicMock()
        mock_sqldb.db_type = 'PostgreSQL'
        mock_sqldb.db_host = 'localhost'
        mock_sqldb.db_port = 5432
        mock_sqldb.db_name = 'test_db'
        mock_sqldb.user_name = 'user'
        mock_sqldb.password = 'pass'
        mock_sqldb.schema = 'public'

        # Execute
        db_manager = get_db_manager(mock_sqldb)

        # Assert
        mock_pg_manager_get_instance.assert_called_once_with(
            host='localhost',
            port=5432,
            dbname='test_db',
            user='user',
            password='pass',
            db_root_path='data',
            db_mode='dev',
            schema='public',
            language='English'
        )
        self.assertIsInstance(db_manager, ThothPgManager)

    @patch('thoth_core.dbmanagement.ThothSqliteManager.get_instance')
    def test_get_db_manager_sqlite(self, mock_sqlite_manager_get_instance):
        # Setup
        mock_sqldb = MagicMock()
        mock_sqldb.db_type = 'SQLite'
        mock_sqldb.db_name = 'test_db'

        # Execute
        db_manager = get_db_manager(mock_sqldb)

        # Assert
        mock_sqlite_manager_get_instance.assert_called_once_with(
            db_id='test_db',
            db_root_path='data',
            db_mode='dev',
            language='English'
        )
        self.assertIsInstance(db_manager, ThothSqliteManager)

    @patch('thoth_core.dbmanagement.get_db_manager')
    def test_get_column_names_and_comments(self, mock_get_db_manager):
        # Setup
        mock_sqldb = MagicMock()
        mock_db_manager = MagicMock()
        mock_db_manager.get_columns.return_value = [
            {'name': 'column1', 'data_type': 'VARCHAR', 'comment': 'Test column'},
            {'name': 'column2', 'data_type': 'INT', 'is_pk': True}
        ]
        mock_get_db_manager.return_value = mock_db_manager

        # Execute
        result = get_column_names_and_comments(mock_sqldb, 'test_table')

        # Assert
        mock_get_db_manager.assert_called_once_with(mock_sqldb)
        mock_db_manager.get_columns.assert_called_once_with('test_table')
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], ('column1', 'VARCHAR', 'Test column', False))
        self.assertEqual(result[1], ('column2', 'INT', '', True))

    @patch('thoth_core.dbmanagement.get_db_manager')
    def test_get_table_names_and_comments(self, mock_get_db_manager):
        # Setup
        mock_sqldb = MagicMock()
        mock_db_manager = MagicMock()
        mock_db_manager.get_tables.return_value = [
            {'name': 'table1', 'comment': 'Test table'},
            {'name': 'table2', 'comment': ''}
        ]
        mock_get_db_manager.return_value = mock_db_manager

        # Execute
        result = get_table_names_and_comments(mock_sqldb)

        # Assert
        mock_get_db_manager.assert_called_once_with(mock_sqldb)
        mock_db_manager.get_tables.assert_called_once()
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], ('table1', 'Test table'))
        self.assertEqual(result[1], ('table2', ''))

    @patch('thoth_core.dbmanagement.get_db_manager')
    @patch('thoth_core.dbmanagement.SqlTable.objects.get')
    @patch('thoth_core.dbmanagement.SqlColumn.objects.get')
    @patch('thoth_core.dbmanagement.Relationship.objects.get_or_create')
    def test_create_relationships(self, mock_relationship_get_or_create, mock_column_get, mock_table_get, mock_get_db_manager):
        # Setup
        mock_sqldb = MagicMock()
        mock_db_manager = MagicMock()
        mock_db_manager.get_foreign_keys.return_value = [
            {
                'source_table_name': 'table1',
                'source_column_name': 'column1',
                'target_table_name': 'table2',
                'target_column_name': 'column2'
            }
        ]
        mock_get_db_manager.return_value = mock_db_manager

        mock_table_get.return_value = MagicMock()
        mock_column_get.return_value = MagicMock()

        # Execute
        create_relationships(MagicMock(), MagicMock(), [mock_sqldb])

        # Assert
        mock_get_db_manager.assert_called_once_with(mock_sqldb)
        mock_db_manager.get_foreign_keys.assert_called_once()
        mock_table_get.assert_called()
        mock_column_get.assert_called()
        mock_relationship_get_or_create.assert_called_once()

if __name__ == '__main__':
    unittest.main()
