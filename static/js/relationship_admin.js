(function($) {
    // Debug function to log messages to console
    function debug(message) {
        console.log("[Relationship Admin Debug] " + message);
    }

    $(document).ready(function() {
        debug("Relationship Admin JS loaded");
        
        // Cache the select elements - use more specific selectors
        const sourceTableSelect = $('#id_source_table');
        const targetTableSelect = $('#id_target_table');
        const sourceColumnSelect = $('#id_source_column');
        const targetColumnSelect = $('#id_target_column');
        
        debug("Source table select found: " + (sourceTableSelect.length > 0));
        debug("Target table select found: " + (targetTableSelect.length > 0));
        debug("Source column select found: " + (sourceColumnSelect.length > 0));
        debug("Target column select found: " + (targetColumnSelect.length > 0));
        
        // Store all options for later filtering
        const allSourceColumnOptions = sourceColumnSelect.find('option').clone();
        const allTargetColumnOptions = targetColumnSelect.find('option').clone();
        
        debug("Source column options count: " + allSourceColumnOptions.length);
        debug("Target column options count: " + allTargetColumnOptions.length);
        
        // Function to filter columns based on selected table
        function filterColumns(tableSelect, columnSelect, allOptions, isSource) {
            const selectedTableId = tableSelect.val();
            debug((isSource ? "Source" : "Target") + " table selected: " + selectedTableId);
            
            if (!selectedTableId) {
                debug("No table selected, showing empty column select");
                // If no table is selected, show only the empty option
                columnSelect.empty().append('<option value="">---------</option>');
                return;
            }
            
            // Get the selected table name
            const selectedTableName = tableSelect.find('option:selected').text().trim();
            debug("Selected table name: " + selectedTableName);
            
            // Clear the column select and add the empty option
            columnSelect.empty().append('<option value="">---------</option>');
            
            let matchCount = 0;
            
            // Filter options based on the selected table
            allOptions.each(function() {
                const option = $(this);
                const optionText = option.text().trim();
                const optionValue = option.val();
                
                // Skip the empty option
                if (!optionValue || optionText === '---------') {
                    return;
                }
                
                // Extract table name from the option text (format: "table.column")
                const parts = optionText.split('.');
                if (parts.length < 2) {
                    debug("Invalid option format: " + optionText);
                    return;
                }
                
                const tableName = parts[0].trim();
                debug("Checking option: " + optionText + " (table: " + tableName + ")");
                
                // If the option belongs to the selected table, add it to the column select
                if (tableName === selectedTableName) {
                    debug("Match found: " + optionText);
                    
                    // Create a clone of the option
                    const newOption = option.clone();
                    
                    // Extract just the column name (after the dot)
                    const columnName = parts[1].trim();
                    
                    // Set the text to just the column name, but keep the original value
                    newOption.text(columnName);
                    
                    // Add the modified option to the select
                    columnSelect.append(newOption);
                    matchCount++;
                }
            });
            
            debug("Total matches found: " + matchCount);
            
            // Add help text if no columns are available
            if (matchCount === 0) {
                const helpText = isSource ? 
                    "Nessuna colonna disponibile per questa tabella di origine" : 
                    "Nessuna colonna disponibile per questa tabella di destinazione";
                
                // Add a disabled option with the help text
                columnSelect.append(`<option disabled>${helpText}</option>`);
            }
        }
        
        // Initial filtering on page load
        if (sourceTableSelect.val()) {
            debug("Initial filtering for source table");
            filterColumns(sourceTableSelect, sourceColumnSelect, allSourceColumnOptions, true);
        }
        
        if (targetTableSelect.val()) {
            debug("Initial filtering for target table");
            filterColumns(targetTableSelect, targetColumnSelect, allTargetColumnOptions, false);
        }
        
        // Add event listeners for table select changes
        sourceTableSelect.on('change', function() {
            debug("Source table changed");
            filterColumns(sourceTableSelect, sourceColumnSelect, allSourceColumnOptions, true);
        });
        
        targetTableSelect.on('change', function() {
            debug("Target table changed");
            filterColumns(targetTableSelect, targetColumnSelect, allTargetColumnOptions, false);
        });
        
        debug("Event listeners attached");
    });
})(django.jQuery);
