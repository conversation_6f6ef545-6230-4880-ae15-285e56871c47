# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import logging

from django.contrib.auth.models import User
from django.shortcuts import get_object_or_404, render, redirect
from django.http import HttpResponse, HttpResponseBadRequest, HttpResponseForbidden
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authtoken.models import Token
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import generics, status

from thoth_core.authentication import ApiKeyAuthentication
from thoth_core.permissions import HasValidApiKey

from .models import SqlColumn, SqlDb, SqlTable, Workspace
from .serializers import SqlColumnSerializer, SqlTableSerializer, UserSerializer, WorkspaceSerializer

logger = logging.getLogger(__name__)
# Create your views here.
@login_required
def index(request):
    context={}
    return render(request, 'index.html', context)

@login_required
@require_POST
def set_workspace_session(request):
    """
    Sets the selected workspace ID in the user's session.
    Called via HTMX from the workspace select dropdown.
    """
    workspace_id = request.POST.get('workspace_id')
    if not workspace_id:
        return HttpResponseBadRequest("Missing workspace_id")

    try:
        # Validate that the workspace exists and the user has access to it
        workspace = Workspace.objects.get(pk=workspace_id, users=request.user)
        request.session['selected_workspace_id'] = workspace.pk
        logger.info(f"User {request.user.username} set workspace {workspace.pk} in session.")
        # Return 204 No Content, HTMX doesn't need a response body here
        return HttpResponse(status=204)
    except Workspace.DoesNotExist:
        logger.warning(f"User {request.user.username} tried to set invalid or inaccessible workspace {workspace_id}.")
        return HttpResponseForbidden("Invalid or inaccessible workspace")
    except Exception as e:
        logger.error(f"Error setting workspace in session for user {request.user.username}: {e}")
        return HttpResponse(status=500)


@api_view(['GET'])
@authentication_classes([SessionAuthentication, TokenAuthentication])
@permission_classes([IsAuthenticated])
def test_token(request):
    return Response("passed!")

@api_view(['POST'])
@authentication_classes([])  # Empty list means no authentication required
@permission_classes([])      # Empty list means no permissions required
def api_login(request):
    # Stampa i dati della richiesta per debug
    try:
        # Verifica che username e password siano presenti nella richiesta
        if 'username' not in request.data or 'password' not in request.data:
            return Response(
                {"error": "Username and password are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        # Ottieni l'utente o restituisci 404
        user = get_object_or_404(User, username=request.data['username'])

        # Verifica la password
        if not user.check_password(request.data['password']):
            return Response(
                {"error": "Invalid credentials"}, 
                status=status.HTTP_401_UNAUTHORIZED
            )
        token, created = Token.objects.get_or_create(user=user)
        serializer = UserSerializer(user)
        return Response({'token': token.key, 'user': serializer.data})
    
    except Exception as e:
        return Response(
            {"error": "An unexpected error occurred", "details": str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@authentication_classes([TokenAuthentication, ApiKeyAuthentication])
@permission_classes([IsAuthenticated | HasValidApiKey])
def get_user_workspaces(request):
    # Verifica se l'autenticazione è avvenuta tramite API key
    # In questo caso, request.auth è True ma request.user è None
    if request.auth is True and request.user is None:
        # Autenticazione tramite API key
        logger.info("Getting all workspaces for API key authentication")
        workspaces = Workspace.objects.select_related(
            'sql_db__vector_db',
            'default_agent__ai_model__basic_model',
            'question_validator__ai_model__basic_model',
            'kw_sel_agent_1__ai_model__basic_model',
            'kw_sel_agent_2__ai_model__basic_model',
            'sel_columns_agent_1__ai_model__basic_model',
            'sel_columns_agent_2__ai_model__basic_model',
            'sql_basic_agent__ai_model__basic_model',
            'sql_advanced_agent__ai_model__basic_model',
            'sql_expert_agent__ai_model__basic_model',
            'sql_titan_agent_1__ai_model__basic_model',
            'sql_titan_agent_2__ai_model__basic_model',
            'sql_titan_agent_3__ai_model__basic_model',
            'sql_titan_agent_3__ai_model__basic_model',
            'test_gen_agent_1__ai_model__basic_model',
            'test_gen_agent_2__ai_model__basic_model',
            'test_exec_agent__ai_model__basic_model',
            'explain_sql_agent__ai_model__basic_model',
            'ask_human_help_agent__ai_model__basic_model',
            'explanation_ai_model__basic_model',
            'plotly_generator_ai_model__basic_model',
            'setting__comment_model__basic_model'
        ).prefetch_related('users', 'default_workspace').all()
    elif hasattr(request, 'user') and request.user is not None and request.user.is_authenticated:
        # Autenticazione tramite token o sessione
        user = request.user
        logger.info(f"Getting workspaces for authenticated user {user.username}")
        # Recupera tutti i Workspaces associati all'utente con ottimizzazione delle query
        workspaces = Workspace.objects.filter(users=user).select_related(
            'sql_db__vector_db',
            'default_agent__ai_model__basic_model',
            'question_validator__ai_model__basic_model',
            'kw_sel_agent_1__ai_model__basic_model',
            'kw_sel_agent_2__ai_model__basic_model',
            'sel_columns_agent_1__ai_model__basic_model',
            'sel_columns_agent_2__ai_model__basic_model',
            'sql_basic_agent__ai_model__basic_model',
            'sql_advanced_agent__ai_model__basic_model',
            'sql_expert_agent__ai_model__basic_model',
            'sql_titan_agent_1__ai_model__basic_model',
            'sql_titan_agent_2__ai_model__basic_model',
            'test_gen_agent_1__ai_model__basic_model',
            'test_gen_agent_2__ai_model__basic_model',
            'test_exec_agent__ai_model__basic_model',
            'explain_sql_agent__ai_model__basic_model',
            'ask_human_help_agent__ai_model__basic_model',
            'explanation_ai_model__basic_model',
            'plotly_generator_ai_model__basic_model',
            'setting__comment_model__basic_model'
        ).prefetch_related('users', 'default_workspace')
    else:
        # Caso non previsto, log per debug
        logger.warning("Unexpected authentication state in get_user_workspaces")
        return Response({"error": "Authentication failed"}, status=status.HTTP_401_UNAUTHORIZED)
    
    logger.info(f"Found {len(workspaces)} workspaces")
    # Serializza i dati dei Workspaces
    serializer = WorkspaceSerializer(workspaces, many=True)
    
    # Restituisci i dati serializzati (headers anti-cache gestiti dal middleware)
    return Response(serializer.data, status=status.HTTP_200_OK)

class TableListByDbNameView(APIView):
    authentication_classes = [TokenAuthentication, ApiKeyAuthentication]
    permission_classes = [IsAuthenticated | HasValidApiKey]

    def get(self, request, db_name):
        try:
            db_to_use = None
            if hasattr(request, 'current_workspace') and request.current_workspace and request.current_workspace.sql_db:
                # If a workspace is active and has an SQL DB, prioritize it.
                # Optionally, validate if db_name from URL matches the workspace's DB.
                if request.current_workspace.sql_db.name == db_name:
                    db_to_use = request.current_workspace.sql_db
                else:
                    # Workspace DB and URL db_name mismatch. Decide behavior:
                    # 1. Error out:
                    # return Response({"error": f"URL db_name '{db_name}' does not match active workspace DB '{request.current_workspace.sql_db.name}'."}, status=status.HTTP_400_BAD_REQUEST)
                    # 2. Or, fall back to using db_name from URL (less secure if workspace context is implied)
                    # For now, let's assume if workspace is set, its DB must match or it's an error to use this endpoint with a different db_name.
                    # If strict workspace scoping is desired, uncomment the error above and remove fallback.
                    # Fallback for now if names don't match but workspace is active:
                    logger.warning(f"TableListByDbNameView: URL db_name '{db_name}' does not match active workspace DB '{request.current_workspace.sql_db.name}'. Falling back to URL db_name.")
                    db_to_use = SqlDb.objects.get(name=db_name)

            else:
                # No active workspace with an SQL DB, fall back to db_name from URL.
                db_to_use = SqlDb.objects.get(name=db_name)

            if not db_to_use: # Should not happen if logic above is correct, but as a safeguard.
                 return Response({"error": "Database could not be determined."}, status=status.HTTP_404_NOT_FOUND)

            tables = SqlTable.objects.filter(sql_db=db_to_use)
            serializer = SqlTableSerializer(tables, many=True)
            
            # Headers anti-cache gestiti dal middleware
            return Response(serializer.data)
        except SqlDb.DoesNotExist:
            return Response({"error": f"Database '{db_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
        
class TableColumnsDetailView(APIView):
    authentication_classes = [ApiKeyAuthentication, SessionAuthentication, TokenAuthentication]
    permission_classes = [IsAuthenticated | HasValidApiKey]

    def get(self, request, db_name, table_name):
        try:
            db_to_use = None
            if hasattr(request, 'current_workspace') and request.current_workspace and request.current_workspace.sql_db:
                # If a workspace is active and has an SQL DB, prioritize it.
                if request.current_workspace.sql_db.name == db_name:
                    db_to_use = request.current_workspace.sql_db
                else:
                    # Workspace DB and URL db_name mismatch.
                    logger.warning(f"TableColumnsDetailView: URL db_name '{db_name}' does not match active workspace DB '{request.current_workspace.sql_db.name}'. Falling back to URL db_name.")
                    db_to_use = SqlDb.objects.get(name=db_name) # Fallback or error, as per policy
            else:
                # No active workspace with an SQL DB, fall back to db_name from URL.
                db_to_use = SqlDb.objects.get(name=db_name)

            if not db_to_use:
                return Response({"error": "Database could not be determined."}, status=status.HTTP_404_NOT_FOUND)

            table = SqlTable.objects.get(sql_db=db_to_use, name=table_name)
            columns = SqlColumn.objects.filter(sql_table=table)
            
            serializer = SqlColumnSerializer(columns, many=True)
            
            # Headers anti-cache gestiti dal middleware
            return Response(serializer.data)
        except SqlDb.DoesNotExist:
            return Response({"error": f"Database '{db_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
        except SqlTable.DoesNotExist:
            return Response({"error": f"Table '{table_name}' not found in database '{db_name}'."}, status=status.HTTP_404_NOT_FOUND)
