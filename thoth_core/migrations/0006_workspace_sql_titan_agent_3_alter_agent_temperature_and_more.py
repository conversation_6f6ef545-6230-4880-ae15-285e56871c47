# Generated by Django 5.2 on 2025-07-21 14:41

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('thoth_core', '0005_workspace_explain_sql_agent_alter_agent_agent_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='workspace',
            name='sql_titan_agent_3',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='workspaces_sql_titan_3', to='thoth_core.agent'),
        ),
        migrations.AlterField(
            model_name='agent',
            name='temperature',
            field=models.DecimalField(decimal_places=2, default=0.8, help_text='\n        What sampling temperature to use, between 0 and 2. \n        Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. \n        We generally recommend altering this or top_p, not both simultaneously.\n        ', max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(2)], verbose_name='Temperature'),
        ),
        migrations.AlterField(
            model_name='agent',
            name='timeout',
            field=models.FloatField(default=45.0, help_text='\n        Timeouts take place if this threshold, expressed in seconds, is exceeded:\n        ', validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(3600.0)], verbose_name='Timeout'),
        ),
        migrations.AlterField(
            model_name='aimodel',
            name='temperature',
            field=models.DecimalField(decimal_places=2, default=0.8, help_text='\n        What sampling temperature to use, between 0 and 2. \n        Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. \n        We generally recommend altering this or top_p, not both simultaneously.\n        ', max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(2)], verbose_name='Temperature'),
        ),
        migrations.AlterField(
            model_name='aimodel',
            name='timeout',
            field=models.FloatField(default=45.0, help_text='\n        Timeouts take place if this threshold, expressed in seconds, is exceeded:', validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(3600.0)], verbose_name='Timeout'),
        ),
    ]
