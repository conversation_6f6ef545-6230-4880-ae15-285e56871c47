"""
Keyword extraction using OpenRouter's Gemini model for SQL context.
"""

import requests
import json
from typing import List, Dict, Any
import time


class KeywordExtractor:
    """Extracts SQL-relevant keywords from natural language questions."""
    
    def __init__(self, api_key: str, api_base: str = "https://openrouter.ai/api/v1"):
        """Initialize keyword extractor with OpenRouter credentials."""
        self.api_key = api_key
        self.api_base = api_base
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:8000",
            "X-Title": "LSH Evaluation Tool"
        }
    
    def extract_keywords(self, question: str) -> List[str]:
        """
        Extract SQL-relevant keywords from a natural language question.
        
        Args:
            question: Natural language question about the database
            
        Returns:
            List of extracted keywords for LSH search
        """
        prompt = f"""You are a SQL query assistant. Extract the most relevant keywords from the following question that would be useful for finding similar database columns and values. Focus on:
1. Column names (e.g., "student", "school", "grade")
2. Data values or concepts (e.g., "test scores", "attendance", "demographics")
3. Domain-specific terms (e.g., "education", "performance", "enrollment")

Return ONLY a JSON array of keywords, no additional text or formatting.

Question: {question}

Keywords:"""

        try:
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "google/gemini-flash-1.5",
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.3,
                    "max_tokens": 200
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                # Clean the content - remove markdown formatting
                content = content.replace('```json', '').replace('```', '').strip()
                
                # Parse JSON response
                try:
                    keywords = json.loads(content)
                    if isinstance(keywords, list):
                        # Clean and deduplicate keywords
                        cleaned_keywords = []
                        for kw in keywords:
                            if isinstance(kw, str) and kw.strip():
                                cleaned = kw.strip().lower().replace('"', '')
                                if cleaned and cleaned not in cleaned_keywords:
                                    cleaned_keywords.append(cleaned)
                        return cleaned_keywords
                except json.JSONDecodeError:
                    # Try to extract keywords from malformed JSON
                    import re
                    # Look for quoted strings
                    matches = re.findall(r'"([^"]+)"', content)
                    if matches:
                        cleaned_keywords = []
                        for match in matches:
                            cleaned = match.strip().lower()
                            if cleaned and cleaned not in cleaned_keywords:
                                cleaned_keywords.append(cleaned)
                        return cleaned_keywords
                    
                    # Final fallback: split by commas
                    keywords = [kw.strip().lower().replace('"', '') for kw in content.split(',') if kw.strip()]
                    return list(set(keywords))
            
            else:
                print(f"API request failed with status {response.status_code}: {response.text}")
                return self._fallback_extraction(question)
                
        except Exception as e:
            print(f"Error extracting keywords: {e}")
            return self._fallback_extraction(question)
    
    def _fallback_extraction(self, question: str) -> List[str]:
        """Fallback keyword extraction using simple heuristics."""
        # Remove common stop words
        stop_words = {
            'what', 'how', 'many', 'much', 'is', 'are', 'the', 'a', 'an', 'in', 'of', 
            'for', 'to', 'from', 'by', 'with', 'on', 'at', 'find', 'show', 'get', 
            'list', 'all', 'any', 'some', 'where', 'which', 'who', 'when', 'why'
        }
        
        # Simple tokenization and cleaning
        import re
        words = re.findall(r'\b[a-zA-Z]+\b', question.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return list(set(keywords))
    
    def test_connection(self) -> bool:
        """Test if the OpenRouter API is accessible."""
        try:
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=self.headers,
                json={
                    "model": "google/gemini-2.5-flash-lite",
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 5
                },
                timeout=10
            )
            return response.status_code == 200
        except:
            return False
