# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

import logging
import os
import uuid
from urllib.parse import urlparse
from vdbmanager.ThothVectorStore import HintDocument

# Assuming the script is run from the project root or PYTHONPATH is set correctly
try:
    from vdbmanager.impl.QdrantHaystackStore import QdrantHaystackStore
except ImportError:
    # Fallback if the above fails, might depend on execution context
    from vdbmanager.impl.QdrantHaystackStore import QdrantHaystackStore


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Qdrant Connection Details ---
QDRANT_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", None)
# --- Collection and Hint Details ---
COLLECTION_NAME = "california_schools-test"
HINT_TEXT = "Hint di test"
# Assign a unique ID for this test run
TEST_HINT_ID = f"test-hint-{uuid.uuid4()}"
# --- Embedding Dimension ---
# IMPORTANT: Adjust this to match your actual Qdrant collection's vector dimension
EMBEDDING_DIM = int(os.getenv("QDRANT_EMBEDDING_DIM", 768))


logging.info(f"--- Starting Qdrant Hint Test Script ---")
logging.info(f"Using Qdrant URL: {QDRANT_URL}")
logging.info(f"Target collection: {COLLECTION_NAME}")
logging.info(f"Using Embedding Dimension: {EMBEDDING_DIM}")
logging.info(f"Test Hint ID: {TEST_HINT_ID}")


try:
    # 1. Initialize the QdrantHaystackStore
    logging.info("Initializing QdrantHaystackStore...")

    # Parse QDRANT_URL
    parsed_url = urlparse(QDRANT_URL)
    qdrant_host = parsed_url.hostname
    # Use default Qdrant port 6333 if not specified in URL
    qdrant_port = parsed_url.port if parsed_url.port else 6333 

    logging.info(f"Parsed Qdrant connection: host='{qdrant_host}', port={qdrant_port}")

    # Check the constructor of your specific QdrantHaystackStore implementation
    # It requires collection, host, and port.
    document_store= QdrantHaystackStore(COLLECTION_NAME, qdrant_host, qdrant_port)
    logging.info("QdrantHaystackStore initialized successfully.")

    # 2. Create the HintDocument with the specific ID
    # Note: HintDocument likely requires 'id' and 'hint' arguments
    hint_document = HintDocument(id=TEST_HINT_ID, hint=HINT_TEXT)
    logging.info(f"Created HintDocument object to write: ID='{hint_document.id}', Hint='{hint_document.hint}'")

    # 3. Write the Document using add_hint
    logging.info(f"Attempting to write hint document with ID '{TEST_HINT_ID}' using add_hint...")
    # The add_hint method takes the HintDocument object
    added_doc_id = document_store.add_hint(hint_document)
    logging.info(f"add_hint operation completed. Returned document ID: {added_doc_id}")

    # Verify write based on the returned ID matching the input ID
    if added_doc_id == TEST_HINT_ID:
        logging.info(f"Successfully wrote hint document with ID: {TEST_HINT_ID}")

        # 4. Read the Document back by first getting all hints, then retrieving the first one by ID
        logging.info("Attempting to retrieve all hint documents...")
        all_hints = document_store.get_all_hint_documents()
        logging.info(f"Found {len(all_hints)} hint documents in the collection.")

        if all_hints:
            first_hint = all_hints[0]
            first_hint_id = first_hint.id
            logging.info(f"Attempting to retrieve the first hint document by its ID: '{first_hint_id}' using get_hint_document_by_id...")
            retrieved_doc = document_store.get_hint_document_by_id(first_hint_id)

            if retrieved_doc:
                logging.info(f"Successfully retrieved hint document matching ID '{first_hint_id}'.")
                # Log details of the retrieved HintDocument
                logging.info(f"Retrieved HintDocument Details: ID='{retrieved_doc.id}', Hint='{retrieved_doc.hint}'")
                # Verify if the retrieved hint matches the one we added (optional check)
                if retrieved_doc.id == TEST_HINT_ID and retrieved_doc.hint == HINT_TEXT:
                    logging.info("Retrieved hint matches the test hint that was added.")
                else:
                    logging.warning("Retrieved hint does NOT match the test hint that was added.")
            else:
                logging.warning(f"Could not retrieve hint document with ID '{first_hint_id}' using get_hint_document_by_id, even though it was listed in all_hints.")
        else:
            logging.warning("Could not find any hint documents in the collection to retrieve.")

    else:
        logging.error(f"Failed to write hint document with ID '{TEST_HINT_ID}'. add_hint returned ID '{added_doc_id}'.")


except ImportError as e:
    logging.error(f"Failed to import QdrantHaystackStore. Check installation and path: {e}", exc_info=True)
except Exception as e:
    logging.error(f"An error occurred during the script execution: {e}", exc_info=True)

finally:
    logging.info(f"--- Qdrant Hint Test Script Finished ---")
