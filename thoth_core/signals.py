# Copyright (c) 2025 <PERSON>
# This file is part of Thoth and is released under the MIT License.
# See the LICENSE.md file in the project root for full license information.

from django.contrib.auth.signals import user_logged_in
from django.dispatch import receiver

@receiver(user_logged_in)
def set_default_workspace_on_login(sender, user, request, **kwargs):
    """
    Dopo il login, imposta nella sessione la prima workspace di default dell'utente (se esiste).
    """
    default_ws = user.default_workspaces.first()
    if default_ws:
        request.session['selected_workspace_id'] = default_ws.pk
